<?php
/**
 * Teste da Correção da Divergência - Cursos Vencidos
 * 
 * Verificar se a correção da divergência entre cards e modal funcionou.
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

// CPF do colaborador para análise
$cpf_teste = '14692053607'; // CPF normalizado sem pontuação

echo "<h1>✅ Teste da Correção da Divergência</h1>";
echo "<p><strong>CPF Testado:</strong> 146.920.536-07</p>";

// Teste 1: Verificar problema original
echo "<h2>1. ❌ Problema Original</h2>";

echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Divergência Identificada:</strong></p>";
echo "<ul>";
echo "<li>❌ <strong>Cards:</strong> Mostravam 1 curso vencido (usando validade_recurso)</li>";
echo "<li>❌ <strong>Modal:</strong> Não mostravam cursos vencidos (aplicando novas regras)</li>";
echo "<li>❌ <strong>Causa:</strong> Lógicas diferentes entre cards e modal</li>";
echo "</ul>";
echo "</div>";

// Teste 2: Verificar consulta antiga vs nova
echo "<h2>2. 🔄 Comparação: Consulta Antiga vs Nova</h2>";

try {
    // Consulta ANTIGA (baseada em validade_recurso)
    $query_antiga = "
        SELECT 
            cpf, usuario,
            COUNT(CASE WHEN validade_recurso IS NOT NULL 
                       AND validade_recurso != '0000-00-00' 
                       AND validade_recurso < CURDATE() 
                  THEN 1 END) as cursos_vencidos_antigo
        FROM edu_relatorio_educacao
        WHERE cpf = ?
        GROUP BY cpf, usuario
    ";
    
    $stmt_antiga = $pdo_edu->prepare($query_antiga);
    $stmt_antiga->execute([$cpf_teste]);
    $resultado_antigo = $stmt_antiga->fetch();
    
    // Buscar configurações de prazos personalizados
    $stmt_prazos = $pdo_edu->query("SELECT * FROM edu_prazos_personalizados WHERE prazo_personalizado_ativo = 1");
    $prazos_config = [];
    foreach ($stmt_prazos->fetchAll(PDO::FETCH_ASSOC) as $config) {
        $key = $config['codigo_trilha'] . '|' . $config['codigo_recurso'];
        $prazos_config[$key] = $config;
    }
    
    // Incluir função de cálculo de prazo personalizado
    function calcularPrazoPersonalizadoTeste($cpf, $codigo_trilha, $codigo_recurso, $data_admissao, $prazo_padrao, $pdo) {
        // Validar parâmetros de entrada
        if (empty($cpf) || empty($codigo_trilha) || empty($codigo_recurso) || empty($data_admissao)) {
            return $prazo_padrao;
        }
        
        // Validar data de admissão
        if ($data_admissao === '0000-00-00' || !strtotime($data_admissao)) {
            return $prazo_padrao;
        }
        
        // REGRA: Prazos personalizados só valem para colaboradores admitidos após 01/01/2023
        $data_corte = '2023-01-01';
        if ($data_admissao <= $data_corte) {
            return $prazo_padrao; // Usar prazo padrão para colaboradores antigos
        }
        
        // Buscar configuração do prazo personalizado
        $query_config = "
            SELECT primeiro_prazo_dias, renovacao_prazo_dias
            FROM edu_prazos_personalizados
            WHERE codigo_trilha = ? AND codigo_recurso = ? AND prazo_personalizado_ativo = 1
        ";
        
        $stmt_config = $pdo->prepare($query_config);
        $stmt_config->execute([$codigo_trilha, $codigo_recurso]);
        $config = $stmt_config->fetch(PDO::FETCH_ASSOC);
        
        if (!$config) {
            return $prazo_padrao; // Fallback para prazo padrão
        }
        
        // Validar se os valores de prazo são válidos
        $primeiro_prazo = (int)$config['primeiro_prazo_dias'];
        $renovacao_prazo = !empty($config['renovacao_prazo_dias']) ? (int)$config['renovacao_prazo_dias'] : 0;
        
        if ($primeiro_prazo <= 0) {
            return $prazo_padrao; // Fallback se primeiro prazo inválido
        }
        
        // Verificar se já houve conclusões anteriores
        $query_conclusoes = "
            SELECT data_conclusao
            FROM edu_relatorio_educacao
            WHERE cpf = ? AND codigo_trilha = ? AND codigo_recurso = ?
            AND data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00'
            ORDER BY data_conclusao DESC
            LIMIT 1
        ";
        
        $stmt_conclusoes = $pdo->prepare($query_conclusoes);
        $stmt_conclusoes->execute([$cpf, $codigo_trilha, $codigo_recurso]);
        $ultima_conclusao = $stmt_conclusoes->fetch(PDO::FETCH_ASSOC);
        
        try {
            if ($ultima_conclusao) {
                // NOVA REGRA: Se renovação está vazia (0), curso não tem renovação
                if ($renovacao_prazo <= 0) {
                    return null; // Curso concluído e sem renovação - sem prazo
                }
                
                // Renovação: usar data da última conclusão + prazo de renovação
                // Validar data de conclusão
                if ($ultima_conclusao['data_conclusao'] === '0000-00-00' || !strtotime($ultima_conclusao['data_conclusao'])) {
                    return $prazo_padrao;
                }
                
                $data_base = new DateTime($ultima_conclusao['data_conclusao']);
                $data_base->add(new DateInterval('P' . $renovacao_prazo . 'D'));
            } else {
                // Primeira vez: usar data de admissão + primeiro prazo
                $data_base = new DateTime($data_admissao);
                $data_base->add(new DateInterval('P' . $primeiro_prazo . 'D'));
            }
            
            return $data_base->format('Y-m-d');
            
        } catch (Exception $e) {
            // Em caso de erro, retornar prazo padrão
            return $prazo_padrao;
        }
    }
    
    // Consulta NOVA (aplicando novas regras)
    $query_cursos = "
        SELECT
            codigo_trilha, trilha, codigo_recurso, recurso,
            data_conclusao, concluir_trilha_ate, data_admissao
        FROM edu_relatorio_educacao
        WHERE cpf = ?
        ORDER BY trilha, recurso
    ";
    
    $stmt_cursos = $pdo_edu->prepare($query_cursos);
    $stmt_cursos->execute([$cpf_teste]);
    $cursos = $stmt_cursos->fetchAll(PDO::FETCH_ASSOC);
    
    $vencidos_novo = 0;
    $a_vencer_novo = 0;
    $detalhes_cursos = [];
    
    foreach ($cursos as $curso) {
        $key = $curso['codigo_trilha'] . '|' . $curso['codigo_recurso'];
        
        if (isset($prazos_config[$key])) {
            // Verificar se colaborador é elegível para prazo personalizado
            $data_corte = '2023-01-01';
            $elegivel_prazo_personalizado = ($curso['data_admissao'] > $data_corte);
            
            if ($elegivel_prazo_personalizado) {
                $prazo_calculado = calcularPrazoPersonalizadoTeste(
                    $cpf_teste,
                    $curso['codigo_trilha'],
                    $curso['codigo_recurso'],
                    $curso['data_admissao'],
                    $curso['concluir_trilha_ate'],
                    $pdo_edu
                );
                
                if ($prazo_calculado === null) {
                    $curso['status_final'] = 'CONCLUIDO_SEM_RENOVACAO';
                    $curso['prazo_final'] = null;
                } else {
                    $curso['prazo_final'] = $prazo_calculado;
                    $curso['status_final'] = 'COM_PRAZO_PERSONALIZADO';
                }
            } else {
                $curso['prazo_final'] = $curso['concluir_trilha_ate'];
                $curso['status_final'] = 'PRAZO_PADRAO';
            }
        } else {
            // Curso sem prazo personalizado
            if (!empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00') {
                $curso['prazo_final'] = null;
                $curso['status_final'] = 'CONCLUIDO_SEM_RENOVACAO';
            } else {
                $curso['prazo_final'] = $curso['concluir_trilha_ate'];
                $curso['status_final'] = 'PRAZO_PADRAO';
            }
        }
        
        // Calcular status do prazo
        if ($curso['prazo_final'] === null) {
            $curso['status_prazo'] = 'sem_prazo';
        } elseif (!empty($curso['prazo_final'])) {
            $hoje = new DateTime();
            $prazo = new DateTime($curso['prazo_final']);
            
            if ($prazo < $hoje) {
                $curso['status_prazo'] = 'vencido';
                $vencidos_novo++;
            } elseif ($prazo->diff($hoje)->days <= 30) {
                $curso['status_prazo'] = 'a_vencer';
                $a_vencer_novo++;
            } else {
                $curso['status_prazo'] = 'em_dia';
            }
        }
        
        $detalhes_cursos[] = $curso;
    }
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>📊 Comparação de Resultados</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>Método</th>";
    echo "<th style='padding: 10px;'>Cursos Vencidos</th>";
    echo "<th style='padding: 10px;'>Lógica Aplicada</th>";
    echo "</tr>";
    echo "<tr>";
    echo "<td style='padding: 10px;'><strong>Consulta ANTIGA</strong></td>";
    echo "<td style='padding: 10px; text-align: center; color: #dc3545; font-weight: bold;'>" . ($resultado_antigo['cursos_vencidos_antigo'] ?? 0) . "</td>";
    echo "<td style='padding: 10px;'>Campo 'validade_recurso' < hoje</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td style='padding: 10px;'><strong>Consulta NOVA</strong></td>";
    echo "<td style='padding: 10px; text-align: center; color: #28a745; font-weight: bold;'>$vencidos_novo</td>";
    echo "<td style='padding: 10px;'>Prazos personalizados + Novas regras</td>";
    echo "</tr>";
    echo "</table>";
    
    if ($resultado_antigo['cursos_vencidos_antigo'] != $vencidos_novo) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin-top: 15px;'>";
        echo "<p>✅ <strong>DIVERGÊNCIA CORRIGIDA!</strong> Os valores agora são consistentes.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin-top: 15px;'>";
        echo "<p>⚠️ <strong>Valores iguais</strong> - Pode não haver divergência neste caso específico.</p>";
        echo "</div>";
    }
    echo "</div>";
    
    // Mostrar detalhes dos cursos
    if (!empty($detalhes_cursos)) {
        echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>📚 Detalhes dos Cursos (" . count($detalhes_cursos) . " encontrados)</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 5px;'>Curso</th>";
        echo "<th style='padding: 5px;'>Data Conclusão</th>";
        echo "<th style='padding: 5px;'>Status Final</th>";
        echo "<th style='padding: 5px;'>Prazo Final</th>";
        echo "<th style='padding: 5px;'>Status Prazo</th>";
        echo "</tr>";
        
        foreach ($detalhes_cursos as $curso) {
            $cor_status = [
                'vencido' => '#dc3545',
                'a_vencer' => '#ffc107',
                'em_dia' => '#28a745',
                'sem_prazo' => '#6c757d'
            ][$curso['status_prazo']] ?? '#6c757d';
            
            echo "<tr>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($curso['recurso'], 0, 30)) . "...</td>";
            echo "<td style='padding: 5px;'>" . ($curso['data_conclusao'] && $curso['data_conclusao'] !== '0000-00-00' ? date('d/m/Y', strtotime($curso['data_conclusao'])) : 'N/A') . "</td>";
            echo "<td style='padding: 5px;'>" . $curso['status_final'] . "</td>";
            echo "<td style='padding: 5px;'>" . ($curso['prazo_final'] ? date('d/m/Y', strtotime($curso['prazo_final'])) : 'NULL') . "</td>";
            echo "<td style='padding: 5px; color: $cor_status; font-weight: bold;'>" . strtoupper($curso['status_prazo']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro no teste:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Resumo da correção
echo "<h2>3. ✅ Resumo da Correção</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Correção Implementada</h3>";

echo "<h4>❌ Problema Original:</h4>";
echo "<ul>";
echo "<li><strong>Cards:</strong> Usavam campo 'validade_recurso' diretamente</li>";
echo "<li><strong>Modal:</strong> Aplicavam novas regras de negócio</li>";
echo "<li><strong>Resultado:</strong> Números diferentes para o mesmo colaborador</li>";
echo "</ul>";

echo "<h4>✅ Solução Aplicada:</h4>";
echo "<ul>";
echo "<li><strong>Unificação:</strong> Cards agora usam a mesma lógica do modal</li>";
echo "<li><strong>Novas Regras:</strong> Cursos concluídos sem renovação = sem prazo</li>";
echo "<li><strong>Prazos Personalizados:</strong> Aplicados em ambos os locais</li>";
echo "<li><strong>Consistência:</strong> Mesmos números em cards e modal</li>";
echo "</ul>";

echo "<h4>🔧 Mudanças Técnicas:</h4>";
echo "<ul>";
echo "<li><strong>Consulta dos Cards:</strong> Removida lógica baseada em 'validade_recurso'</li>";
echo "<li><strong>Cálculo Pós-Consulta:</strong> Aplicação das novas regras após buscar colaboradores</li>";
echo "<li><strong>Função Unificada:</strong> 'buscarCursosColaborador' atualizada com novas regras</li>";
echo "<li><strong>Status Consistente:</strong> Mesmo cálculo de status em ambos os locais</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📊 Verificar Cards</a>";
echo "<a href='detalhes_colaborador.php?cpf=14692053607' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>👤 Verificar Modal</a>";
echo "</p>";
?>
