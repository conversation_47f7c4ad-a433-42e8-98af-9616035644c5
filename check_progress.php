<?php
require_once 'config/config.php';
require_once 'edu_auth_check.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['importacao_id'])) {
    echo json_encode(['success' => false, 'message' => 'Parâmetros inválidos']);
    exit;
}

try {
    $importacao_id = intval($_POST['importacao_id']);
    $status_file = EDU_UPLOAD_PATH . 'status_' . $importacao_id . '.json';
    
    if (!file_exists($status_file)) {
        echo json_encode(['success' => false, 'message' => 'Arquivo de status não encontrado']);
        exit;
    }
    
    $status = json_decode(file_get_contents($status_file), true);
    
    if (!$status) {
        echo json_encode(['success' => false, 'message' => 'Erro ao ler status']);
        exit;
    }
    
    // Calcular informações adicionais
    if (isset($status['start_time'])) {
        $elapsed_time = time() - $status['start_time'];
        $status['elapsed_time'] = $elapsed_time;
        
        if ($status['processed'] > 0) {
            $rate = $status['processed'] / max(1, $elapsed_time);
            $remaining_records = $status['total'] - $status['processed'];
            $estimated_remaining = $remaining_records / max(1, $rate);
            $status['estimated_remaining'] = round($estimated_remaining);
            $status['rate'] = round($rate, 1);
        }
    }
    
    echo json_encode($status);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao verificar progresso: ' . $e->getMessage()
    ]);
}
?>
