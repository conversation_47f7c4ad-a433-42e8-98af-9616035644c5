<?php
// Relatório por PA/Agência
echo '<table border="1" style="border-collapse: collapse;">';
echo '<tr style="background-color: #17a2b8; color: white; font-weight: bold;">';
echo '<td colspan="12" style="padding: 10px; text-align: center; font-size: 16px;">';
echo 'RELATÓRIO POR PA/AGÊNCIA';
echo '</td>';
echo '</tr>';

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td style="padding: 8px;">Gerado em:</td>';
echo '<td colspan="11" style="padding: 8px;">' . date('d/m/Y H:i:s') . '</td>';
echo '</tr>';

// Buscar todos os colaboradores
$colaboradores_query = "
    SELECT DISTINCT cpf, usuario, funcao
    FROM edu_relatorio_educacao
    ORDER BY usuario
";
$todos_colaboradores = $pdo_edu->query($colaboradores_query)->fetchAll();

// Agrupar por PA
$pas_dados = [];

foreach ($todos_colaboradores as $colaborador) {
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;
    
    $pa_key = 'Sem PA';
    $pa_nome = 'Sem PA Definido';
    
    if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
        $agencia_id = $usuario_intranet['agencia'];
        if (isset($mapa_agencias[$agencia_id])) {
            $agencia_data = $mapa_agencias[$agencia_id];
            $pa_key = $agencia_id;
            $pa_nome = $agencia_data['numero'] . ' - ' . $agencia_data['nome'];
        } else {
            $pa_key = $agencia_id;
            $pa_nome = 'PA ' . $agencia_id;
        }
    }
    
    if (!isset($pas_dados[$pa_key])) {
        $pas_dados[$pa_key] = [
            'nome' => $pa_nome,
            'colaboradores' => 0,
            'aprovados' => 0,
            'vencidos' => 0,
            'a_vencer' => 0,
            'em_andamento' => 0,
            'em_dia' => 0,
            'total_cursos' => 0
        ];
    }
    
    $pas_dados[$pa_key]['colaboradores']++;
    
    // Buscar cursos do colaborador
    $cursos_colaborador = buscarCursosColaborador($colaborador['cpf'], $pdo_edu, $prazos_config);
    
    $tem_vencido = false;
    $tem_a_vencer = false;
    $tem_em_andamento = false;
    $cursos_aprovados_colab = 0;
    
    foreach ($cursos_colaborador as $curso) {
        $pas_dados[$pa_key]['total_cursos']++;
        
        if ($curso['aprovacao'] === 'Sim') {
            $cursos_aprovados_colab++;
        } elseif ($curso['status_prazo'] === 'vencido') {
            $tem_vencido = true;
        } elseif ($curso['status_prazo'] === 'a_vencer') {
            $tem_a_vencer = true;
        } elseif (!empty($curso['andamento_etapa']) && $curso['aprovacao'] !== 'Sim') {
            $tem_em_andamento = true;
        }
    }
    
    $pas_dados[$pa_key]['aprovados'] += $cursos_aprovados_colab;
    
    if ($tem_vencido) {
        $pas_dados[$pa_key]['vencidos']++;
    } elseif ($tem_a_vencer) {
        $pas_dados[$pa_key]['a_vencer']++;
    } elseif ($tem_em_andamento) {
        $pas_dados[$pa_key]['em_andamento']++;
    } else {
        $pas_dados[$pa_key]['em_dia']++;
    }
}

// Ordenar por número de colaboradores
uasort($pas_dados, function($a, $b) {
    return $b['colaboradores'] - $a['colaboradores'];
});

// Cabeçalhos
echo '<tr style="background-color: #17a2b8; color: white; font-weight: bold;">';
echo '<td style="padding: 8px;">PA/Agência</td>';
echo '<td style="padding: 8px; text-align: center;">Colaboradores</td>';
echo '<td style="padding: 8px; text-align: center;">Total Cursos</td>';
echo '<td style="padding: 8px; text-align: center;">Aprovados</td>';
echo '<td style="padding: 8px; text-align: center;">% Aprovação</td>';
echo '<td style="padding: 8px; text-align: center;">Em Dia</td>';
echo '<td style="padding: 8px; text-align: center;">Em Andamento</td>';
echo '<td style="padding: 8px; text-align: center;">A Vencer</td>';
echo '<td style="padding: 8px; text-align: center;">Vencidos</td>';
echo '<td style="padding: 8px; text-align: center;">% Em Dia</td>';
echo '<td style="padding: 8px; text-align: center;">% Problemas</td>';
echo '<td style="padding: 8px; text-align: center;">Status</td>';
echo '</tr>';

// Dados
foreach ($pas_dados as $pa_key => $dados) {
    $perc_aprovacao = $dados['total_cursos'] > 0 ? ($dados['aprovados'] / $dados['total_cursos']) * 100 : 0;
    $perc_em_dia = $dados['colaboradores'] > 0 ? ($dados['em_dia'] / $dados['colaboradores']) * 100 : 0;
    $perc_problemas = $dados['colaboradores'] > 0 ? (($dados['vencidos'] + $dados['a_vencer']) / $dados['colaboradores']) * 100 : 0;
    
    $status = 'Excelente';
    $cor_status = '#28a745';
    
    if ($perc_em_dia < 60 || $perc_problemas > 30) {
        $status = 'Crítico';
        $cor_status = '#dc3545';
    } elseif ($perc_em_dia < 80 || $perc_problemas > 15) {
        $status = 'Regular';
        $cor_status = '#fd7e14';
    } elseif ($perc_em_dia < 90 || $perc_problemas > 5) {
        $status = 'Bom';
        $cor_status = '#007bff';
    }
    
    echo '<tr>';
    echo '<td style="padding: 6px;">' . htmlspecialchars($dados['nome']) . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . $dados['colaboradores'] . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . $dados['total_cursos'] . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . $dados['aprovados'] . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . number_format($perc_aprovacao, 1) . '%</td>';
    echo '<td style="padding: 6px; text-align: center; color: #28a745;">' . $dados['em_dia'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; color: #007bff;">' . $dados['em_andamento'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; color: #fd7e14;">' . $dados['a_vencer'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; color: #dc3545;">' . $dados['vencidos'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; font-weight: bold;">' . number_format($perc_em_dia, 1) . '%</td>';
    echo '<td style="padding: 6px; text-align: center; font-weight: bold;">' . number_format($perc_problemas, 1) . '%</td>';
    echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: ' . $cor_status . ';">' . $status . '</td>';
    echo '</tr>';
}

echo '</table>';
?>
