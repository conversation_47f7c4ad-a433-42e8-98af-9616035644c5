<?php
require_once 'config/config.php';
require_once 'edu_auth_check.php';
require_once 'config/database.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

$message = '';
$message_type = '';
$installation_status = [];

// Função para verificar se uma tabela existe
function tableExists($pdo, $tableName) {
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name = ?
        ");
        $stmt->execute([$tableName]);
        return $stmt->fetchColumn() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// Verificar status das tabelas
$installation_status['edu_relatorio_educacao'] = tableExists($pdo_edu, 'edu_relatorio_educacao');
$installation_status['edu_importacoes'] = tableExists($pdo_edu, 'edu_importacoes');

// Processar instalação
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['install'])) {
    try {
        $pdo_edu->beginTransaction();
        
        // Ler e executar script de instalação
        $sql_file = __DIR__ . '/sql/install.sql';
        if (!file_exists($sql_file)) {
            throw new Exception('Arquivo de instalação não encontrado: ' . $sql_file);
        }
        
        $sql_content = file_get_contents($sql_file);
        if ($sql_content === false) {
            throw new Exception('Erro ao ler arquivo de instalação.');
        }
        
        // Dividir em comandos individuais
        $commands = explode(';', $sql_content);
        
        foreach ($commands as $command) {
            $command = trim($command);
            if (!empty($command) && !preg_match('/^(--|USE|SELECT)/i', $command)) {
                $pdo_edu->exec($command);
            }
        }
        
        $pdo_edu->commit();
        
        // Atualizar status
        $installation_status['edu_relatorio_educacao'] = tableExists($pdo_edu, 'edu_relatorio_educacao');
        $installation_status['edu_importacoes'] = tableExists($pdo_edu, 'edu_importacoes');
        
        $message = 'Instalação concluída com sucesso! Todas as tabelas foram criadas.';
        $message_type = 'success';
        
    } catch (Exception $e) {
        if ($pdo_edu->inTransaction()) {
            $pdo_edu->rollBack();
        }
        
        $message = 'Erro durante a instalação: ' . $e->getMessage();
        $message_type = 'danger';
    }
}

// Verificar se a instalação está completa
$installation_complete = array_reduce($installation_status, function($carry, $status) {
    return $carry && $status;
}, true);
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instalação - <?php echo EDU_PROJECT_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;
            --sicoob-roxo: #49479D;
            --sicoob-branco: #FFFFFF;
            --sicoob-cinza: #58595B;
        }

        body {
            background-color: #f8f9fa;
        }

        .navbar {
            background-color: var(--sicoob-verde-escuro) !important;
            box-shadow: 0 2px 8px rgba(0, 54, 65, 0.2);
        }

        .navbar-brand, .navbar-nav .nav-link {
            color: var(--sicoob-branco) !important;
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: var(--sicoob-branco);
            border-radius: 12px 12px 0 0 !important;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
        }

        .btn-success {
            background-color: var(--sicoob-verde-claro);
            border-color: var(--sicoob-verde-claro);
            color: var(--sicoob-verde-escuro);
            font-weight: 600;
        }

        .status-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .status-success {
            background-color: #28a745;
        }

        .status-error {
            background-color: #dc3545;
        }

        .installation-item {
            padding: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            background-color: white;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap me-2"></i>
                <?php echo EDU_PROJECT_NAME; ?>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../../logout.php">
                    <i class="fas fa-sign-out-alt me-1"></i> Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Mensagens -->
        <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Status da Instalação -->
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i> Instalação do Sistema
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if ($installation_complete): ?>
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Sistema Instalado</h6>
                            <p class="mb-0">Todas as tabelas foram criadas com sucesso. O sistema está pronto para uso.</p>
                        </div>
                        <?php else: ?>
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Instalação Necessária</h6>
                            <p class="mb-0">Algumas tabelas ainda não foram criadas. Execute a instalação para configurar o sistema.</p>
                        </div>
                        <?php endif; ?>

                        <h6 class="mt-4 mb-3">Status das Tabelas:</h6>
                        
                        <div class="installation-item">
                            <div class="d-flex align-items-center">
                                <span class="status-icon <?php echo $installation_status['edu_relatorio_educacao'] ? 'status-success' : 'status-error'; ?>">
                                    <i class="fas <?php echo $installation_status['edu_relatorio_educacao'] ? 'fa-check' : 'fa-times'; ?>"></i>
                                </span>
                                <div class="ms-3">
                                    <strong>edu_relatorio_educacao</strong>
                                    <div class="text-muted small">Tabela principal para dados de educação corporativa</div>
                                </div>
                            </div>
                        </div>

                        <div class="installation-item">
                            <div class="d-flex align-items-center">
                                <span class="status-icon <?php echo $installation_status['edu_importacoes'] ? 'status-success' : 'status-error'; ?>">
                                    <i class="fas <?php echo $installation_status['edu_importacoes'] ? 'fa-check' : 'fa-times'; ?>"></i>
                                </span>
                                <div class="ms-3">
                                    <strong>edu_importacoes</strong>
                                    <div class="text-muted small">Tabela de controle de importações</div>
                                </div>
                            </div>
                        </div>

                        <?php if (!$installation_complete): ?>
                        <div class="mt-4">
                            <form method="POST">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="index.php" class="btn btn-secondary me-md-2">
                                        <i class="fas fa-arrow-left me-2"></i>Voltar
                                    </a>
                                    <button type="submit" name="install" class="btn btn-primary">
                                        <i class="fas fa-play me-2"></i>Executar Instalação
                                    </button>
                                </div>
                            </form>
                        </div>
                        <?php else: ?>
                        <div class="mt-4">
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="index.php" class="btn btn-success">
                                    <i class="fas fa-home me-2"></i>Ir para o Sistema
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informações Adicionais -->
        <div class="row mt-4">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i> Informações do Sistema
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Versão:</strong> <?php echo EDU_PROJECT_VERSION; ?><br>
                                <strong>Banco de Dados:</strong> <?php echo DB_NAME; ?><br>
                                <strong>Usuário:</strong> <?php echo htmlspecialchars($user['nome_completo']); ?>
                            </div>
                            <div class="col-md-6">
                                <strong>Diretório de Upload:</strong> <?php echo EDU_UPLOAD_PATH; ?><br>
                                <strong>Diretório de Logs:</strong> <?php echo EDU_LOG_PATH; ?><br>
                                <strong>Tamanho Máximo:</strong> <?php echo EDU_MAX_FILE_SIZE / 1024 / 1024; ?>MB
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
