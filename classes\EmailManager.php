<?php
/**
 * Gerenciador de E-mails para Educação Corporativa
 * 
 * Classe responsável por gerenciar envios individuais, em massa e agendados
 * de e-mails para colaboradores com cursos a vencer ou vencidos.
 */

require_once __DIR__ . '/../../../config/smtp.php';
require_once __DIR__ . '/../config/database.php';

class EmailManager {
    private $pdo;
    private $prazos_config;
    
    public function __construct() {
        global $pdo_edu;
        $this->pdo = $pdo_edu;
        $this->carregarPrazosPersonalizados();
    }
    
    /**
     * Carrega configurações de prazos personalizados
     */
    private function carregarPrazosPersonalizados() {
        $stmt = $this->pdo->query("SELECT * FROM edu_prazos_personalizados WHERE prazo_personalizado_ativo = 1");
        $this->prazos_config = [];
        foreach ($stmt->fetchAll(PDO::FETCH_ASSOC) as $config) {
            $key = $config['codigo_trilha'] . '|' . $config['codigo_recurso'];
            $this->prazos_config[$key] = $config;
        }
    }
    
    /**
     * Busca colaboradores com cursos a vencer
     */
    public function buscarColaboradoresAVencer($dias_limite = 30, $filtros = []) {
        $where_conditions = ["1=1"];
        $params = [];

        // Aplicar filtros se fornecidos
        if (!empty($filtros['pa'])) {
            $where_conditions[] = "codigo_unidade LIKE ?";
            $params[] = '%' . $filtros['pa'] . '%';
        }

        if (!empty($filtros['funcao'])) {
            $where_conditions[] = "funcao = ?";
            $params[] = $filtros['funcao'];
        }

        if (!empty($filtros['trilha'])) {
            $where_conditions[] = "trilha = ?";
            $params[] = $filtros['trilha'];
        }

        // Filtro por trilhas obrigatórias (com verificação se a tabela existe)
        if (!empty($filtros['trilha_obrigatoria'])) {
            try {
                // Verificar se a tabela existe
                $check_table = $this->pdo->query("SHOW TABLES LIKE 'edu_trilhas_obrigatorias'");
                if ($check_table->rowCount() > 0) {
                    if ($filtros['trilha_obrigatoria'] === 'sim') {
                        $where_conditions[] = "codigo_trilha IN (SELECT codigo_trilha FROM edu_trilhas_obrigatorias WHERE obrigatoria = 1)";
                    } elseif ($filtros['trilha_obrigatoria'] === 'nao') {
                        $where_conditions[] = "(codigo_trilha NOT IN (SELECT codigo_trilha FROM edu_trilhas_obrigatorias WHERE obrigatoria = 1) OR codigo_trilha NOT IN (SELECT codigo_trilha FROM edu_trilhas_obrigatorias))";
                    }
                }
            } catch (Exception $e) {
                // Se houver erro, ignorar o filtro de trilhas obrigatórias
                error_log("Erro ao aplicar filtro de trilhas obrigatórias: " . $e->getMessage());
            }
        }

        $query = "
            SELECT
                cpf,
                MAX(usuario) as nome,
                MAX(email) as email,
                MAX(funcao) as funcao,
                MAX(codigo_unidade) as codigo_unidade,
                COUNT(DISTINCT CASE WHEN concluir_trilha_ate BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY)
                                   AND (aprovacao != 'Sim' OR aprovacao IS NULL)
                                   THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_a_vencer
            FROM edu_relatorio_educacao
            WHERE " . implode(" AND ", $where_conditions) . "
            GROUP BY cpf
            HAVING cursos_a_vencer > 0
            ORDER BY cursos_a_vencer DESC, MAX(usuario)
        ";

        $stmt = $this->pdo->prepare($query);
        $stmt->execute(array_merge([$dias_limite], $params));
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Busca detalhes dos cursos a vencer para um colaborador específico
     */
    public function buscarCursosAVencerDetalhado($cpf, $dias_limite = 30) {
        $stmt = $this->pdo->prepare("
            SELECT
                trilha,
                recurso,
                concluir_trilha_ate,
                DATEDIFF(concluir_trilha_ate, CURDATE()) as dias_restantes
            FROM edu_relatorio_educacao
            WHERE cpf = ?
            AND concluir_trilha_ate BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY)
            AND (aprovacao != 'Sim' OR aprovacao IS NULL)
            ORDER BY concluir_trilha_ate ASC
        ");
        $stmt->execute([$cpf, $dias_limite]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Busca colaboradores com cursos vencidos
     */
    public function buscarColaboradoresVencidos($filtros = []) {
        $where_conditions = ["1=1"];
        $params = [];

        // Aplicar filtros se fornecidos
        if (!empty($filtros['pa'])) {
            $where_conditions[] = "codigo_unidade LIKE ?";
            $params[] = '%' . $filtros['pa'] . '%';
        }

        if (!empty($filtros['funcao'])) {
            $where_conditions[] = "funcao = ?";
            $params[] = $filtros['funcao'];
        }

        if (!empty($filtros['trilha'])) {
            $where_conditions[] = "trilha = ?";
            $params[] = $filtros['trilha'];
        }

        // Filtro por trilhas obrigatórias (com verificação se a tabela existe)
        if (!empty($filtros['trilha_obrigatoria'])) {
            try {
                // Verificar se a tabela existe
                $check_table = $this->pdo->query("SHOW TABLES LIKE 'edu_trilhas_obrigatorias'");
                if ($check_table->rowCount() > 0) {
                    if ($filtros['trilha_obrigatoria'] === 'sim') {
                        $where_conditions[] = "codigo_trilha IN (SELECT codigo_trilha FROM edu_trilhas_obrigatorias WHERE obrigatoria = 1)";
                    } elseif ($filtros['trilha_obrigatoria'] === 'nao') {
                        $where_conditions[] = "(codigo_trilha NOT IN (SELECT codigo_trilha FROM edu_trilhas_obrigatorias WHERE obrigatoria = 1) OR codigo_trilha NOT IN (SELECT codigo_trilha FROM edu_trilhas_obrigatorias))";
                    }
                }
            } catch (Exception $e) {
                // Se houver erro, ignorar o filtro de trilhas obrigatórias
                error_log("Erro ao aplicar filtro de trilhas obrigatórias: " . $e->getMessage());
            }
        }

        $query = "
            SELECT
                cpf,
                MAX(usuario) as nome,
                MAX(email) as email,
                MAX(funcao) as funcao,
                MAX(codigo_unidade) as codigo_unidade,
                COUNT(DISTINCT CASE WHEN concluir_trilha_ate < CURDATE()
                                   AND (aprovacao != 'Sim' OR aprovacao IS NULL)
                                   THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_vencidos
            FROM edu_relatorio_educacao
            WHERE " . implode(" AND ", $where_conditions) . "
            GROUP BY cpf
            HAVING cursos_vencidos > 0
            ORDER BY cursos_vencidos DESC, MAX(usuario)
        ";

        $stmt = $this->pdo->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Busca detalhes dos cursos vencidos para um colaborador específico
     */
    public function buscarCursosVencidosDetalhado($cpf) {
        $stmt = $this->pdo->prepare("
            SELECT
                trilha,
                recurso,
                concluir_trilha_ate,
                DATEDIFF(CURDATE(), concluir_trilha_ate) as dias_vencidos
            FROM edu_relatorio_educacao
            WHERE cpf = ?
            AND concluir_trilha_ate < CURDATE()
            AND (aprovacao != 'Sim' OR aprovacao IS NULL)
            ORDER BY concluir_trilha_ate ASC
        ");
        $stmt->execute([$cpf]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Busca template de e-mail por ID
     */
    public function buscarTemplate($template_id) {
        $stmt = $this->pdo->prepare("SELECT * FROM edu_email_templates WHERE id = ? AND ativo = 1");
        $stmt->execute([$template_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Lista todos os templates ativos
     */
    public function listarTemplates($tipo = null) {
        $where = "ativo = 1";
        $params = [];
        
        if ($tipo) {
            $where .= " AND tipo = ?";
            $params[] = $tipo;
        }
        
        $stmt = $this->pdo->prepare("SELECT * FROM edu_email_templates WHERE $where ORDER BY nome");
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Processa variáveis do template
     */
    public function processarVariaveis($template, $colaborador, $dados_extras = []) {
        // Buscar detalhes dos cursos se necessário
        $cpf = $colaborador['cpf'] ?? '';
        $cursosAVencer = [];
        $cursosVencidos = [];

        if (!empty($cpf)) {
            $cursosAVencer = $this->buscarCursosAVencerDetalhado($cpf);
            $cursosVencidos = $this->buscarCursosVencidosDetalhado($cpf);
        }

        // Gerar listas HTML dos cursos
        $listaAVencerHtml = $this->gerarListaCursosHtml($cursosAVencer, 'a_vencer');
        $listaVencidosHtml = $this->gerarListaCursosHtml($cursosVencidos, 'vencidos');

        // Gerar listas de texto dos cursos
        $listaAVencerTexto = $this->gerarListaCursosTexto($cursosAVencer, 'a_vencer');
        $listaVencidosTexto = $this->gerarListaCursosTexto($cursosVencidos, 'vencidos');

        $variaveis = [
            '{{nome_colaborador}}' => $colaborador['nome'] ?? 'Colaborador',
            '{{cpf_colaborador}}' => $this->formatarCpf($colaborador['cpf'] ?? ''),
            '{{email_colaborador}}' => $colaborador['email'] ?? 'N/A',
            '{{funcao_colaborador}}' => $colaborador['funcao'] ?? 'N/A',
            '{{pa_colaborador}}' => $this->extrairPA($colaborador['codigo_unidade'] ?? ''),
            '{{total_cursos_a_vencer}}' => $colaborador['cursos_a_vencer'] ?? count($cursosAVencer),
            '{{total_cursos_vencidos}}' => $colaborador['cursos_vencidos'] ?? count($cursosVencidos),
            '{{lista_cursos_a_vencer}}' => $listaAVencerHtml,
            '{{lista_cursos_vencidos}}' => $listaVencidosHtml,
            '{{lista_cursos_a_vencer_texto}}' => $listaAVencerTexto,
            '{{lista_cursos_vencidos_texto}}' => $listaVencidosTexto,
            '{{data_envio}}' => date('d/m/Y H:i:s')
        ];

        // Adicionar dados extras
        foreach ($dados_extras as $chave => $valor) {
            $variaveis["{{$chave}}"] = $valor;
        }

        // Substituir variáveis no assunto e corpo
        $assunto = str_replace(array_keys($variaveis), array_values($variaveis), $template['assunto']);
        $corpo_html = str_replace(array_keys($variaveis), array_values($variaveis), $template['corpo_html']);
        $corpo_texto = str_replace(array_keys($variaveis), array_values($variaveis), $template['corpo_texto']);

        return [
            'assunto' => $assunto,
            'corpo_html' => $corpo_html,
            'corpo_texto' => $corpo_texto
        ];
    }

    /**
     * Gera lista HTML dos cursos
     */
    private function gerarListaCursosHtml($cursos, $tipo) {
        if (empty($cursos)) {
            return '<p><em>Nenhum curso encontrado.</em></p>';
        }

        $cor = $tipo === 'vencidos' ? '#dc3545' : '#ffc107';
        $icone = $tipo === 'vencidos' ? 'fas fa-exclamation-triangle' : 'fas fa-clock';

        $html = '<div style="margin: 15px 0;">';
        $html .= '<ul style="list-style: none; padding: 0; margin: 0;">';

        foreach ($cursos as $curso) {
            $dataFormatada = date('d/m/Y', strtotime($curso['concluir_trilha_ate']));

            if ($tipo === 'vencidos') {
                $diasTexto = $curso['dias_vencidos'] . ' dia(s) em atraso';
            } else {
                $diasTexto = $curso['dias_restantes'] . ' dia(s) restantes';
            }

            $html .= '<li style="margin-bottom: 10px; padding: 8px; border-left: 3px solid ' . $cor . '; background-color: #f8f9fa;">';
            $html .= '<div style="display: flex; justify-content: between; align-items: center;">';
            $html .= '<div>';
            $html .= '<strong style="color: #333;">' . htmlspecialchars($curso['trilha']) . '</strong><br>';
            $html .= '<span style="color: #666; font-size: 0.9em;">' . htmlspecialchars($curso['recurso']) . '</span>';
            $html .= '</div>';
            $html .= '<div style="text-align: right; color: ' . $cor . '; font-size: 0.85em;">';
            $html .= '<i class="' . $icone . '"></i> ' . $dataFormatada . '<br>';
            $html .= '<small>' . $diasTexto . '</small>';
            $html .= '</div>';
            $html .= '</div>';
            $html .= '</li>';
        }

        $html .= '</ul>';
        $html .= '</div>';

        return $html;
    }

    /**
     * Gera lista de texto dos cursos
     */
    private function gerarListaCursosTexto($cursos, $tipo) {
        if (empty($cursos)) {
            return 'Nenhum curso encontrado.';
        }

        $texto = '';
        $contador = 1;

        foreach ($cursos as $curso) {
            $dataFormatada = date('d/m/Y', strtotime($curso['concluir_trilha_ate']));

            if ($tipo === 'vencidos') {
                $diasTexto = $curso['dias_vencidos'] . ' dia(s) em atraso';
            } else {
                $diasTexto = $curso['dias_restantes'] . ' dia(s) restantes';
            }

            $texto .= $contador . '. ' . $curso['trilha'] . ' - ' . $curso['recurso'] . "\n";
            $texto .= '   Prazo: ' . $dataFormatada . ' (' . $diasTexto . ")\n\n";

            $contador++;
        }

        return trim($texto);
    }
    
    /**
     * Agenda envio de e-mail individual
     */
    public function agendarEnvioIndividual($template_id, $cpf_colaborador) {
        // Buscar dados do colaborador
        $stmt = $this->pdo->prepare("
            SELECT cpf, MAX(usuario) as nome, MAX(email) as email,
                   MAX(funcao) as funcao, MAX(codigo_unidade) as codigo_unidade,
                   COUNT(DISTINCT CASE WHEN concluir_trilha_ate BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
                                      AND (aprovacao != 'Sim' OR aprovacao IS NULL)
                                      THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_a_vencer,
                   COUNT(DISTINCT CASE WHEN concluir_trilha_ate < CURDATE()
                                      AND (aprovacao != 'Sim' OR aprovacao IS NULL)
                                      THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_vencidos
            FROM edu_relatorio_educacao
            WHERE cpf = ?
            GROUP BY cpf
        ");
        $stmt->execute([$cpf_colaborador]);
        $colaborador = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$colaborador) {
            throw new Exception("Colaborador não encontrado");
        }

        return $this->agendarEnvio($template_id, $colaborador, 'manual');
    }

    /**
     * Agenda envio de e-mail em massa
     */
    public function agendarEnvioMassa($template_id, $tipo_destinatario, $filtros = [], $cpfs_selecionados = []) {
        $colaboradores = [];

        if (!empty($cpfs_selecionados)) {
            // Envio para CPFs específicos
            foreach ($cpfs_selecionados as $cpf) {
                try {
                    $this->agendarEnvioIndividual($template_id, $cpf);
                } catch (Exception $e) {
                    // Log do erro mas continua processamento
                    error_log("Erro ao agendar e-mail para CPF $cpf: " . $e->getMessage());
                }
            }
            return count($cpfs_selecionados);
        }

        // Envio baseado em critérios
        switch ($tipo_destinatario) {
            case 'a_vencer':
                $dias_limite = $filtros['dias_limite'] ?? 30;
                $colaboradores = $this->buscarColaboradoresAVencer($dias_limite, $filtros);
                break;

            case 'vencidos':
                $colaboradores = $this->buscarColaboradoresVencidos($filtros);
                break;
        }

        $total_agendados = 0;
        foreach ($colaboradores as $colaborador) {
            try {
                if (!empty($colaborador['email'])) {
                    $this->agendarEnvio($template_id, $colaborador, 'manual');
                    $total_agendados++;
                }
            } catch (Exception $e) {
                error_log("Erro ao agendar e-mail para {$colaborador['nome']}: " . $e->getMessage());
            }
        }

        return $total_agendados;
    }

    /**
     * Agenda envio de e-mail
     */
    public function agendarEnvio($template_id, $colaborador, $tipo_envio = 'manual', $agendamento_id = null, $data_agendamento = null) {
        $template = $this->buscarTemplate($template_id);
        if (!$template) {
            throw new Exception("Template não encontrado");
        }
        
        if (empty($colaborador['email'])) {
            throw new Exception("E-mail do colaborador não informado");
        }
        
        $conteudo = $this->processarVariaveis($template, $colaborador);
        
        $stmt = $this->pdo->prepare("
            INSERT INTO edu_email_envios 
            (agendamento_id, template_id, destinatario_cpf, destinatario_nome, destinatario_email, 
             assunto, corpo_html, tipo_envio, data_agendamento, usuario_envio) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $agendamento_id,
            $template_id,
            $colaborador['cpf'],
            $colaborador['nome'],
            $colaborador['email'],
            $conteudo['assunto'],
            $conteudo['corpo_html'],
            $tipo_envio,
            $data_agendamento ?: date('Y-m-d H:i:s'),
            $_SESSION['user_id'] ?? 1
        ]);
        
        $envio_id = $this->pdo->lastInsertId();
        
        // Adicionar à fila de processamento
        $this->adicionarFila($envio_id);
        
        return $envio_id;
    }
    
    /**
     * Adiciona envio à fila de processamento
     */
    private function adicionarFila($envio_id, $prioridade = 5) {
        $stmt = $this->pdo->prepare("
            INSERT INTO edu_email_fila (envio_id, prioridade) 
            VALUES (?, ?)
        ");
        $stmt->execute([$envio_id, $prioridade]);
    }
    
    /**
     * Processa fila de e-mails
     */
    public function processarFila($limite = 10) {
        // Validar e sanitizar o limite
        $limite = (int)$limite;
        if ($limite <= 0 || $limite > 100) {
            $limite = 10;
        }

        // Buscar e-mails pendentes na fila
        $stmt = $this->pdo->prepare("
            SELECT f.*, e.destinatario_email, e.assunto, e.corpo_html, e.id as envio_id
            FROM edu_email_fila f
            JOIN edu_email_envios e ON f.envio_id = e.id
            WHERE f.status = 'pendente'
            AND f.tentativas < f.max_tentativas
            AND f.data_agendamento <= NOW()
            ORDER BY f.prioridade ASC, f.data_agendamento ASC
            LIMIT " . $limite
        );
        $stmt->execute();
        $emails = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $resultados = [];
        
        foreach ($emails as $email) {
            try {
                // Marcar como processando
                $this->atualizarStatusFila($email['id'], 'processando');
                
                // Tentar enviar
                $sucesso = enviarEmail($email['destinatario_email'], $email['assunto'], $email['corpo_html']);
                
                if ($sucesso) {
                    // Sucesso
                    $this->atualizarStatusFila($email['id'], 'enviado');
                    $this->atualizarStatusEnvio($email['envio_id'], 'enviado');
                    $resultados[] = ['status' => 'sucesso', 'email' => $email['destinatario_email']];
                } else {
                    // Erro
                    $this->incrementarTentativa($email['id'], 'Erro no envio SMTP');
                    $resultados[] = ['status' => 'erro', 'email' => $email['destinatario_email'], 'erro' => 'Erro SMTP'];
                }
                
            } catch (Exception $e) {
                // Erro na tentativa
                $this->incrementarTentativa($email['id'], $e->getMessage());
                $resultados[] = ['status' => 'erro', 'email' => $email['destinatario_email'], 'erro' => $e->getMessage()];
            }
        }
        
        return $resultados;
    }
    
    /**
     * Atualiza status na fila
     */
    private function atualizarStatusFila($fila_id, $status, $erro = null) {
        $stmt = $this->pdo->prepare("
            UPDATE edu_email_fila 
            SET status = ?, data_processamento = NOW(), erro_detalhes = ?
            WHERE id = ?
        ");
        $stmt->execute([$status, $erro, $fila_id]);
    }
    
    /**
     * Atualiza status do envio
     */
    private function atualizarStatusEnvio($envio_id, $status, $erro = null) {
        $stmt = $this->pdo->prepare("
            UPDATE edu_email_envios 
            SET status = ?, data_envio = NOW(), erro_detalhes = ?
            WHERE id = ?
        ");
        $stmt->execute([$status, $erro, $envio_id]);
    }
    
    /**
     * Incrementa tentativa de envio
     */
    private function incrementarTentativa($fila_id, $erro) {
        $stmt = $this->pdo->prepare("
            UPDATE edu_email_fila 
            SET tentativas = tentativas + 1, 
                status = CASE WHEN tentativas + 1 >= max_tentativas THEN 'erro' ELSE 'pendente' END,
                erro_detalhes = ?
            WHERE id = ?
        ");
        $stmt->execute([$erro, $fila_id]);
        
        // Se atingiu máximo de tentativas, atualizar envio também
        $stmt = $this->pdo->prepare("
            UPDATE edu_email_envios e
            JOIN edu_email_fila f ON e.id = f.envio_id
            SET e.status = 'erro', e.erro_detalhes = ?
            WHERE f.id = ? AND f.tentativas >= f.max_tentativas
        ");
        $stmt->execute([$erro, $fila_id]);
    }
    
    /**
     * Gerenciamento da Fila
     */
    public function listarFila($limite = 50) {
        $limite = (int)$limite;
        if ($limite <= 0 || $limite > 200) {
            $limite = 50;
        }

        $stmt = $this->pdo->prepare("
            SELECT
                f.id as fila_id,
                f.status as status_fila,
                f.prioridade,
                f.tentativas,
                f.max_tentativas,
                f.data_agendamento,
                f.data_processamento,
                f.erro_detalhes,
                e.id as envio_id,
                e.destinatario_cpf,
                e.destinatario_nome,
                e.destinatario_email,
                e.assunto,
                e.tipo_envio,
                e.data_criacao,
                t.nome as template_nome,
                t.tipo as template_tipo
            FROM edu_email_fila f
            JOIN edu_email_envios e ON f.envio_id = e.id
            LEFT JOIN edu_email_templates t ON e.template_id = t.id
            WHERE f.status IN ('pendente', 'processando', 'erro')
            ORDER BY
                CASE f.status
                    WHEN 'processando' THEN 1
                    WHEN 'pendente' THEN 2
                    WHEN 'erro' THEN 3
                END,
                f.prioridade ASC,
                f.data_agendamento ASC
            LIMIT " . $limite
        );
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function removerDaFila($fila_id) {
        try {
            $this->pdo->beginTransaction();

            // Buscar informações do item da fila
            $stmt = $this->pdo->prepare("
                SELECT f.envio_id, e.destinatario_nome, e.destinatario_email
                FROM edu_email_fila f
                JOIN edu_email_envios e ON f.envio_id = e.id
                WHERE f.id = ?
            ");
            $stmt->execute([$fila_id]);
            $item = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$item) {
                throw new Exception("Item não encontrado na fila");
            }

            // Remover da fila
            $stmt = $this->pdo->prepare("DELETE FROM edu_email_fila WHERE id = ?");
            $stmt->execute([$fila_id]);

            // Atualizar status do envio para cancelado
            $stmt = $this->pdo->prepare("
                UPDATE edu_email_envios
                SET status = 'cancelado', erro_detalhes = 'Removido manualmente da fila'
                WHERE id = ?
            ");
            $stmt->execute([$item['envio_id']]);

            $this->pdo->commit();

            return [
                'success' => true,
                'message' => "E-mail para {$item['destinatario_nome']} ({$item['destinatario_email']}) removido da fila com sucesso"
            ];

        } catch (Exception $e) {
            $this->pdo->rollBack();
            return [
                'success' => false,
                'message' => "Erro ao remover da fila: " . $e->getMessage()
            ];
        }
    }

    public function limparFilaErros() {
        try {
            $this->pdo->beginTransaction();

            // Buscar itens com erro
            $stmt = $this->pdo->prepare("
                SELECT f.envio_id
                FROM edu_email_fila f
                WHERE f.status = 'erro' OR f.tentativas >= f.max_tentativas
            ");
            $stmt->execute();
            $envios_erro = $stmt->fetchAll(PDO::FETCH_COLUMN);

            if (empty($envios_erro)) {
                $this->pdo->rollBack();
                return [
                    'success' => true,
                    'message' => "Nenhum item com erro encontrado na fila"
                ];
            }

            // Remover da fila
            $stmt = $this->pdo->prepare("
                DELETE FROM edu_email_fila
                WHERE status = 'erro' OR tentativas >= max_tentativas
            ");
            $stmt->execute();
            $removidos_fila = $stmt->rowCount();

            // Atualizar status dos envios
            $placeholders = str_repeat('?,', count($envios_erro) - 1) . '?';
            $stmt = $this->pdo->prepare("
                UPDATE edu_email_envios
                SET status = 'cancelado', erro_detalhes = 'Removido da fila - limpeza de erros'
                WHERE id IN ($placeholders)
            ");
            $stmt->execute($envios_erro);

            $this->pdo->commit();

            return [
                'success' => true,
                'message' => "$removidos_fila itens com erro removidos da fila com sucesso"
            ];

        } catch (Exception $e) {
            $this->pdo->rollBack();
            return [
                'success' => false,
                'message' => "Erro ao limpar fila: " . $e->getMessage()
            ];
        }
    }

    /**
     * Utilitários
     */
    private function formatarCpf($cpf) {
        $cpf = preg_replace('/[^0-9]/', '', $cpf);
        if (strlen($cpf) == 11) {
            return substr($cpf, 0, 3) . '.' . substr($cpf, 3, 3) . '.' . substr($cpf, 6, 3) . '-' . substr($cpf, 9, 2);
        }
        return $cpf;
    }

    private function extrairPA($codigo_unidade) {
        if (preg_match('/(\d+)-(.+)/', $codigo_unidade, $matches)) {
            return $matches[1] . ' - ' . $matches[2];
        }
        return $codigo_unidade ?: 'N/A';
    }
}
?>
