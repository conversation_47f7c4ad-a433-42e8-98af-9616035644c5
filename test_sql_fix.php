<?php
/**
 * Teste de Correção SQL - Aba de Cursos
 * 
 * Este arquivo testa especificamente a correção do erro SQL na aba de cursos.
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'classes/IntranetAPI.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

echo "<h1>🔧 Teste de Correção SQL - Aba de Cursos</h1>";

// Teste 1: Verificar constantes
echo "<h2>1. 📋 Verificação das Constantes</h2>";

echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p>✅ <strong>Constantes:</strong></p>";
echo "<ul>";
echo "<li><strong>EDU_RECORDS_PER_PAGE:</strong> " . EDU_RECORDS_PER_PAGE . " (tipo: " . gettype(EDU_RECORDS_PER_PAGE) . ")</li>";
echo "<li><strong>Valor inteiro:</strong> " . (int)EDU_RECORDS_PER_PAGE . "</li>";
echo "</ul>";
echo "</div>";

// Teste 2: Simular exatamente a lógica da aba de cursos
echo "<h2>2. 🎓 Simulação Exata da Aba de Cursos</h2>";

try {
    // Simular parâmetros exatos
    $aba_ativa = 'cursos';
    $filtros = ['page' => 1];
    
    // Definir offset para paginação de cursos
    $offset_cursos = ($filtros['page'] - 1) * EDU_RECORDS_PER_PAGE;
    
    // Criar condições específicas para cursos
    $where_conditions_cursos = [];
    $params_cursos = [];
    
    echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Parâmetros Calculados:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Página:</strong> " . $filtros['page'] . "</li>";
    echo "<li><strong>EDU_RECORDS_PER_PAGE:</strong> " . EDU_RECORDS_PER_PAGE . "</li>";
    echo "<li><strong>offset_cursos:</strong> $offset_cursos (tipo: " . gettype($offset_cursos) . ")</li>";
    echo "<li><strong>offset_cursos (int):</strong> " . (int)$offset_cursos . "</li>";
    echo "</ul>";
    echo "</div>";
    
    // Query exata como no arquivo principal
    $cursos_query = "
        SELECT 
            codigo_recurso,
            recurso,
            trilha,
            codigo_trilha,
            carga_horaria_recurso,
            COUNT(DISTINCT cpf) as total_colaboradores,
            SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as total_aprovados,
            COUNT(*) as total_registros,
            AVG(CASE WHEN nota_recurso > 0 THEN nota_recurso ELSE NULL END) as media_notas,
            AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento,
            COUNT(CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00' THEN 1 END) as total_concluidos,
            COUNT(CASE WHEN validade_recurso IS NOT NULL AND validade_recurso != '0000-00-00' AND validade_recurso <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND validade_recurso >= CURDATE() THEN 1 END) as a_vencer,
            COUNT(CASE WHEN validade_recurso IS NOT NULL AND validade_recurso != '0000-00-00' AND validade_recurso < CURDATE() THEN 1 END) as vencidos,
            MAX(data_importacao) as ultima_atualizacao
        FROM edu_relatorio_educacao
        " . (!empty($where_conditions_cursos) ? "WHERE " . implode(" AND ", $where_conditions_cursos) : "") . "
        GROUP BY codigo_recurso, recurso, trilha, codigo_trilha, carga_horaria_recurso
        ORDER BY recurso
        LIMIT " . (int)EDU_RECORDS_PER_PAGE . " OFFSET " . (int)$offset_cursos;
    
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Query SQL Gerada:</strong></p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 11px; overflow-x: auto;'>";
    echo htmlspecialchars($cursos_query);
    echo "</pre>";
    echo "</div>";
    
    // Executar a query
    $stmt_cursos = $pdo_edu->prepare($cursos_query);
    $stmt_cursos->execute($params_cursos);
    $cursos_data = $stmt_cursos->fetchAll();
    
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>✅ <strong>Query Executada com Sucesso!</strong></p>";
    echo "<ul>";
    echo "<li><strong>Cursos encontrados:</strong> " . count($cursos_data) . "</li>";
    echo "<li><strong>Parâmetros usados:</strong> " . count($params_cursos) . " parâmetros</li>";
    echo "<li><strong>Parâmetros:</strong> [" . implode(', ', $params_cursos) . "]</li>";
    echo "</ul>";
    echo "</div>";
    
    // Mostrar alguns resultados
    if (!empty($cursos_data)) {
        echo "<div style='background: #e2e3e5; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>Primeiros 3 Cursos Encontrados:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 5px;'>Código</th>";
        echo "<th style='padding: 5px;'>Curso</th>";
        echo "<th style='padding: 5px;'>Trilha</th>";
        echo "<th style='padding: 5px;'>Colaboradores</th>";
        echo "<th style='padding: 5px;'>Aprovados</th>";
        echo "<th style='padding: 5px;'>Concluídos</th>";
        echo "</tr>";
        
        for ($i = 0; $i < min(3, count($cursos_data)); $i++) {
            $curso = $cursos_data[$i];
            echo "<tr>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($curso['codigo_recurso']) . "</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($curso['recurso'], 0, 40)) . "...</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($curso['trilha']) . "</td>";
            echo "<td style='padding: 5px; text-align: center;'>" . $curso['total_colaboradores'] . "</td>";
            echo "<td style='padding: 5px; text-align: center;'>" . $curso['total_aprovados'] . "</td>";
            echo "<td style='padding: 5px; text-align: center;'>" . $curso['total_concluidos'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro na execução:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Mensagem:</strong> " . htmlspecialchars($e->getMessage()) . "</li>";
    echo "<li><strong>Arquivo:</strong> " . $e->getFile() . "</li>";
    echo "<li><strong>Linha:</strong> " . $e->getLine() . "</li>";
    echo "</ul>";
    echo "</div>";
}

// Teste 3: Verificar contagem
echo "<h2>3. 📊 Teste de Contagem</h2>";

try {
    $count_cursos_query = "
        SELECT COUNT(DISTINCT CONCAT(codigo_recurso, '-', recurso)) as total
        FROM edu_relatorio_educacao
    ";
    
    $stmt_count_cursos = $pdo_edu->prepare($count_cursos_query);
    $stmt_count_cursos->execute();
    $total_cursos_count = $stmt_count_cursos->fetch()['total'];
    $total_pages_cursos = ceil($total_cursos_count / EDU_RECORDS_PER_PAGE);
    
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>✅ <strong>Contagem Executada com Sucesso!</strong></p>";
    echo "<ul>";
    echo "<li><strong>Total de cursos únicos:</strong> " . number_format($total_cursos_count) . "</li>";
    echo "<li><strong>Registros por página:</strong> " . EDU_RECORDS_PER_PAGE . "</li>";
    echo "<li><strong>Total de páginas:</strong> " . $total_pages_cursos . "</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro na contagem:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 4: Verificar versão do MariaDB
echo "<h2>4. 🗄️ Informações do Banco</h2>";

try {
    $version_query = "SELECT VERSION() as version";
    $stmt_version = $pdo_edu->prepare($version_query);
    $stmt_version->execute();
    $version_info = $stmt_version->fetch();
    
    echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Informações do Banco:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Versão:</strong> " . htmlspecialchars($version_info['version']) . "</li>";
    echo "<li><strong>Driver PDO:</strong> " . $pdo_edu->getAttribute(PDO::ATTR_DRIVER_NAME) . "</li>";
    echo "<li><strong>Versão do Cliente:</strong> " . $pdo_edu->getAttribute(PDO::ATTR_CLIENT_VERSION) . "</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro ao obter informações do banco:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Resumo final
echo "<h2>5. 📋 Resumo da Correção SQL</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔧 Correção Implementada</h3>";

echo "<h4>❌ Problema Original:</h4>";
echo "<ul>";
echo "<li><strong>Erro:</strong> SQLSTATE[42000]: Syntax error near ''50' OFFSET '0''</li>";
echo "<li><strong>Causa:</strong> Parâmetros LIMIT e OFFSET sendo tratados como strings</li>";
echo "<li><strong>MariaDB:</strong> Não aceita parâmetros preparados para LIMIT/OFFSET em algumas versões</li>";
echo "</ul>";

echo "<h4>✅ Solução Aplicada:</h4>";
echo "<ul>";
echo "<li><strong>LIMIT/OFFSET Diretos:</strong> Valores inseridos diretamente na query</li>";
echo "<li><strong>Conversão para Inteiro:</strong> (int)EDU_RECORDS_PER_PAGE e (int)\$offset_cursos</li>";
echo "<li><strong>Segurança Mantida:</strong> Valores são constantes ou calculados internamente</li>";
echo "<li><strong>Compatibilidade:</strong> Funciona com todas as versões do MariaDB/MySQL</li>";
echo "</ul>";

echo "<h4>✅ Estrutura Final:</h4>";
echo "<ul>";
echo "<li><strong>Query:</strong> LIMIT " . (int)EDU_RECORDS_PER_PAGE . " OFFSET " . (int)$offset_cursos . "</li>";
echo "<li><strong>Parâmetros:</strong> Apenas para filtros WHERE</li>";
echo "<li><strong>Paginação:</strong> Valores diretos na string SQL</li>";
echo "<li><strong>Segurança:</strong> Valores validados e convertidos</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=cursos' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🎓 Testar Aba Cursos Corrigida</a>";
echo "</p>";
?>
