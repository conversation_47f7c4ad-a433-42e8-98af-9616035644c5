<?php
// Relatório de Colaboradores com Cursos A Vencer
// Este arquivo gera um relatório específico de colaboradores que possuem cursos prestes a vencer

// Construir query base
$where_conditions = [];
$params = [];

if (!empty($filtros['trilha'])) {
    $where_conditions[] = "trilha LIKE ?";
    $params[] = '%' . $filtros['trilha'] . '%';
}

if (!empty($filtros['funcao'])) {
    $where_conditions[] = "funcao = ?";
    $params[] = $filtros['funcao'];
}

// Query para buscar todos os colaboradores
$colaboradores_query = "
    SELECT
        cpf,
        MAX(usuario) as usuario,
        MAX(email) as email,
        MAX(funcao) as funcao,
        MAX(codigo_unidade) as codigo_unidade,
        MAX(data_admissao) as data_admissao,
        MAX(superior_imediato) as superior_imediato,
        COUNT(DISTINCT trilha) as total_trilhas,
        COUNT(DISTINCT CONCAT(codigo_trilha, '|', codigo_recurso)) as total_cursos,
        MAX(data_importacao) as ultima_atualizacao
    FROM edu_relatorio_educacao
    " . (!empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "") . "
    GROUP BY cpf
    ORDER BY MAX(usuario)
";

$stmt_colaboradores = $pdo_edu->prepare($colaboradores_query);
$stmt_colaboradores->execute($params);
$todos_colaboradores_bruto = $stmt_colaboradores->fetchAll();

// APLICAR FILTRO DE USUÁRIOS INATIVOS
$todos_colaboradores = aplicarFiltroUsuariosInativos($todos_colaboradores_bruto, $mapa_usuarios_ativos, $mapa_usuarios_todos);

// Filtrar colaboradores que possuem cursos a vencer
$colaboradores_com_a_vencer = [];

foreach ($todos_colaboradores as $colaborador) {
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;
    
    // Filtrar por PA se especificado
    if (!empty($filtros['pa'])) {
        if (!$usuario_intranet || $usuario_intranet['agencia'] != $filtros['pa']) {
            continue;
        }
    }
    
    $cursos_colaborador = buscarCursosColaborador($colaborador['cpf'], $pdo_edu, $prazos_config);
    $cursos_a_vencer = [];
    $tem_curso_no_periodo = true;
    
    // Verificar filtro de período se especificado
    if (!empty($filtros['periodo'])) {
        $tem_curso_no_periodo = false;
        foreach ($cursos_colaborador as $curso) {
            if (!empty($curso['prazo_calculado'])) {
                $prazo_curso = new DateTime($curso['prazo_calculado']);
                if (verificarCursoNoPeriodo($prazo_curso, $filtros['periodo'])) {
                    $tem_curso_no_periodo = true;
                    break;
                }
            }
        }
    }
    
    if (!$tem_curso_no_periodo) continue;
    
    // Buscar cursos a vencer
    foreach ($cursos_colaborador as $curso) {
        if ($curso['status_prazo'] === 'a_vencer') {
            $cursos_a_vencer[] = $curso;
        }
    }
    
    if (!empty($cursos_a_vencer)) {
        $colaborador['cursos_a_vencer'] = $cursos_a_vencer;
        $colaborador['total_a_vencer'] = count($cursos_a_vencer);
        $colaboradores_com_a_vencer[] = $colaborador;
    }
}

// Ordenar por urgência (menor número de dias primeiro)
usort($colaboradores_com_a_vencer, function($a, $b) {
    $min_dias_a = min(array_column($a['cursos_a_vencer'], 'dias_prazo'));
    $min_dias_b = min(array_column($b['cursos_a_vencer'], 'dias_prazo'));
    return $min_dias_a - $min_dias_b;
});

// Cabeçalho do relatório
echo '<table border="1" style="border-collapse: collapse;">';
echo '<tr style="background-color: #fd7e14; color: white; font-weight: bold;">';
echo '<td colspan="15" style="padding: 10px; text-align: center; font-size: 16px;">';
echo 'RELATÓRIO DE COLABORADORES COM CURSOS A VENCER';
echo '</td>';
echo '</tr>';

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td style="padding: 8px;">Gerado em:</td>';
echo '<td colspan="14" style="padding: 8px;">' . date('d/m/Y H:i:s') . '</td>';
echo '</tr>';

if (!empty($filtros['trilha']) || !empty($filtros['funcao']) || !empty($filtros['periodo']) || !empty($filtros['pa'])) {
    echo '<tr style="background-color: #e9ecef;">';
    echo '<td style="padding: 8px; font-weight: bold;">Filtros Aplicados:</td>';
    echo '<td colspan="14" style="padding: 8px;">';
    $filtros_texto = [];
    if (!empty($filtros['trilha'])) $filtros_texto[] = "Trilha: " . $filtros['trilha'];
    if (!empty($filtros['funcao'])) $filtros_texto[] = "Função: " . $filtros['funcao'];
    if (!empty($filtros['periodo'])) $filtros_texto[] = "Período: " . $filtros['periodo'];
    if (!empty($filtros['pa'])) {
        $agencia_nome = isset($mapa_agencias[$filtros['pa']]) ? 
            $mapa_agencias[$filtros['pa']]['numero'] . ' - ' . $mapa_agencias[$filtros['pa']]['nome'] : 
            $filtros['pa'];
        $filtros_texto[] = "PA: " . $agencia_nome;
    }
    echo implode(' | ', $filtros_texto);
    echo '</td>';
    echo '</tr>';
}

echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Colaboradores com Cursos A Vencer:</td>';
echo '<td colspan="14" style="padding: 8px; color: #fd7e14; font-weight: bold;">' . count($colaboradores_com_a_vencer) . '</td>';
echo '</tr>';

$total_cursos_a_vencer = array_sum(array_column($colaboradores_com_a_vencer, 'total_a_vencer'));
echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Total de Cursos A Vencer:</td>';
echo '<td colspan="14" style="padding: 8px; color: #fd7e14; font-weight: bold;">' . $total_cursos_a_vencer . '</td>';
echo '</tr>';

echo '<tr><td colspan="15" style="padding: 5px;"></td></tr>'; // Espaçamento

// Cabeçalhos das colunas
echo '<tr style="background-color: #fd7e14; color: white; font-weight: bold;">';
echo '<td style="padding: 8px; text-align: center;">CPF</td>';
echo '<td style="padding: 8px; text-align: center;">Nome</td>';
echo '<td style="padding: 8px; text-align: center;">E-mail</td>';
echo '<td style="padding: 8px; text-align: center;">Função</td>';
echo '<td style="padding: 8px; text-align: center;">PA/Agência</td>';
echo '<td style="padding: 8px; text-align: center;">Setor</td>';
echo '<td style="padding: 8px; text-align: center;">Superior Imediato</td>';
echo '<td style="padding: 8px; text-align: center;">Qtd A Vencer</td>';
echo '<td style="padding: 8px; text-align: center;">Trilha</td>';
echo '<td style="padding: 8px; text-align: center;">Curso A Vencer</td>';
echo '<td style="padding: 8px; text-align: center;">Prazo</td>';
echo '<td style="padding: 8px; text-align: center;">Dias Restantes</td>';
echo '<td style="padding: 8px; text-align: center;">Andamento</td>';
echo '<td style="padding: 8px; text-align: center;">Carga Horária</td>';
echo '<td style="padding: 8px; text-align: center;">Urgência</td>';
echo '</tr>';

// Dados dos colaboradores com cursos a vencer
$linha = 0;
foreach ($colaboradores_com_a_vencer as $colaborador) {
    $linha++;
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;
    
    // Informações da intranet
    $nome_exibir = $usuario_intranet['nome'] ?? $colaborador['usuario'];
    $email_exibir = $usuario_intranet['email'] ?? $colaborador['email'] ?? 'N/A';
    $funcao_exibir = $usuario_intranet['nomeFuncao'] ?? $colaborador['funcao'] ?? 'N/A';
    $setor_exibir = $usuario_intranet['nomeSetor'] ?? 'N/A';
    
    // Informações da agência
    $agencia_info = 'N/A';
    if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
        $agencia_id = $usuario_intranet['agencia'];
        if (isset($mapa_agencias[$agencia_id])) {
            $agencia_data = $mapa_agencias[$agencia_id];
            $agencia_info = $agencia_data['numero'] . ' - ' . $agencia_data['nome'];
        } else {
            $agencia_info = $agencia_id;
        }
    }
    
    // Ordenar cursos a vencer por dias restantes (menor primeiro)
    $cursos_a_vencer = $colaborador['cursos_a_vencer'];
    usort($cursos_a_vencer, function($a, $b) {
        return $a['dias_prazo'] - $b['dias_prazo'];
    });
    
    foreach ($cursos_a_vencer as $curso) {
        $cor_linha = ($linha % 2 == 0) ? '#fff8e1' : '#ffffff';
        echo '<tr style="background-color: ' . $cor_linha . ';">';
        
        // Repetir dados do colaborador em todas as linhas para compatibilidade com Excel
        echo '<td style="padding: 6px;">' . formatarCpf($colaborador['cpf']) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($nome_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($email_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($funcao_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($agencia_info) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($setor_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($colaborador['superior_imediato'] ?? 'N/A') . '</td>';
        echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: #fd7e14;">' . $colaborador['total_a_vencer'] . '</td>';
        
        // Dados do curso a vencer
        echo '<td style="padding: 6px;">' . htmlspecialchars($curso['trilha']) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($curso['recurso']) . '</td>';
        echo '<td style="padding: 6px; text-align: center;">' . 
             ($curso['prazo_calculado'] ? date('d/m/Y', strtotime($curso['prazo_calculado'])) : 'N/A') . '</td>';
        
        // Dias restantes
        $dias_restantes = $curso['dias_prazo'];
        $cor_urgencia = '#28a745'; // Verde
        if ($dias_restantes <= 7) {
            $cor_urgencia = '#dc3545'; // Vermelho
        } elseif ($dias_restantes <= 15) {
            $cor_urgencia = '#fd7e14'; // Laranja
        } elseif ($dias_restantes <= 30) {
            $cor_urgencia = '#ffc107'; // Amarelo
        }
        
        echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: ' . $cor_urgencia . ';">' . 
             $dias_restantes . ' dias</td>';
        
        // Andamento
        $andamento = $curso['andamento_etapa'] ?? 'Não iniciado';
        echo '<td style="padding: 6px;">' . htmlspecialchars($andamento) . '</td>';
        
        // Carga horária
        echo '<td style="padding: 6px; text-align: center;">' . 
             ($curso['carga_horaria_recurso'] ?? 'N/A') . '</td>';
        
        // Urgência baseada nos dias restantes
        $urgencia = 'Baixa';
        $cor_urgencia_texto = '#28a745';
        if ($dias_restantes <= 7) {
            $urgencia = 'CRÍTICA';
            $cor_urgencia_texto = '#dc3545';
        } elseif ($dias_restantes <= 15) {
            $urgencia = 'ALTA';
            $cor_urgencia_texto = '#fd7e14';
        } elseif ($dias_restantes <= 30) {
            $urgencia = 'Média';
            $cor_urgencia_texto = '#ffc107';
        }
        
        echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: ' . $cor_urgencia_texto . ';">' . 
             $urgencia . '</td>';
        echo '</tr>';
    }
}

// Resumo por urgência
echo '<tr><td colspan="15" style="padding: 10px;"></td></tr>'; // Espaçamento

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td colspan="15" style="padding: 10px; text-align: center; font-size: 14px;">RESUMO POR URGÊNCIA</td>';
echo '</tr>';

// Calcular estatísticas de urgência
$critica = 0; $alta = 0; $media = 0; $baixa = 0;
foreach ($colaboradores_com_a_vencer as $colaborador) {
    foreach ($colaborador['cursos_a_vencer'] as $curso) {
        $dias_restantes = $curso['dias_prazo'];
        if ($dias_restantes <= 7) $critica++;
        elseif ($dias_restantes <= 15) $alta++;
        elseif ($dias_restantes <= 30) $media++;
        else $baixa++;
    }
}

echo '<tr>';
echo '<td colspan="3" style="padding: 8px; background-color: #dc3545; color: white; text-align: center; font-weight: bold;">CRÍTICA (≤7 dias)</td>';
echo '<td colspan="3" style="padding: 8px; background-color: #fd7e14; color: white; text-align: center; font-weight: bold;">ALTA (8-15 dias)</td>';
echo '<td colspan="3" style="padding: 8px; background-color: #ffc107; color: black; text-align: center; font-weight: bold;">MÉDIA (16-30 dias)</td>';
echo '<td colspan="3" style="padding: 8px; background-color: #28a745; color: white; text-align: center; font-weight: bold;">BAIXA (>30 dias)</td>';
echo '<td colspan="3" style="padding: 8px; background-color: #6c757d; color: white; text-align: center; font-weight: bold;">TOTAL</td>';
echo '</tr>';

echo '<tr>';
echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #dc3545;">' . $critica . '</td>';
echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #fd7e14;">' . $alta . '</td>';
echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #ffc107;">' . $media . '</td>';
echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #28a745;">' . $baixa . '</td>';
echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #6c757d;">' . $total_cursos_a_vencer . '</td>';
echo '</tr>';

echo '</table>';
?>
