<?php
/**
 * Teste de Seções por PA
 * 
 * Este arquivo testa se a organização por PA está funcionando corretamente.
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'classes/IntranetAPI.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

echo "<h1>🏢 Teste de Seções por PA</h1>";

// Teste 1: Verificar dados da Intranet API
echo "<h2>1. 🌐 Verificação da API Intranet</h2>";

try {
    $api = new IntranetAPI();
    
    // Buscar usuários
    $usuarios_api = $api->listarUsuarios();
    $agencias_api = $api->listarAgencias();
    
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>✅ <strong>API Intranet Funcionando:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Usuários encontrados:</strong> " . count($usuarios_api) . "</li>";
    echo "<li><strong>Agências encontradas:</strong> " . count($agencias_api) . "</li>";
    echo "</ul>";
    echo "</div>";
    
    // Criar mapeamentos
    $mapa_usuarios_cpf = [];
    foreach ($usuarios_api as $usuario) {
        if (!empty($usuario['cpf'])) {
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
            $mapa_usuarios_cpf[$cpf_normalizado] = $usuario;
        }
    }
    
    $mapa_agencias = [];
    foreach ($agencias_api as $agencia) {
        $mapa_agencias[$agencia['id']] = $agencia;
    }
    
    echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Mapeamentos Criados:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Usuários por CPF:</strong> " . count($mapa_usuarios_cpf) . "</li>";
    echo "<li><strong>Agências por ID:</strong> " . count($mapa_agencias) . "</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro na API Intranet:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 2: Verificar agrupamento por PA
echo "<h2>2. 🏢 Teste de Agrupamento por PA</h2>";

try {
    // Buscar alguns colaboradores para teste
    $query_colaboradores = "
        SELECT DISTINCT 
            cpf, usuario, email, funcao, codigo_unidade
        FROM edu_relatorio_educacao 
        WHERE cpf IS NOT NULL AND cpf != ''
        LIMIT 10
    ";
    
    $stmt = $pdo_edu->prepare($query_colaboradores);
    $stmt->execute();
    $colaboradores_teste = $stmt->fetchAll();
    
    if (!empty($colaboradores_teste)) {
        $colaboradores_por_pa = [];
        
        foreach ($colaboradores_teste as $colaborador) {
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
            $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;
            
            // Determinar PA
            $pa_info = ['id' => 'sem_pa', 'numero' => 'S/PA', 'nome' => 'Sem PA Definido'];
            
            if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
                $agencia_id = $usuario_intranet['agencia'];
                if (isset($mapa_agencias[$agencia_id])) {
                    $agencia_data = $mapa_agencias[$agencia_id];
                    $pa_info = [
                        'id' => $agencia_id,
                        'numero' => $agencia_data['numero'],
                        'nome' => $agencia_data['nome']
                    ];
                } else {
                    $pa_info = ['id' => $agencia_id, 'numero' => $agencia_id, 'nome' => 'PA ' . $agencia_id];
                }
            }
            
            $pa_key = $pa_info['numero'] . ' - ' . $pa_info['nome'];
            
            if (!isset($colaboradores_por_pa[$pa_key])) {
                $colaboradores_por_pa[$pa_key] = [
                    'info' => $pa_info,
                    'colaboradores' => []
                ];
            }
            
            $colaboradores_por_pa[$pa_key]['colaboradores'][] = [
                'nome' => $colaborador['usuario'],
                'cpf' => substr($colaborador['cpf'], 0, 3) . '***',
                'funcao' => $colaborador['funcao']
            ];
        }
        
        // Ordenar PAs
        uksort($colaboradores_por_pa, function($a, $b) {
            preg_match('/^(\d+|S\/PA)/', $a, $matches_a);
            preg_match('/^(\d+|S\/PA)/', $b, $matches_b);
            
            $num_a = $matches_a[1] === 'S/PA' ? 9999 : (int)$matches_a[1];
            $num_b = $matches_b[1] === 'S/PA' ? 9999 : (int)$matches_b[1];
            
            return $num_a <=> $num_b;
        });
        
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>Agrupamento por PA Funcionando:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Total de PAs encontrados:</strong> " . count($colaboradores_por_pa) . "</li>";
        echo "<li><strong>Colaboradores testados:</strong> " . count($colaboradores_teste) . "</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>📊 Resultado do Agrupamento:</h3>";
        
        foreach ($colaboradores_por_pa as $pa_nome => $pa_data) {
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<h6><strong>🏢 " . htmlspecialchars($pa_nome) . "</strong></h6>";
            echo "<p><strong>ID:</strong> " . $pa_data['info']['id'] . "</p>";
            echo "<p><strong>Colaboradores (" . count($pa_data['colaboradores']) . "):</strong></p>";
            echo "<ul>";
            foreach ($pa_data['colaboradores'] as $colaborador) {
                echo "<li>" . htmlspecialchars($colaborador['nome']) . " (" . $colaborador['cpf'] . ") - " . htmlspecialchars($colaborador['funcao']) . "</li>";
            }
            echo "</ul>";
            echo "</div>";
        }
        echo "</div>";
        
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>⚠️ <strong>Nenhum colaborador encontrado para teste</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro no agrupamento:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 3: Verificar estrutura HTML das seções
echo "<h2>3. 🎨 Teste da Estrutura HTML</h2>";

echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Exemplo de Seção PA:</strong></p>";
echo "<div style='border: 1px solid #ccc; padding: 15px; background: white; border-radius: 8px;'>";

// Simular uma seção PA
$pa_exemplo = "88 - UAD Exemplo";
$pa_id = md5($pa_exemplo);

echo "
<div class='pa-section' style='margin-bottom: 1.5rem; border: 1px solid #e0e0e0; border-radius: 12px; overflow: hidden; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);'>
    <button class='pa-header' style='background: linear-gradient(135deg, #2E7D32, #00ACC1); color: white; padding: 1rem 1.5rem; cursor: pointer; border: none; width: 100%; text-align: left; display: flex; justify-content: space-between; align-items: center;'>
        <div>
            <h5 style='font-size: 1.1rem; font-weight: 600; margin: 0;'>
                <i class='fas fa-building' style='margin-right: 0.5rem;'></i>
                $pa_exemplo
            </h5>
            <div style='display: flex; gap: 1rem; font-size: 0.9rem; opacity: 0.9;'>
                <span><i class='fas fa-users' style='margin-right: 0.25rem;'></i>5 colaboradores</span>
                <span><i class='fas fa-graduation-cap' style='margin-right: 0.25rem;'></i>25 cursos</span>
                <span style='color: #ffc107;'><i class='fas fa-exclamation-triangle' style='margin-right: 0.25rem;'></i>3 vencidos</span>
            </div>
        </div>
        <i class='fas fa-chevron-down' style='font-size: 1.2rem;'></i>
    </button>
    
    <div class='pa-content' style='background: white; padding: 1.5rem;'>
        <div class='pa-summary' style='background: linear-gradient(135deg, #f8f9fa, #e9ecef); padding: 1rem; border-radius: 8px; margin-bottom: 1.5rem; border-left: 4px solid #00ACC1;'>
            <div style='display: grid; grid-template-columns: repeat(5, 1fr); gap: 1rem; text-align: center;'>
                <div style='background: white; padding: 0.8rem; border-radius: 6px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);'>
                    <div style='font-size: 1.5rem; font-weight: bold; color: #2E7D32;'>5</div>
                    <div style='font-size: 0.8rem; color: #6c757d; margin-top: 0.2rem;'>Colaboradores</div>
                </div>
                <div style='background: white; padding: 0.8rem; border-radius: 6px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);'>
                    <div style='font-size: 1.5rem; font-weight: bold; color: #2E7D32;'>25</div>
                    <div style='font-size: 0.8rem; color: #6c757d; margin-top: 0.2rem;'>Cursos</div>
                </div>
                <div style='background: white; padding: 0.8rem; border-radius: 6px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);'>
                    <div style='font-size: 1.5rem; font-weight: bold; color: #28a745;'>18</div>
                    <div style='font-size: 0.8rem; color: #6c757d; margin-top: 0.2rem;'>Concluídos</div>
                </div>
                <div style='background: white; padding: 0.8rem; border-radius: 6px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);'>
                    <div style='font-size: 1.5rem; font-weight: bold; color: #ffc107;'>4</div>
                    <div style='font-size: 0.8rem; color: #6c757d; margin-top: 0.2rem;'>A Vencer</div>
                </div>
                <div style='background: white; padding: 0.8rem; border-radius: 6px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);'>
                    <div style='font-size: 1.5rem; font-weight: bold; color: #dc3545;'>3</div>
                    <div style='font-size: 0.8rem; color: #6c757d; margin-top: 0.2rem;'>Vencidos</div>
                </div>
            </div>
        </div>
        
        <p style='margin: 0; color: #6c757d; font-style: italic;'>Cards dos colaboradores apareceriam aqui...</p>
    </div>
</div>
";

echo "</div>";
echo "</div>";

// Resumo final
echo "<h2>4. 📋 Resumo da Implementação</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Funcionalidade Implementada</h3>";

echo "<h4>✅ Organização por PA:</h4>";
echo "<ul>";
echo "<li><strong>Agrupamento:</strong> Colaboradores organizados por Ponto de Atendimento</li>";
echo "<li><strong>Integração API:</strong> Dados de PA vindos da Intranet Sicoob</li>";
echo "<li><strong>Ordenação:</strong> PAs ordenados numericamente</li>";
echo "<li><strong>Fallback:</strong> Colaboradores sem PA ficam em 'Sem PA Definido'</li>";
echo "</ul>";

echo "<h4>✅ Seções Expansíveis:</h4>";
echo "<ul>";
echo "<li><strong>Cabeçalho Interativo:</strong> Clique para expandir/retrair</li>";
echo "<li><strong>Estatísticas no Cabeçalho:</strong> Resumo rápido do PA</li>";
echo "<li><strong>Resumo Detalhado:</strong> Cards com métricas do PA</li>";
echo "<li><strong>Controles Globais:</strong> Botões para expandir/retrair todas as seções</li>";
echo "</ul>";

echo "<h4>✅ Interface Visual:</h4>";
echo "<ul>";
echo "<li><strong>Design Moderno:</strong> Gradientes e sombras</li>";
echo "<li><strong>Cores Sicoob:</strong> Verde escuro e turquesa</li>";
echo "<li><strong>Animações:</strong> Transições suaves</li>";
echo "<li><strong>Responsivo:</strong> Adapta-se a diferentes telas</li>";
echo "</ul>";

echo "<h4>✅ Funcionalidades:</h4>";
echo "<ul>";
echo "<li><strong>Estatísticas por PA:</strong> Total de colaboradores, cursos, vencidos</li>";
echo "<li><strong>Indicadores Visuais:</strong> Badges coloridos para alertas</li>";
echo "<li><strong>Navegação Intuitiva:</strong> Ícones e textos descritivos</li>";
echo "<li><strong>Preservação de Estado:</strong> Seções mantêm estado ao navegar</li>";
echo "</ul>";

echo "<h4>✅ Estrutura de Dados:</h4>";
echo "<ul>";
echo "<li><strong>Mapeamento CPF:</strong> Relaciona colaboradores com usuários da API</li>";
echo "<li><strong>Mapeamento Agência:</strong> Relaciona IDs com dados das agências</li>";
echo "<li><strong>Agrupamento Dinâmico:</strong> Criado em tempo real</li>";
echo "<li><strong>Ordenação Inteligente:</strong> Numérica com fallback para texto</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>👥 Testar Seções por PA</a>";
echo "<a href='analise_colaboradores.php?aba=cursos' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🎓 Testar Aba Cursos</a>";
echo "</p>";
?>
