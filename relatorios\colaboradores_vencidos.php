<?php
// Relatório de Colaboradores com Cursos Vencidos
// Este arquivo gera um relatório específico de colaboradores que possuem cursos vencidos

// Construir query base
$where_conditions = [];
$params = [];

if (!empty($filtros['trilha'])) {
    $where_conditions[] = "trilha LIKE ?";
    $params[] = '%' . $filtros['trilha'] . '%';
}

if (!empty($filtros['funcao'])) {
    $where_conditions[] = "funcao = ?";
    $params[] = $filtros['funcao'];
}

// Query para buscar todos os colaboradores
$colaboradores_query = "
    SELECT
        cpf,
        MAX(usuario) as usuario,
        MAX(email) as email,
        MAX(funcao) as funcao,
        MAX(codigo_unidade) as codigo_unidade,
        MAX(data_admissao) as data_admissao,
        MAX(superior_imediato) as superior_imediato,
        COUNT(DISTINCT trilha) as total_trilhas,
        COUNT(DISTINCT CONCAT(codigo_trilha, '|', codigo_recurso)) as total_cursos,
        MAX(data_importacao) as ultima_atualizacao
    FROM edu_relatorio_educacao
    " . (!empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "") . "
    GROUP BY cpf
    ORDER BY MAX(usuario)
";

$stmt_colaboradores = $pdo_edu->prepare($colaboradores_query);
$stmt_colaboradores->execute($params);
$todos_colaboradores = $stmt_colaboradores->fetchAll();

// Filtrar colaboradores que possuem cursos vencidos
$colaboradores_com_vencidos = [];

foreach ($todos_colaboradores as $colaborador) {
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;
    
    // Filtrar por PA se especificado
    if (!empty($filtros['pa'])) {
        if (!$usuario_intranet || $usuario_intranet['agencia'] != $filtros['pa']) {
            continue;
        }
    }
    
    $cursos_colaborador = buscarCursosColaborador($colaborador['cpf'], $pdo_edu, $prazos_config);
    $cursos_vencidos = [];
    $tem_curso_no_periodo = true;
    
    // Verificar filtro de período se especificado
    if (!empty($filtros['periodo'])) {
        $tem_curso_no_periodo = false;
        foreach ($cursos_colaborador as $curso) {
            if (!empty($curso['prazo_calculado'])) {
                $prazo_curso = new DateTime($curso['prazo_calculado']);
                if (verificarCursoNoPeriodo($prazo_curso, $filtros['periodo'])) {
                    $tem_curso_no_periodo = true;
                    break;
                }
            }
        }
    }
    
    if (!$tem_curso_no_periodo) continue;
    
    // Buscar cursos vencidos
    foreach ($cursos_colaborador as $curso) {
        if ($curso['status_prazo'] === 'vencido') {
            $cursos_vencidos[] = $curso;
        }
    }
    
    if (!empty($cursos_vencidos)) {
        $colaborador['cursos_vencidos'] = $cursos_vencidos;
        $colaborador['total_vencidos'] = count($cursos_vencidos);
        $colaboradores_com_vencidos[] = $colaborador;
    }
}

// Ordenar por quantidade de cursos vencidos (maior primeiro)
usort($colaboradores_com_vencidos, function($a, $b) {
    return $b['total_vencidos'] - $a['total_vencidos'];
});

// Cabeçalho do relatório
echo '<table border="1" style="border-collapse: collapse;">';
echo '<tr style="background-color: #dc3545; color: white; font-weight: bold;">';
echo '<td colspan="16" style="padding: 10px; text-align: center; font-size: 16px;">';
echo 'RELATÓRIO DE COLABORADORES COM CURSOS VENCIDOS';
echo '</td>';
echo '</tr>';

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td style="padding: 8px;">Gerado em:</td>';
echo '<td colspan="15" style="padding: 8px;">' . date('d/m/Y H:i:s') . '</td>';
echo '</tr>';

if (!empty($filtros['trilha']) || !empty($filtros['funcao']) || !empty($filtros['periodo']) || !empty($filtros['pa'])) {
    echo '<tr style="background-color: #e9ecef;">';
    echo '<td style="padding: 8px; font-weight: bold;">Filtros Aplicados:</td>';
    echo '<td colspan="15" style="padding: 8px;">';
    $filtros_texto = [];
    if (!empty($filtros['trilha'])) $filtros_texto[] = "Trilha: " . $filtros['trilha'];
    if (!empty($filtros['funcao'])) $filtros_texto[] = "Função: " . $filtros['funcao'];
    if (!empty($filtros['periodo'])) $filtros_texto[] = "Período: " . $filtros['periodo'];
    if (!empty($filtros['pa'])) {
        $agencia_nome = isset($mapa_agencias[$filtros['pa']]) ? 
            $mapa_agencias[$filtros['pa']]['numero'] . ' - ' . $mapa_agencias[$filtros['pa']]['nome'] : 
            $filtros['pa'];
        $filtros_texto[] = "PA: " . $agencia_nome;
    }
    echo implode(' | ', $filtros_texto);
    echo '</td>';
    echo '</tr>';
}

echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Colaboradores com Cursos Vencidos:</td>';
echo '<td colspan="15" style="padding: 8px; color: #dc3545; font-weight: bold;">' . count($colaboradores_com_vencidos) . '</td>';
echo '</tr>';

$total_cursos_vencidos = array_sum(array_column($colaboradores_com_vencidos, 'total_vencidos'));
echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Total de Cursos Vencidos:</td>';
echo '<td colspan="15" style="padding: 8px; color: #dc3545; font-weight: bold;">' . $total_cursos_vencidos . '</td>';
echo '</tr>';

echo '<tr><td colspan="16" style="padding: 5px;"></td></tr>'; // Espaçamento

// Cabeçalhos das colunas
echo '<tr style="background-color: #dc3545; color: white; font-weight: bold;">';
echo '<td style="padding: 8px; text-align: center;">CPF</td>';
echo '<td style="padding: 8px; text-align: center;">Nome</td>';
echo '<td style="padding: 8px; text-align: center;">E-mail</td>';
echo '<td style="padding: 8px; text-align: center;">Função</td>';
echo '<td style="padding: 8px; text-align: center;">PA/Agência</td>';
echo '<td style="padding: 8px; text-align: center;">Setor</td>';
echo '<td style="padding: 8px; text-align: center;">Superior Imediato</td>';
echo '<td style="padding: 8px; text-align: center;">Qtd Vencidos</td>';
echo '<td style="padding: 8px; text-align: center;">Trilha</td>';
echo '<td style="padding: 8px; text-align: center;">Trilha Obrigatória</td>';
echo '<td style="padding: 8px; text-align: center;">Curso Vencido</td>';
echo '<td style="padding: 8px; text-align: center;">Prazo Original</td>';
echo '<td style="padding: 8px; text-align: center;">Dias em Atraso</td>';
echo '<td style="padding: 8px; text-align: center;">Andamento</td>';
echo '<td style="padding: 8px; text-align: center;">Carga Horária</td>';
echo '<td style="padding: 8px; text-align: center;">Prioridade</td>';
echo '</tr>';

// Dados dos colaboradores com cursos vencidos
$linha = 0;
foreach ($colaboradores_com_vencidos as $colaborador) {
    $linha++;
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;
    
    // Informações da intranet
    $nome_exibir = $usuario_intranet['nome'] ?? $colaborador['usuario'];
    $email_exibir = $usuario_intranet['email'] ?? $colaborador['email'] ?? 'N/A';
    $funcao_exibir = $usuario_intranet['nomeFuncao'] ?? $colaborador['funcao'] ?? 'N/A';
    $setor_exibir = $usuario_intranet['nomeSetor'] ?? 'N/A';
    
    // Informações da agência
    $agencia_info = 'N/A';
    if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
        $agencia_id = $usuario_intranet['agencia'];
        if (isset($mapa_agencias[$agencia_id])) {
            $agencia_data = $mapa_agencias[$agencia_id];
            $agencia_info = $agencia_data['numero'] . ' - ' . $agencia_data['nome'];
        } else {
            $agencia_info = $agencia_id;
        }
    }
    
    // Ordenar cursos vencidos por dias de atraso (maior primeiro)
    $cursos_vencidos = $colaborador['cursos_vencidos'];
    usort($cursos_vencidos, function($a, $b) {
        return abs($b['dias_prazo']) - abs($a['dias_prazo']);
    });
    
    foreach ($cursos_vencidos as $curso) {
        $cor_linha = ($linha % 2 == 0) ? '#fff5f5' : '#ffffff';
        echo '<tr style="background-color: ' . $cor_linha . ';">';

        // Repetir dados do colaborador em todas as linhas para compatibilidade com Excel
        echo '<td style="padding: 6px;">' . formatarCpf($colaborador['cpf']) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($nome_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($email_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($funcao_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($agencia_info) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($setor_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($colaborador['superior_imediato'] ?? 'N/A') . '</td>';
        echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: #dc3545;">' . $colaborador['total_vencidos'] . '</td>';
        
        // Dados do curso vencido
        echo '<td style="padding: 6px;">' . htmlspecialchars($curso['trilha']) . '</td>';

        // Verificar se a trilha é obrigatória
        $trilha_obrigatoria = isTrilhaObrigatoria($curso['codigo_trilha'], $trilhas_obrigatorias);
        echo '<td style="padding: 6px; text-align: center;">';
        if ($trilha_obrigatoria) {
            echo '<span style="color: #856404; font-weight: bold;">SIM</span>';
        } else {
            echo '<span style="color: #6c757d;">NÃO</span>';
        }
        echo '</td>';

        echo '<td style="padding: 6px;">' . htmlspecialchars($curso['recurso']) . '</td>';
        echo '<td style="padding: 6px; text-align: center;">' . 
             ($curso['prazo_calculado'] ? date('d/m/Y', strtotime($curso['prazo_calculado'])) : 'N/A') . '</td>';
        
        // Dias em atraso
        $dias_atraso = abs($curso['dias_prazo']);
        $cor_atraso = '#dc3545';
        if ($dias_atraso > 90) $cor_atraso = '#6f42c1'; // Roxo para muito atrasado
        elseif ($dias_atraso > 60) $cor_atraso = '#dc3545'; // Vermelho
        elseif ($dias_atraso > 30) $cor_atraso = '#fd7e14'; // Laranja
        
        echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: ' . $cor_atraso . ';">' . 
             $dias_atraso . ' dias</td>';
        
        // Andamento
        $andamento = $curso['andamento_etapa'] ?? 'Não iniciado';
        echo '<td style="padding: 6px;">' . htmlspecialchars($andamento) . '</td>';
        
        // Carga horária
        echo '<td style="padding: 6px; text-align: center;">' . 
             ($curso['carga_horaria_recurso'] ?? 'N/A') . '</td>';
        
        // Prioridade baseada nos dias de atraso
        $prioridade = 'Baixa';
        $cor_prioridade = '#28a745';
        if ($dias_atraso > 90) {
            $prioridade = 'CRÍTICA';
            $cor_prioridade = '#6f42c1';
        } elseif ($dias_atraso > 60) {
            $prioridade = 'ALTA';
            $cor_prioridade = '#dc3545';
        } elseif ($dias_atraso > 30) {
            $prioridade = 'Média';
            $cor_prioridade = '#fd7e14';
        }
        
        echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: ' . $cor_prioridade . ';">' . 
             $prioridade . '</td>';
        echo '</tr>';
    }
}

// Resumo por prioridade
echo '<tr><td colspan="16" style="padding: 10px;"></td></tr>'; // Espaçamento

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td colspan="16" style="padding: 10px; text-align: center; font-size: 14px;">RESUMO POR PRIORIDADE</td>';
echo '</tr>';

// Calcular estatísticas de prioridade
$critica = 0; $alta = 0; $media = 0; $baixa = 0;
foreach ($colaboradores_com_vencidos as $colaborador) {
    foreach ($colaborador['cursos_vencidos'] as $curso) {
        $dias_atraso = abs($curso['dias_prazo']);
        if ($dias_atraso > 90) $critica++;
        elseif ($dias_atraso > 60) $alta++;
        elseif ($dias_atraso > 30) $media++;
        else $baixa++;
    }
}

echo '<tr>';
echo '<td colspan="3" style="padding: 8px; background-color: #6f42c1; color: white; text-align: center; font-weight: bold;">CRÍTICA (>90 dias)</td>';
echo '<td colspan="3" style="padding: 8px; background-color: #dc3545; color: white; text-align: center; font-weight: bold;">ALTA (61-90 dias)</td>';
echo '<td colspan="4" style="padding: 8px; background-color: #fd7e14; color: white; text-align: center; font-weight: bold;">MÉDIA (31-60 dias)</td>';
echo '<td colspan="3" style="padding: 8px; background-color: #28a745; color: white; text-align: center; font-weight: bold;">BAIXA (≤30 dias)</td>';
echo '<td colspan="3" style="padding: 8px; background-color: #6c757d; color: white; text-align: center; font-weight: bold;">TOTAL</td>';
echo '</tr>';

echo '<tr>';
echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #6f42c1;">' . $critica . '</td>';
echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #dc3545;">' . $alta . '</td>';
echo '<td colspan="4" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #fd7e14;">' . $media . '</td>';
echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #28a745;">' . $baixa . '</td>';
echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #6c757d;">' . $total_cursos_vencidos . '</td>';
echo '</tr>';

echo '</table>';
?>
