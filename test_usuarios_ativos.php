<?php
/**
 * Teste para verificar se o filtro de usuários ativos está funcionando
 * e se o status de bloqueio está sendo exibido corretamente
 */

require_once 'config/config.php';
require_once 'classes/IntranetAPI.php';

// Criar instância da API
$api = new IntranetAPI();

echo "<h1>🧪 Teste de Usuários Ativos e Status de Bloqueio</h1>\n";

// Teste 1: Comparar todos os usuários vs apenas ativos
echo "<h2>1. 📊 Comparação: Todos vs Apenas Ativos</h2>\n";

try {
    // Buscar todos os usuários
    $todos_usuarios = $api->listarUsuarios(false, false);
    $usuarios_ativos = $api->listarUsuariosAtivos(false);
    
    if ($todos_usuarios !== false && $usuarios_ativos !== false) {
        echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>📈 Estatísticas:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Total de usuários na API:</strong> " . count($todos_usuarios) . "</li>";
        echo "<li><strong>Usuários ativos (status=1):</strong> " . count($usuarios_ativos) . "</li>";
        echo "<li><strong>Usuários filtrados:</strong> " . (count($todos_usuarios) - count($usuarios_ativos)) . "</li>";
        echo "</ul>";
        echo "</div>";
        
        // Análise de status
        $status_count = [];
        foreach ($todos_usuarios as $usuario) {
            $status = $usuario['status'] ?? 'indefinido';
            $status_count[$status] = ($status_count[$status] ?? 0) + 1;
        }
        
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>📋 Distribuição por Status:</strong></p>";
        echo "<ul>";
        foreach ($status_count as $status => $count) {
            $cor = $status == 1 ? '#28a745' : ($status == 2 ? '#dc3545' : '#6c757d');
            $texto = $status == 1 ? 'Ativo' : ($status == 2 ? 'Inativo' : 'Indefinido');
            echo "<li style='color: $cor;'><strong>Status $status ($texto):</strong> $count usuários</li>";
        }
        echo "</ul>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>❌ <strong>Erro ao buscar usuários da API</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 2: Verificar status de bloqueio
echo "<h2>2. 🔒 Análise de Status de Bloqueio</h2>\n";

try {
    $usuarios_ativos = $api->listarUsuariosAtivos(false);
    
    if ($usuarios_ativos !== false) {
        // Análise de bloqueio
        $bloqueio_count = [];
        foreach ($usuarios_ativos as $usuario) {
            $bloqueado = $usuario['bloqueado'] ?? 'indefinido';
            $bloqueio_count[$bloqueado] = ($bloqueio_count[$bloqueado] ?? 0) + 1;
        }
        
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>🔐 Distribuição por Status de Bloqueio (apenas usuários ativos):</strong></p>";
        echo "<ul>";
        foreach ($bloqueio_count as $bloqueado => $count) {
            $info = '';
            $cor = '#6c757d';
            
            switch ($bloqueado) {
                case 0:
                    $info = 'Sem Bloqueio';
                    $cor = '#28a745';
                    break;
                case 1:
                    $info = 'Bloqueado';
                    $cor = '#dc3545';
                    break;
                case 3:
                    $info = 'Bloqueio Agendado';
                    $cor = '#fd7e14';
                    break;
                default:
                    $info = 'Status Desconhecido';
                    break;
            }
            
            echo "<li style='color: $cor;'><strong>Bloqueado $bloqueado ($info):</strong> $count usuários</li>";
        }
        echo "</ul>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>❌ <strong>Erro ao buscar usuários ativos</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 3: Exibir exemplos de usuários ativos com status de bloqueio
echo "<h2>3. 👥 Exemplos de Usuários Ativos</h2>\n";

try {
    $usuarios_ativos = $api->listarUsuariosAtivos(false);
    
    if ($usuarios_ativos !== false && count($usuarios_ativos) > 0) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>✅ Primeiros 5 usuários ativos com status de bloqueio:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 5px;'>Nome</th>";
        echo "<th style='padding: 5px;'>CPF</th>";
        echo "<th style='padding: 5px;'>Status</th>";
        echo "<th style='padding: 5px;'>Bloqueio</th>";
        echo "<th style='padding: 5px;'>Badge</th>";
        echo "</tr>";
        
        $count = 0;
        foreach ($usuarios_ativos as $usuario) {
            if ($count >= 5) break;
            
            $status_bloqueio = $usuario['status_bloqueio'] ?? null;
            
            echo "<tr>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($usuario['nome'] ?? 'N/A', 0, 20)) . "...</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($usuario['cpf'] ?? '', 0, 3)) . "***</td>";
            echo "<td style='padding: 5px; color: #28a745; font-weight: bold;'>Ativo</td>";
            
            if ($status_bloqueio) {
                echo "<td style='padding: 5px;'>" . htmlspecialchars($status_bloqueio['texto']) . "</td>";
                echo "<td style='padding: 5px;'>";
                echo "<span style='background: ";
                switch ($status_bloqueio['classe']) {
                    case 'success': echo '#28a745'; break;
                    case 'danger': echo '#dc3545'; break;
                    case 'warning': echo '#fd7e14'; break;
                    default: echo '#6c757d'; break;
                }
                echo "; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;'>";
                echo "<i class='fas " . $status_bloqueio['icone'] . "'></i> ";
                echo htmlspecialchars($status_bloqueio['texto']);
                echo "</span>";
                echo "</td>";
            } else {
                echo "<td style='padding: 5px; color: #dc3545;'>Erro</td>";
                echo "<td style='padding: 5px; color: #dc3545;'>Status não processado</td>";
            }
            
            echo "</tr>";
            $count++;
        }
        echo "</table>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>❌ <strong>Nenhum usuário ativo encontrado</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 4: Verificar método buscarUsuarioPorCpf com filtro de ativos
echo "<h2>4. 🔍 Teste de Busca por CPF (Apenas Ativos)</h2>\n";

try {
    // Pegar um CPF de exemplo dos usuários ativos
    $usuarios_ativos = $api->listarUsuariosAtivos(false);
    
    if ($usuarios_ativos !== false && count($usuarios_ativos) > 0) {
        $usuario_exemplo = $usuarios_ativos[0];
        $cpf_exemplo = $usuario_exemplo['cpf'] ?? '';
        
        if (!empty($cpf_exemplo)) {
            echo "<div style='background: #e2e3e5; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p><strong>🔍 Testando busca por CPF:</strong> " . substr($cpf_exemplo, 0, 3) . ".***.***-**</p>";
            
            // Buscar sem filtro de ativos
            $usuario_sem_filtro = $api->buscarUsuarioPorCpf($cpf_exemplo, false);
            
            // Buscar apenas entre ativos
            $usuario_com_filtro = $api->buscarUsuarioPorCpf($cpf_exemplo, true);
            
            echo "<ul>";
            echo "<li><strong>Busca sem filtro:</strong> " . ($usuario_sem_filtro ? '✅ Encontrado' : '❌ Não encontrado') . "</li>";
            echo "<li><strong>Busca apenas ativos:</strong> " . ($usuario_com_filtro ? '✅ Encontrado' : '❌ Não encontrado') . "</li>";
            echo "</ul>";
            
            if ($usuario_com_filtro && isset($usuario_com_filtro['status_bloqueio'])) {
                $status_bloqueio = $usuario_com_filtro['status_bloqueio'];
                echo "<p><strong>Status de bloqueio:</strong> ";
                echo "<span style='color: ";
                switch ($status_bloqueio['classe']) {
                    case 'success': echo '#28a745'; break;
                    case 'danger': echo '#dc3545'; break;
                    case 'warning': echo '#fd7e14'; break;
                    default: echo '#6c757d'; break;
                }
                echo ";'>";
                echo htmlspecialchars($status_bloqueio['texto']) . " - " . htmlspecialchars($status_bloqueio['descricao']);
                echo "</span></p>";
            }
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Resumo final
echo "<h2>5. 📋 Resumo das Implementações</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔧 Modificações Implementadas</h3>";

echo "<h4>✅ Filtro de Usuários Ativos:</h4>";
echo "<ul>";
echo "<li><strong>Método listarUsuariosAtivos():</strong> Retorna apenas usuários com status = 1</li>";
echo "<li><strong>Parâmetro apenasAtivos:</strong> Adicionado ao método listarUsuarios()</li>";
echo "<li><strong>Cache separado:</strong> Cache diferente para usuários ativos vs todos</li>";
echo "</ul>";

echo "<h4>✅ Status de Bloqueio:</h4>";
echo "<ul>";
echo "<li><strong>Campo status_bloqueio:</strong> Adicionado automaticamente aos usuários</li>";
echo "<li><strong>Códigos suportados:</strong> 0=Sem Bloqueio, 1=Bloqueado, 3=Bloqueio Agendado</li>";
echo "<li><strong>Informações incluídas:</strong> texto, classe CSS, ícone, descrição</li>";
echo "</ul>";

echo "<h4>✅ Arquivos Atualizados:</h4>";
echo "<ul>";
echo "<li><strong>classes/IntranetAPI.php:</strong> Métodos principais</li>";
echo "<li><strong>analise_colaboradores.php:</strong> Cards com status de bloqueio</li>";
echo "<li><strong>detalhes_colaborador.php:</strong> Página de detalhes</li>";
echo "<li><strong>Arquivos de relatórios:</strong> Filtro aplicado</li>";
echo "<li><strong>Arquivos AJAX:</strong> Filtro aplicado</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>👥 Testar Sistema Atualizado</a>";
echo "</p>";
?>
