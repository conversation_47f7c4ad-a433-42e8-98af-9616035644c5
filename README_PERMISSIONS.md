# Sistema de Permissões - Educação Corporativa

## 📋 Visão Geral

Este sistema de permissões é **específico** do projeto de Educação Corporativa e **NÃO interfere** com as permissões globais do sistema.

## 🔐 Características Principais

### ✅ Isolamento Completo
- **Tabela própria**: `edu_permissoes_usuarios`
- **Classe específica**: `EduPermissions`
- **Autenticação própria**: `edu_auth_check.php`
- **Logs identificados**: "Educação Corporativa"

### ✅ Níveis de Acesso
- **ADMIN**: Acesso total, incluindo gerenciamento de permissões
- **GESTOR**: Gerenciamento de trilhas e relatórios avançados
- **COMUM**: Acesso básico (importar dados, visualizar análises)

### ✅ Fallback de Segurança
- Administradores globais (ID 1) têm acesso automático
- Sistema não quebra se houver problemas com permissões específicas

## 🚀 Configuração Inicial

### 1. Executar Setup (apenas uma vez)
```
http://localhost/d/rh/educacao-corporativa/setup_permissions.php
```

### 2. Verificar Criação da Tabela
```sql
DESCRIBE edu_permissoes_usuarios;
```

### 3. Confirmar Permissão de Admin
```sql
SELECT * FROM edu_permissoes_usuarios WHERE nivel_acesso = 'ADMIN';
```

## 📁 Estrutura de Arquivos

```
rh/educacao-corporativa/
├── classes/
│   └── EduPermissions.php          # Classe de permissões específica
├── sql/
│   └── create_permissions_table.sql # Script SQL da tabela
├── edu_auth_check.php              # Autenticação específica
├── permissions.php                 # Gerenciamento de permissões

├── setup_permissions.php           # Configuração inicial
├── test_access.php                 # Página de testes
└── README_PERMISSIONS.md           # Esta documentação

/ (raiz do projeto)
└── access_denied_edu.php           # Página de acesso negado específica
```

## 🛠️ Como Usar

### Gerenciar Permissões
1. Acesse: `permissions.php`
2. Visualize usuários com acesso ao projeto
3. Edite níveis de acesso conforme necessário
4. Remova acesso quando necessário

### Gerenciar Usuários
1. Acesse: `permissions.php`
2. Visualize TODOS os usuários (com e sem acesso)
3. Use filtros para encontrar usuários específicos
4. Clique em "Editar" para conceder/alterar permissões
5. Clique em "Remover" para retirar acesso

### Testar Permissões
1. Acesse: `test_access.php`
2. Selecione diferentes usuários
3. Verifique permissões e acessos
4. Teste páginas do projeto

## 🔒 Segurança

### Verificações Implementadas
- ✅ Validação de níveis de acesso
- ✅ Prevenção de duplicação de permissões
- ✅ Logs detalhados de todas as ações
- ✅ Soft delete (desativação em vez de exclusão)
- ✅ Foreign keys para integridade referencial

### Controle de Acesso
```php
// Verificar acesso básico ao projeto
if (!$edu_permissions->hasAccess()) {
    // Redirecionar para acesso negado
}

// Verificar permissão específica
if (!$edu_permissions->canManagePermissions()) {
    // Redirecionar para acesso negado
}
```

## 📊 Monitoramento

### Logs
Todas as ações são registradas com o padrão:
```
"Educação Corporativa - [Ação]: [Detalhes]"
```

### Auditoria
- **Criação**: `data_criacao`, `usuario_criacao`
- **Atualização**: `data_atualizacao`, `usuario_atualizacao`
- **Status**: `ativo` (TRUE/FALSE)

## 🚨 Importante

### ⚠️ NÃO Afeta Sistema Global
- As permissões deste projeto são **independentes**
- Usuários mantêm suas permissões globais intactas
- Sistema global continua funcionando normalmente

### ⚠️ Administradores Globais
- Usuários com `nivel_acesso_id = 1` têm acesso automático
- Não aparecem na lista de usuários sem acesso
- Não podem ter acesso removido via interface

### ⚠️ Backup e Recuperação
- Faça backup da tabela `edu_permissoes_usuarios`
- Em caso de problemas, administradores globais sempre têm acesso
- Logs permitem rastrear todas as alterações

## 🔧 Troubleshooting

### Problema: Usuário não consegue acessar
1. Verificar se tem registro em `edu_permissoes_usuarios`
2. Verificar se `ativo = TRUE`
3. Verificar se é administrador global
4. Usar `test_access.php` para diagnóstico

### Problema: Erro de permissão
1. Verificar se tabela foi criada corretamente
2. Executar `setup_permissions.php` novamente
3. Verificar logs de erro do PHP
4. Verificar conexão com banco de dados

### Problema: Interface não carrega
1. Verificar se `edu_auth_check.php` está sendo incluído
2. Verificar se classe `EduPermissions` existe
3. Verificar permissões de arquivo no servidor
4. Verificar logs de erro do servidor

## 📞 Suporte

Para problemas ou dúvidas:
1. Verificar logs do sistema
2. Usar página de testes (`test_access.php`)
3. Consultar esta documentação
4. Contatar administrador do sistema

---

**Versão**: 1.0  
**Data**: <?php echo date('d/m/Y'); ?>  
**Autor**: Sistema Sicoob  
**Projeto**: Educação Corporativa
