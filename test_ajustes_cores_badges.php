<?php
/**
 * Teste dos Ajustes de Cores dos Badges
 * 
 * Verificar se os ajustes de cores para identidade visual Sicoob foram implementados corretamente.
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

echo "<h1>🔧 Teste dos Ajustes de Cores dos Badges</h1>";

echo "<h2>1. ✅ Ajustes Implementados</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎨 Cores da Identidade Visual Sicoob Aplicadas:</h3>";

echo "<h4>Badges Ajustados:</h4>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 8px;'>Local</th>";
echo "<th style='padding: 8px;'>Badge</th>";
echo "<th style='padding: 8px;'>Cor Anterior</th>";
echo "<th style='padding: 8px;'>Nova Cor Sicoob</th>";
echo "<th style='padding: 8px;'>Classe CSS</th>";
echo "</tr>";
echo "<tr><td style='padding: 8px;'>Card Principal</td><td style='padding: 8px;'>Em Andamento</td><td style='padding: 8px;'>bg-info (azul)</td><td style='padding: 8px;'>Turquesa Sicoob</td><td style='padding: 8px;'>bg-sicoob-turquesa</td></tr>";
echo "<tr><td style='padding: 8px;'>Card Principal</td><td style='padding: 8px;'>Em Dia</td><td style='padding: 8px;'>bg-success (verde)</td><td style='padding: 8px;'>Verde Claro Sicoob</td><td style='padding: 8px;'>bg-sicoob-verde-claro</td></tr>";
echo "<tr><td style='padding: 8px;'>Modal Trilhas</td><td style='padding: 8px;'>Aprovada</td><td style='padding: 8px;'>bg-primary (azul)</td><td style='padding: 8px;'>Verde Claro Sicoob</td><td style='padding: 8px;'>bg-sicoob-verde-claro</td></tr>";
echo "</table>";

echo "<h4>Cores da Identidade Visual Sicoob:</h4>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>";

echo "<div style='background: #003641; color: white; padding: 10px; border-radius: 5px; text-align: center;'>";
echo "<strong>Verde Escuro</strong><br>";
echo "<code>#003641</code><br>";
echo "<small>--sicoob-verde-escuro</small>";
echo "</div>";

echo "<div style='background: #00AE9D; color: white; padding: 10px; border-radius: 5px; text-align: center;'>";
echo "<strong>Turquesa</strong><br>";
echo "<code>#00AE9D</code><br>";
echo "<small>--sicoob-turquesa</small>";
echo "</div>";

echo "<div style='background: #C9D200; color: #003641; padding: 10px; border-radius: 5px; text-align: center;'>";
echo "<strong>Verde Claro</strong><br>";
echo "<code>#C9D200</code><br>";
echo "<small>--sicoob-verde-claro</small>";
echo "</div>";

echo "<div style='background: #FFFFFF; color: #003641; padding: 10px; border-radius: 5px; text-align: center; border: 1px solid #ddd;'>";
echo "<strong>Branco</strong><br>";
echo "<code>#FFFFFF</code><br>";
echo "<small>--sicoob-branco</small>";
echo "</div>";

echo "<div style='background: #58595B; color: white; padding: 10px; border-radius: 5px; text-align: center;'>";
echo "<strong>Cinza</strong><br>";
echo "<code>#58595B</code><br>";
echo "<small>--sicoob-cinza</small>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "<h2>2. 🎯 Implementação Técnica</h2>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>⚙️ Mudanças Realizadas:</h3>";

echo "<h4>1. Card Principal do Colaborador (analise_colaboradores.php):</h4>";
echo "<pre style='background: #ffffff; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
// ANTES
} elseif (\$tem_em_andamento) {
    return ['status' => 'em_andamento', 'texto' => 'Em Andamento', 'classe' => 'info'];
} elseif (\$cursos_concluidos > 0) {
    return ['status' => 'em_dia', 'texto' => 'Em Dia', 'classe' => 'success'];

// DEPOIS
} elseif (\$tem_em_andamento) {
    return ['status' => 'em_andamento', 'texto' => 'Em Andamento', 'classe' => 'sicoob-turquesa'];
} elseif (\$cursos_concluidos > 0) {
    return ['status' => 'em_dia', 'texto' => 'Em Dia', 'classe' => 'sicoob-verde-claro'];
");
echo "</pre>";

echo "<h4>2. Classes CSS Adicionadas (analise_colaboradores.php):</h4>";
echo "<pre style='background: #ffffff; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
/* Badges personalizados com cores da identidade visual Sicoob */
.bg-sicoob-turquesa {
    background-color: var(--sicoob-turquesa) !important;
    color: var(--sicoob-branco) !important;
}

.bg-sicoob-verde-claro {
    background-color: var(--sicoob-verde-claro) !important;
    color: var(--sicoob-verde-escuro) !important;
    font-weight: 600;
}
");
echo "</pre>";

echo "<h4>3. Badge Trilha Aprovada (detalhes_colaborador.php):</h4>";
echo "<pre style='background: #ffffff; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
// ANTES
<?php elseif (\$trilha['aprovado_trilha'] === 'Sim'): ?>
<span class=\"badge bg-primary\">Aprovada</span>

// DEPOIS
<?php elseif (\$trilha['aprovado_trilha'] === 'Sim'): ?>
<span class=\"badge bg-sicoob-verde-claro\">Aprovada</span>
");
echo "</pre>";

echo "<h4>4. CSS Adicionado (detalhes_colaborador.php):</h4>";
echo "<pre style='background: #ffffff; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
echo '<style>
:root {
    --sicoob-verde-escuro: #003641;
    --sicoob-verde-medio: #00AE9D;
    --sicoob-verde-claro: #C9D200;
    --sicoob-turquesa: #00AE9D;
    --sicoob-branco: #FFFFFF;
    --sicoob-cinza: #58595B;
}

.bg-sicoob-turquesa {
    background-color: var(--sicoob-turquesa) !important;
    color: var(--sicoob-branco) !important;
}

.bg-sicoob-verde-claro {
    background-color: var(--sicoob-verde-claro) !important;
    color: var(--sicoob-verde-escuro) !important;
    font-weight: 600;
}
</style>';
");
echo "</pre>";
echo "</div>";

echo "<h2>3. 🎨 Demonstração Visual</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>👁️ Como Ficaram os Badges:</h3>";

echo "<h4>Badges do Card Principal:</h4>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<div style='display: flex; gap: 15px; flex-wrap: wrap; align-items: center;'>";

echo "<div style='text-align: center;'>";
echo "<div style='background: #dc3545; color: white; padding: 8px 16px; border-radius: 20px; font-size: 0.75rem; margin-bottom: 5px;'>Cursos Vencidos</div>";
echo "<small>Vermelho (mantido)</small>";
echo "</div>";

echo "<div style='text-align: center;'>";
echo "<div style='background: #ffc107; color: #212529; padding: 8px 16px; border-radius: 20px; font-size: 0.75rem; margin-bottom: 5px;'>A Vencer</div>";
echo "<small>Amarelo (mantido)</small>";
echo "</div>";

echo "<div style='text-align: center;'>";
echo "<div style='background: #00AE9D; color: white; padding: 8px 16px; border-radius: 20px; font-size: 0.75rem; margin-bottom: 5px;'>Em Andamento</div>";
echo "<small>Turquesa Sicoob (novo)</small>";
echo "</div>";

echo "<div style='text-align: center;'>";
echo "<div style='background: #C9D200; color: #003641; padding: 8px 16px; border-radius: 20px; font-size: 0.75rem; font-weight: 600; margin-bottom: 5px;'>Em Dia</div>";
echo "<small>Verde Claro Sicoob (novo)</small>";
echo "</div>";

echo "<div style='text-align: center;'>";
echo "<div style='background: #6c757d; color: white; padding: 8px 16px; border-radius: 20px; font-size: 0.75rem; margin-bottom: 5px;'>Pendente</div>";
echo "<small>Cinza (mantido)</small>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<h4>Badge da Trilha no Modal:</h4>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<div style='display: flex; gap: 15px; flex-wrap: wrap; align-items: center;'>";

echo "<div style='text-align: center;'>";
echo "<div style='background: #28a745; color: white; padding: 6px 12px; border-radius: 4px; font-size: 0.875rem; margin-bottom: 5px;'>Concluída</div>";
echo "<small>Verde (mantido)</small>";
echo "</div>";

echo "<div style='text-align: center;'>";
echo "<div style='background: #C9D200; color: #003641; padding: 6px 12px; border-radius: 4px; font-size: 0.875rem; font-weight: 600; margin-bottom: 5px;'>Aprovada</div>";
echo "<small>Verde Claro Sicoob (novo)</small>";
echo "</div>";

echo "<div style='text-align: center;'>";
echo "<div style='background: #6c757d; color: white; padding: 6px 12px; border-radius: 4px; font-size: 0.875rem; margin-bottom: 5px;'>Em Andamento</div>";
echo "<small>Cinza (mantido)</small>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "<h2>4. 🧪 Como Testar os Ajustes</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📋 Passos para Verificação:</h3>";

echo "<h4>Teste 1: Badges do Card Principal</h4>";
echo "<ol>";
echo "<li><strong>Acesse:</strong> analise_colaboradores.php</li>";
echo "<li><strong>Procure:</strong> Colaboradores com status \"Em Andamento\"</li>";
echo "<li><strong>Verifique:</strong> Badge deve estar na cor turquesa Sicoob (#00AE9D)</li>";
echo "<li><strong>Procure:</strong> Colaboradores com status \"Em Dia\"</li>";
echo "<li><strong>Verifique:</strong> Badge deve estar na cor verde claro Sicoob (#C9D200)</li>";
echo "</ol>";

echo "<h4>Teste 2: Badge da Trilha no Modal</h4>";
echo "<ol>";
echo "<li><strong>Clique:</strong> \"Ver Detalhes\" de um colaborador</li>";
echo "<li><strong>Vá para:</strong> Seção \"Trilhas de Aprendizagem\"</li>";
echo "<li><strong>Procure:</strong> Trilhas com status \"Aprovada\"</li>";
echo "<li><strong>Verifique:</strong> Badge deve estar na cor verde claro Sicoob (#C9D200)</li>";
echo "<li><strong>Compare:</strong> Texto deve estar em verde escuro (#003641) para contraste</li>";
echo "</ol>";

echo "<h4>Teste 3: Contraste e Legibilidade</h4>";
echo "<ol>";
echo "<li><strong>Verifique:</strong> Texto dos badges está legível</li>";
echo "<li><strong>Confirme:</strong> Contraste adequado entre fundo e texto</li>";
echo "<li><strong>Teste:</strong> Diferentes tamanhos de tela</li>";
echo "<li><strong>Valide:</strong> Consistência visual com identidade Sicoob</li>";
echo "</ol>";

echo "<h4>Teste 4: Responsividade</h4>";
echo "<ol>";
echo "<li><strong>Teste:</strong> Desktop → Tablet → Mobile</li>";
echo "<li><strong>Verifique:</strong> Badges mantêm cores corretas</li>";
echo "<li><strong>Confirme:</strong> Texto permanece legível</li>";
echo "<li><strong>Valide:</strong> Layout não quebra</li>";
echo "</ol>";
echo "</div>";

echo "<h2>5. 🎯 Benefícios dos Ajustes</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🚀 Melhorias Alcançadas:</h3>";

echo "<h4>✅ Identidade Visual:</h4>";
echo "<ul>";
echo "<li><strong>Consistência:</strong> Cores alinhadas com identidade visual Sicoob</li>";
echo "<li><strong>Profissionalismo:</strong> Interface mais coesa e institucional</li>";
echo "<li><strong>Reconhecimento:</strong> Usuários identificam facilmente a marca</li>";
echo "<li><strong>Padronização:</strong> Seguimento das diretrizes visuais</li>";
echo "</ul>";

echo "<h4>✅ Experiência do Usuário:</h4>";
echo "<ul>";
echo "<li><strong>Familiaridade:</strong> Cores conhecidas pelos usuários Sicoob</li>";
echo "<li><strong>Confiança:</strong> Interface alinhada com expectativas da marca</li>";
echo "<li><strong>Legibilidade:</strong> Contraste adequado para boa leitura</li>";
echo "<li><strong>Harmonia:</strong> Paleta de cores equilibrada</li>";
echo "</ul>";

echo "<h4>✅ Aspectos Técnicos:</h4>";
echo "<ul>";
echo "<li><strong>Variáveis CSS:</strong> Uso de custom properties para fácil manutenção</li>";
echo "<li><strong>Reutilização:</strong> Classes CSS podem ser usadas em outros componentes</li>";
echo "<li><strong>Flexibilidade:</strong> Fácil alteração de cores se necessário</li>";
echo "<li><strong>Performance:</strong> CSS otimizado sem impacto na velocidade</li>";
echo "</ul>";
echo "</div>";

echo "<h2>6. 📋 Resumo dos Ajustes</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Ajustes de Cores Implementados com Sucesso</h3>";

echo "<h4>✅ Badges Ajustados:</h4>";
echo "<ul>";
echo "<li><strong>\"Em Andamento\":</strong> bg-info → bg-sicoob-turquesa (#00AE9D)</li>";
echo "<li><strong>\"Em Dia\":</strong> bg-success → bg-sicoob-verde-claro (#C9D200)</li>";
echo "<li><strong>\"Aprovada\":</strong> bg-primary → bg-sicoob-verde-claro (#C9D200)</li>";
echo "</ul>";

echo "<h4>✅ Arquivos Modificados:</h4>";
echo "<ul>";
echo "<li><strong>analise_colaboradores.php:</strong> Função calcularStatusColaborador + CSS</li>";
echo "<li><strong>detalhes_colaborador.php:</strong> Badge trilha aprovada + CSS</li>";
echo "</ul>";

echo "<h4>✅ CSS Implementado:</h4>";
echo "<ul>";
echo "<li><strong>Variáveis CSS:</strong> Cores da identidade visual Sicoob</li>";
echo "<li><strong>Classes Personalizadas:</strong> bg-sicoob-turquesa e bg-sicoob-verde-claro</li>";
echo "<li><strong>Contraste:</strong> Cores de texto adequadas para legibilidade</li>";
echo "<li><strong>Peso da Fonte:</strong> font-weight: 600 para destaque</li>";
echo "</ul>";

echo "<h4>🚀 Resultado Final:</h4>";
echo "<ul>";
echo "<li><strong>Interface Sicoob:</strong> Cores alinhadas com identidade visual</li>";
echo "<li><strong>Experiência Consistente:</strong> Badges harmonizados com marca</li>";
echo "<li><strong>Profissionalismo:</strong> Sistema mais institucional e confiável</li>";
echo "<li><strong>Facilidade de Manutenção:</strong> CSS organizado e reutilizável</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php' style='background: #00AE9D; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🎨 Testar Cards</a>";
echo "<a href='detalhes_colaborador.php?cpf=12345678901' style='background: #C9D200; color: #003641; padding: 10px 15px; text-decoration: none; border-radius: 5px; font-weight: 600;'>🏆 Testar Modal</a>";
echo "</p>";
?>
