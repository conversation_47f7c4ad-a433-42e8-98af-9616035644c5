<?php
require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'classes/EmailManager.php';

// Verificar permissões
checkPageAccess(['gestor', 'admin']);

header('Content-Type: application/json');

try {
    $emailManager = new EmailManager();
    
    // Buscar estatísticas da fila
    $stmt = $pdo_edu->prepare("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'pendente' THEN 1 ELSE 0 END) as pendentes,
            SUM(CASE WHEN status = 'processando' THEN 1 ELSE 0 END) as processando,
            SUM(CASE WHEN status = 'erro' OR tentativas >= max_tentativas THEN 1 ELSE 0 END) as erros
        FROM edu_email_fila
        WHERE status IN ('pendente', 'processando', 'erro')
    ");
    $stmt->execute();
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Buscar próximos itens a serem processados
    $stmt = $pdo_edu->prepare("
        SELECT 
            f.id,
            f.status,
            f.data_agendamento,
            e.destinatario_nome,
            e.destinatario_email
        FROM edu_email_fila f
        JOIN edu_email_envios e ON f.envio_id = e.id
        WHERE f.status = 'pendente'
        AND f.data_agendamento <= NOW()
        ORDER BY f.prioridade ASC, f.data_agendamento ASC
        LIMIT 5
    ");
    $stmt->execute();
    $proximos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'stats' => $stats,
        'proximos' => $proximos
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
