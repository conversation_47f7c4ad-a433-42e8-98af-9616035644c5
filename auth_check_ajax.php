<?php
// Verificação de autenticação específica para requisições AJAX
session_start();
require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../classes/Permissions.php';
require_once __DIR__ . '/config/config.php';

// Criar alias para compatibilidade com código que usa $pdo_edu
$pdo_edu = $pdo;

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Usuário não autenticado']);
    exit;
}

// Criar instância de Permissions
$permissions = new Permissions($pdo, $_SESSION['user_id']);

// Função para verificar acesso à página (versão AJAX)
function checkPageAccessAjax($required_level) {
    global $permissions;
    
    switch ($required_level) {
        case 'admin':
            if (!$permissions->isAdmin()) {
                http_response_code(403);
                echo json_encode(['success' => false, 'message' => 'Acesso negado - Apenas administradores']);
                exit;
            }
            break;
        case 'gestor':
            if (!$permissions->isGestor() && !$permissions->isAdmin()) {
                http_response_code(403);
                echo json_encode(['success' => false, 'message' => 'Acesso negado - Apenas gestores e administradores']);
                exit;
            }
            break;
    }
}

// Obter informações do usuário logado
try {
    $stmt = $pdo->prepare("SELECT id, username, nome_completo, email FROM usuarios WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        session_destroy();
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Usuário não encontrado']);
        exit;
    }
} catch (PDOException $e) {
    error_log('Erro ao buscar dados do usuário: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Erro interno do servidor']);
    exit;
}
?>
