<?php
require_once 'edu_auth_check.php';
require_once 'config/database.php';

// Redirecionar automaticamente para análise de colaboradores
header('Location: analise_colaboradores.php');
exit;

// Verificar se as tabelas existem
$tables_exist = true;
try {
    $pdo_edu->query("SELECT 1 FROM edu_relatorio_educacao LIMIT 1");
    $pdo_edu->query("SELECT 1 FROM edu_importacoes LIMIT 1");
} catch (PDOException $e) {
    $tables_exist = false;
}

// Buscar estatísticas básicas se as tabelas existem
$stats = [
    'total_registros' => 0,
    'ultima_importacao' => null,
    'total_importacoes' => 0
];

if ($tables_exist) {
    try {
        // Total de registros
        $stmt = $pdo_edu->query("SELECT COUNT(*) as total FROM edu_relatorio_educacao");
        $stats['total_registros'] = $stmt->fetch()['total'];
        
        // Última importação
        $stmt = $pdo_edu->query("SELECT data_importacao FROM edu_importacoes ORDER BY data_importacao DESC LIMIT 1");
        $result = $stmt->fetch();
        if ($result) {
            $stats['ultima_importacao'] = $result['data_importacao'];
        }
        
        // Total de importações
        $stmt = $pdo_edu->query("SELECT COUNT(*) as total FROM edu_importacoes");
        $stats['total_importacoes'] = $stmt->fetch()['total'];
        
    } catch (PDOException $e) {
        error_log('Erro ao buscar estatísticas: ' . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo EDU_PROJECT_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;
            --sicoob-roxo: #49479D;
            --sicoob-branco: #FFFFFF;
            --sicoob-cinza: #58595B;
        }

        body {
            background-color: #f8f9fa;
        }



        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: var(--sicoob-branco);
            border-radius: 12px 12px 0 0 !important;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--sicoob-turquesa), var(--sicoob-verde-claro));
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 174, 157, 0.3);
        }

        .btn-success {
            background-color: var(--sicoob-verde-claro);
            border-color: var(--sicoob-verde-claro);
            color: var(--sicoob-verde-escuro);
            font-weight: 600;
        }

        .btn-success:hover {
            background-color: #b8c200;
            border-color: #b8c200;
            color: var(--sicoob-verde-escuro);
        }

        .stats-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-left: 4px solid var(--sicoob-turquesa);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--sicoob-verde-escuro);
        }

        .stats-label {
            color: var(--sicoob-cinza);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .welcome-section {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: var(--sicoob-branco);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--sicoob-turquesa), var(--sicoob-verde-claro));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: var(--sicoob-verde-escuro);
            font-size: 1.5rem;
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <div class="container mt-4">
        <!-- Seção de Boas-vindas -->
        <div class="welcome-section">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-3">
                        <i class="fas fa-graduation-cap me-3"></i>
                        Sistema de Educação Corporativa
                    </h1>
                    <p class="lead mb-0">
                        Gerencie e acompanhe os dados de educação corporativa da sua organização.
                        Importe relatórios, visualize estatísticas e monitore o progresso dos colaboradores.
                    </p>
                </div>
                <div class="col-md-4 text-center">
                    <i class="fas fa-chart-line" style="font-size: 4rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>

        <?php if (!$tables_exist): ?>
        <!-- Alerta de instalação -->
        <div class="alert alert-warning" role="alert">
            <h4 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>Sistema não instalado</h4>
            <p>As tabelas do sistema ainda não foram criadas. Execute o script de instalação primeiro.</p>
            <hr>
            <p class="mb-0">Execute o arquivo <code>sql/install.sql</code> no banco de dados para criar as tabelas necessárias.</p>
        </div>
        <?php else: ?>
        
        <!-- Estatísticas -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($stats['total_registros']); ?></div>
                        <div class="stats-label">Total de Registros</div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number"><?php echo number_format($stats['total_importacoes']); ?></div>
                        <div class="stats-label">Importações Realizadas</div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-number">
                            <?php 
                            if ($stats['ultima_importacao']) {
                                echo date('d/m', strtotime($stats['ultima_importacao']));
                            } else {
                                echo '-';
                            }
                            ?>
                        </div>
                        <div class="stats-label">Última Importação</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ações Principais -->
        <div class="row">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="fas fa-file-upload"></i>
                        </div>
                        <h5 class="card-title">Importar Relatório</h5>
                        <p class="card-text">
                            Faça upload do arquivo CSV com os dados de educação corporativa.
                            O sistema irá processar e armazenar todas as informações.
                        </p>
                        <a href="importar.php" class="btn btn-primary">
                            <i class="fas fa-upload me-2"></i>Importar Agora
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h5 class="card-title">Relatórios e Análises</h5>
                        <p class="card-text">
                            Visualize relatórios detalhados, estatísticas e análises dos dados
                            de educação corporativa importados.
                        </p>
                        <button class="btn btn-success" disabled>
                            <i class="fas fa-chart-line me-2"></i>Em Breve
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
