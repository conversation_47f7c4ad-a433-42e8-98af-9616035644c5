<?php
// Relatório de Colaboradores com Cursos Aprovados
// Este arquivo gera um relatório específico de colaboradores que possuem cursos aprovados

// Construir query base
$where_conditions = [];
$params = [];

if (!empty($filtros['trilha'])) {
    $where_conditions[] = "trilha LIKE ?";
    $params[] = '%' . $filtros['trilha'] . '%';
}

if (!empty($filtros['funcao'])) {
    $where_conditions[] = "funcao = ?";
    $params[] = $filtros['funcao'];
}

// Query para buscar todos os colaboradores
$colaboradores_query = "
    SELECT
        cpf,
        MAX(usuario) as usuario,
        MAX(email) as email,
        MAX(funcao) as funcao,
        MAX(codigo_unidade) as codigo_unidade,
        MAX(data_admissao) as data_admissao,
        MAX(superior_imediato) as superior_imediato,
        COUNT(DISTINCT trilha) as total_trilhas,
        COUNT(DISTINCT CONCAT(codigo_trilha, '|', codigo_recurso)) as total_cursos,
        MAX(data_importacao) as ultima_atualizacao
    FROM edu_relatorio_educacao
    " . (!empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "") . "
    GROUP BY cpf
    ORDER BY MAX(usuario)
";

$stmt_colaboradores = $pdo_edu->prepare($colaboradores_query);
$stmt_colaboradores->execute($params);
$todos_colaboradores_bruto = $stmt_colaboradores->fetchAll();

// APLICAR FILTRO DE USUÁRIOS INATIVOS
$todos_colaboradores = aplicarFiltroUsuariosInativos($todos_colaboradores_bruto, $mapa_usuarios_ativos, $mapa_usuarios_todos);

// Filtrar colaboradores que possuem cursos aprovados
$colaboradores_com_aprovados = [];

foreach ($todos_colaboradores as $colaborador) {
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;

    // Filtrar por PA se especificado
    if (!empty($filtros['pa'])) {
        if (!$usuario_intranet || $usuario_intranet['agencia'] != $filtros['pa']) {
            continue;
        }
    }

    $cursos_colaborador = buscarCursosColaborador($colaborador['cpf'], $pdo_edu, $prazos_config);
    $cursos_aprovados = [];
    $tem_curso_no_periodo = true;

    // Verificar filtro de período se especificado
    if (!empty($filtros['periodo'])) {
        $tem_curso_no_periodo = false;
        foreach ($cursos_colaborador as $curso) {
            if (!empty($curso['prazo_calculado'])) {
                $prazo_curso = new DateTime($curso['prazo_calculado']);
                if (verificarCursoNoPeriodo($prazo_curso, $filtros['periodo'])) {
                    $tem_curso_no_periodo = true;
                    break;
                }
            }
        }
    }

    if (!$tem_curso_no_periodo) continue;

    // Buscar cursos aprovados
    foreach ($cursos_colaborador as $curso) {
        if ($curso['aprovacao'] === 'Sim') {
            $cursos_aprovados[] = $curso;
        }
    }

    if (!empty($cursos_aprovados)) {
        $colaborador['cursos_aprovados'] = $cursos_aprovados;
        $colaborador['total_aprovados'] = count($cursos_aprovados);

        // Calcular média de notas e aproveitamento
        $notas = array_filter(array_column($cursos_aprovados, 'nota_recurso'));
        $aproveitamentos = array_filter(array_column($cursos_aprovados, 'aproveitamento'));

        $colaborador['media_notas'] = !empty($notas) ? array_sum($notas) / count($notas) : null;
        $colaborador['media_aproveitamento'] = !empty($aproveitamentos) ? array_sum($aproveitamentos) / count($aproveitamentos) : null;

        $colaboradores_com_aprovados[] = $colaborador;
    }
}

// Ordenar por quantidade de cursos aprovados (maior primeiro)
usort($colaboradores_com_aprovados, function($a, $b) {
    return $b['total_aprovados'] - $a['total_aprovados'];
});

// Cabeçalho do relatório
echo '<table border="1" style="border-collapse: collapse;">';
echo '<tr style="background-color: #28a745; color: white; font-weight: bold;">';
echo '<td colspan="16" style="padding: 10px; text-align: center; font-size: 16px;">';
echo 'RELATÓRIO DE COLABORADORES COM CURSOS APROVADOS';
echo '</td>';
echo '</tr>';

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td style="padding: 8px;">Gerado em:</td>';
echo '<td colspan="15" style="padding: 8px;">' . date('d/m/Y H:i:s') . '</td>';
echo '</tr>';

if (!empty($filtros['trilha']) || !empty($filtros['funcao']) || !empty($filtros['periodo']) || !empty($filtros['pa'])) {
    echo '<tr style="background-color: #e9ecef;">';
    echo '<td style="padding: 8px; font-weight: bold;">Filtros Aplicados:</td>';
    echo '<td colspan="15" style="padding: 8px;">';
    $filtros_texto = [];
    if (!empty($filtros['trilha'])) $filtros_texto[] = "Trilha: " . $filtros['trilha'];
    if (!empty($filtros['funcao'])) $filtros_texto[] = "Função: " . $filtros['funcao'];
    if (!empty($filtros['periodo'])) $filtros_texto[] = "Período: " . $filtros['periodo'];
    if (!empty($filtros['pa'])) {
        $agencia_nome = isset($mapa_agencias[$filtros['pa']]) ?
            $mapa_agencias[$filtros['pa']]['numero'] . ' - ' . $mapa_agencias[$filtros['pa']]['nome'] :
            $filtros['pa'];
        $filtros_texto[] = "PA: " . $agencia_nome;
    }
    echo implode(' | ', $filtros_texto);
    echo '</td>';
    echo '</tr>';
}

echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Colaboradores com Cursos Aprovados:</td>';
echo '<td colspan="15" style="padding: 8px; color: #28a745; font-weight: bold;">' . count($colaboradores_com_aprovados) . '</td>';
echo '</tr>';

$total_cursos_aprovados = array_sum(array_column($colaboradores_com_aprovados, 'total_aprovados'));
echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Total de Cursos Aprovados:</td>';
echo '<td colspan="15" style="padding: 8px; color: #28a745; font-weight: bold;">' . $total_cursos_aprovados . '</td>';
echo '</tr>';

echo '<tr><td colspan="16" style="padding: 5px;"></td></tr>'; // Espaçamento

// Cabeçalhos das colunas
echo '<tr style="background-color: #28a745; color: white; font-weight: bold;">';
echo '<td style="padding: 8px; text-align: center;">CPF</td>';
echo '<td style="padding: 8px; text-align: center;">Nome</td>';
echo '<td style="padding: 8px; text-align: center;">E-mail</td>';
echo '<td style="padding: 8px; text-align: center;">Função</td>';
echo '<td style="padding: 8px; text-align: center;">PA/Agência</td>';
echo '<td style="padding: 8px; text-align: center;">Setor</td>';
echo '<td style="padding: 8px; text-align: center;">Superior Imediato</td>';
echo '<td style="padding: 8px; text-align: center;">Qtd Aprovados</td>';
echo '<td style="padding: 8px; text-align: center;">Média Notas</td>';
echo '<td style="padding: 8px; text-align: center;">Média Aproveitamento</td>';
echo '<td style="padding: 8px; text-align: center;">Trilha</td>';
echo '<td style="padding: 8px; text-align: center;">Curso Aprovado</td>';
echo '<td style="padding: 8px; text-align: center;">Nota</td>';
echo '<td style="padding: 8px; text-align: center;">Aproveitamento</td>';
echo '<td style="padding: 8px; text-align: center;">Data Conclusão</td>';
echo '<td style="padding: 8px; text-align: center;">Carga Horária</td>';
echo '</tr>';

// Dados dos colaboradores com cursos aprovados
$linha = 0;
foreach ($colaboradores_com_aprovados as $colaborador) {
    $linha++;
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;

    // Informações da intranet
    $nome_exibir = $usuario_intranet['nome'] ?? $colaborador['usuario'];
    $email_exibir = $usuario_intranet['email'] ?? $colaborador['email'] ?? 'N/A';
    $funcao_exibir = $usuario_intranet['nomeFuncao'] ?? $colaborador['funcao'] ?? 'N/A';
    $setor_exibir = $usuario_intranet['nomeSetor'] ?? 'N/A';

    // Informações da agência
    $agencia_info = 'N/A';
    if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
        $agencia_id = $usuario_intranet['agencia'];
        if (isset($mapa_agencias[$agencia_id])) {
            $agencia_data = $mapa_agencias[$agencia_id];
            $agencia_info = $agencia_data['numero'] . ' - ' . $agencia_data['nome'];
        } else {
            $agencia_info = $agencia_id;
        }
    }

    // Ordenar cursos aprovados por nota (maior primeiro)
    $cursos_aprovados = $colaborador['cursos_aprovados'];
    usort($cursos_aprovados, function($a, $b) {
        $nota_a = $a['nota_recurso'] ?? 0;
        $nota_b = $b['nota_recurso'] ?? 0;
        return $nota_b - $nota_a;
    });

    foreach ($cursos_aprovados as $curso) {
        $cor_linha = ($linha % 2 == 0) ? '#f0f8f0' : '#ffffff';
        echo '<tr style="background-color: ' . $cor_linha . ';">';

        // Repetir dados do colaborador em todas as linhas para compatibilidade com Excel
        echo '<td style="padding: 6px;">' . formatarCpf($colaborador['cpf']) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($nome_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($email_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($funcao_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($agencia_info) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($setor_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($colaborador['superior_imediato'] ?? 'N/A') . '</td>';
        echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: #28a745;">' . $colaborador['total_aprovados'] . '</td>';
        echo '<td style="padding: 6px; text-align: center; font-weight: bold;">' .
             ($colaborador['media_notas'] ? number_format($colaborador['media_notas'], 1) : 'N/A') . '</td>';
        echo '<td style="padding: 6px; text-align: center; font-weight: bold;">' .
             ($colaborador['media_aproveitamento'] ? number_format($colaborador['media_aproveitamento'], 1) . '%' : 'N/A') . '</td>';

        // Dados do curso aprovado
        echo '<td style="padding: 6px;">' . htmlspecialchars($curso['trilha']) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($curso['recurso']) . '</td>';

        // Nota
        $nota_texto = 'N/A';
        $cor_nota = '#6c757d';
        if ($curso['nota_recurso']) {
            $nota = floatval($curso['nota_recurso']);
            $nota_texto = number_format($nota, 1);

            if ($nota >= 9.0) {
                $cor_nota = '#28a745'; // Verde - excelente
            } elseif ($nota >= 8.0) {
                $cor_nota = '#20c997'; // Verde claro - muito bom
            } elseif ($nota >= 7.0) {
                $cor_nota = '#ffc107'; // Amarelo - bom
            } elseif ($nota >= 6.0) {
                $cor_nota = '#fd7e14'; // Laranja - regular
            } else {
                $cor_nota = '#dc3545'; // Vermelho - baixo
            }
        }
        echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: ' . $cor_nota . ';">' . $nota_texto . '</td>';

        // Aproveitamento
        $aproveitamento_texto = 'N/A';
        $cor_aproveitamento = '#6c757d';
        if ($curso['aproveitamento']) {
            $aproveitamento = floatval($curso['aproveitamento']);
            $aproveitamento_texto = number_format($aproveitamento, 1) . '%';

            if ($aproveitamento >= 90) {
                $cor_aproveitamento = '#28a745'; // Verde - excelente
            } elseif ($aproveitamento >= 80) {
                $cor_aproveitamento = '#20c997'; // Verde claro - muito bom
            } elseif ($aproveitamento >= 70) {
                $cor_aproveitamento = '#ffc107'; // Amarelo - bom
            } elseif ($aproveitamento >= 60) {
                $cor_aproveitamento = '#fd7e14'; // Laranja - regular
            } else {
                $cor_aproveitamento = '#dc3545'; // Vermelho - baixo
            }
        }
        echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: ' . $cor_aproveitamento . ';">' . $aproveitamento_texto . '</td>';

        // Data de conclusão
        echo '<td style="padding: 6px; text-align: center;">' .
             ($curso['data_conclusao'] && $curso['data_conclusao'] !== '0000-00-00' ?
              date('d/m/Y', strtotime($curso['data_conclusao'])) : 'N/A') . '</td>';

        // Carga horária
        echo '<td style="padding: 6px; text-align: center;">' .
             ($curso['carga_horaria_recurso'] ?? 'N/A') . '</td>';

        echo '</tr>';
    }
}

// Resumo por performance
echo '<tr><td colspan="16" style="padding: 10px;"></td></tr>'; // Espaçamento

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td colspan="16" style="padding: 10px; text-align: center; font-size: 14px;">RESUMO POR PERFORMANCE</td>';
echo '</tr>';

// Calcular estatísticas de performance
$excelente = 0; $muito_bom = 0; $bom = 0; $regular = 0; $baixo = 0;
$total_com_nota = 0;

foreach ($colaboradores_com_aprovados as $colaborador) {
    foreach ($colaborador['cursos_aprovados'] as $curso) {
        if ($curso['nota_recurso']) {
            $total_com_nota++;
            $nota = floatval($curso['nota_recurso']);

            if ($nota >= 9.0) $excelente++;
            elseif ($nota >= 8.0) $muito_bom++;
            elseif ($nota >= 7.0) $bom++;
            elseif ($nota >= 6.0) $regular++;
            else $baixo++;
        }
    }
}

echo '<tr>';
echo '<td colspan="3" style="padding: 8px; background-color: #28a745; color: white; text-align: center; font-weight: bold;">EXCELENTE (≥9.0)</td>';
echo '<td colspan="3" style="padding: 8px; background-color: #20c997; color: white; text-align: center; font-weight: bold;">MUITO BOM (8.0-8.9)</td>';
echo '<td colspan="3" style="padding: 8px; background-color: #ffc107; color: black; text-align: center; font-weight: bold;">BOM (7.0-7.9)</td>';
echo '<td colspan="3" style="padding: 8px; background-color: #fd7e14; color: white; text-align: center; font-weight: bold;">REGULAR (6.0-6.9)</td>';
echo '<td colspan="4" style="padding: 8px; background-color: #dc3545; color: white; text-align: center; font-weight: bold;">BAIXO (<6.0)</td>';
echo '</tr>';

echo '<tr>';
echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #28a745;">' . $excelente . '</td>';
echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #20c997;">' . $muito_bom . '</td>';
echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #ffc107;">' . $bom . '</td>';
echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #fd7e14;">' . $regular . '</td>';
echo '<td colspan="4" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #dc3545;">' . $baixo . '</td>';
echo '</tr>';

if ($total_com_nota > 0) {
    echo '<tr>';
    echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 12px; color: #28a745;">(' . number_format(($excelente/$total_com_nota)*100, 1) . '%)</td>';
    echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 12px; color: #20c997;">(' . number_format(($muito_bom/$total_com_nota)*100, 1) . '%)</td>';
    echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 12px; color: #ffc107;">(' . number_format(($bom/$total_com_nota)*100, 1) . '%)</td>';
    echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 12px; color: #fd7e14;">(' . number_format(($regular/$total_com_nota)*100, 1) . '%)</td>';
    echo '<td colspan="4" style="padding: 8px; text-align: center; font-size: 12px; color: #dc3545;">(' . number_format(($baixo/$total_com_nota)*100, 1) . '%)</td>';
    echo '</tr>';
}

echo '</table>';
?>
