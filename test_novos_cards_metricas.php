<?php
/**
 * Teste dos Novos Cards de Métricas
 * 
 * Verificar se os novos cards de métricas foram implementados corretamente.
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

echo "<h1>🔧 Teste dos Novos Cards de Métricas</h1>";

// Teste 1: Verificar cards adicionados
echo "<h2>1. ✅ Novos Cards Implementados</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📊 Cards de Métricas Atualizados:</h3>";

echo "<h4>ANTES (5 cards):</h4>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 8px;'>Posição</th>";
echo "<th style='padding: 8px;'>Card</th>";
echo "<th style='padding: 8px;'>Métrica</th>";
echo "</tr>";
echo "<tr><td style='padding: 8px;'>1</td><td style='padding: 8px;'>Total de Colaboradores</td><td style='padding: 8px;'>306</td></tr>";
echo "<tr><td style='padding: 8px;'>2</td><td style='padding: 8px;'>Trilhas Disponíveis</td><td style='padding: 8px;'>105</td></tr>";
echo "<tr><td style='padding: 8px;'>3</td><td style='padding: 8px;'>Cursos Cadastrados</td><td style='padding: 8px;'>400</td></tr>";
echo "<tr><td style='padding: 8px;'>4</td><td style='padding: 8px;'>Cursos Aprovados</td><td style='padding: 8px;'>14,334</td></tr>";
echo "<tr><td style='padding: 8px;'>5</td><td style='padding: 8px;'>Média de Aproveitamento</td><td style='padding: 8px;'>87.4%</td></tr>";
echo "</table>";

echo "<h4>DEPOIS (8 cards):</h4>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 8px;'>Posição</th>";
echo "<th style='padding: 8px;'>Card</th>";
echo "<th style='padding: 8px;'>Métrica</th>";
echo "<th style='padding: 8px;'>Status</th>";
echo "</tr>";
echo "<tr><td style='padding: 8px;'>1</td><td style='padding: 8px;'>Total de Colaboradores</td><td style='padding: 8px;'>\$estatisticas['total_colaboradores']</td><td style='padding: 8px;'>✅ Mantido</td></tr>";
echo "<tr><td style='padding: 8px;'>2</td><td style='padding: 8px;'>Trilhas Disponíveis</td><td style='padding: 8px;'>\$estatisticas['total_trilhas']</td><td style='padding: 8px;'>✅ Mantido</td></tr>";
echo "<tr><td style='padding: 8px;'>3</td><td style='padding: 8px;'>Cursos Cadastrados</td><td style='padding: 8px;'>\$estatisticas['total_cursos']</td><td style='padding: 8px;'>✅ Mantido</td></tr>";
echo "<tr style='background: #d4edda;'><td style='padding: 8px;'>4</td><td style='padding: 8px;'><strong>Atribuições</strong></td><td style='padding: 8px;'>\$estatisticas['total_atribuicoes']</td><td style='padding: 8px;'>🆕 <strong>NOVO</strong></td></tr>";
echo "<tr><td style='padding: 8px;'>5</td><td style='padding: 8px;'>Cursos Aprovados</td><td style='padding: 8px;'>\$estatisticas['cursos_aprovados']</td><td style='padding: 8px;'>✅ Mantido</td></tr>";
echo "<tr style='background: #d4edda;'><td style='padding: 8px;'>6</td><td style='padding: 8px;'><strong>Cursos em Andamento</strong></td><td style='padding: 8px;'>\$estatisticas['total_em_andamento']</td><td style='padding: 8px;'>🆕 <strong>NOVO</strong></td></tr>";
echo "<tr style='background: #d4edda;'><td style='padding: 8px;'>7</td><td style='padding: 8px;'><strong>Cursos Vencidos</strong></td><td style='padding: 8px;'>\$estatisticas['total_vencidos']</td><td style='padding: 8px;'>🆕 <strong>NOVO</strong></td></tr>";
echo "<tr><td style='padding: 8px;'>8</td><td style='padding: 8px;'>Média de Aproveitamento</td><td style='padding: 8px;'>\$estatisticas['media_aproveitamento']</td><td style='padding: 8px;'>✅ Mantido</td></tr>";
echo "</table>";
echo "</div>";

// Teste 2: Verificar cálculos das métricas
echo "<h2>2. ✅ Cálculos das Novas Métricas</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔢 Lógica de Cálculo Implementada:</h3>";

echo "<h4>1. Atribuições (total_atribuicoes):</h4>";
echo "<pre style='background: #ffffff; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
// Calcular atribuições (total de cursos atribuídos)
\$total_atribuicoes += \$colaborador['total_cursos'];

Descrição: Soma de todos os cursos atribuídos a todos os colaboradores únicos.
Exemplo: Se colaborador A tem 5 cursos e colaborador B tem 3 cursos = 8 atribuições.
");
echo "</pre>";

echo "<h4>2. Cursos em Andamento (total_em_andamento):</h4>";
echo "<pre style='background: #ffffff; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
// Calcular cursos em andamento
\$cursos_colaborador = buscarCursosColaborador(\$colaborador['cpf'], \$pdo_edu, \$prazos_config);
\$em_andamento = 0;

foreach (\$cursos_colaborador as \$curso) {
    // Curso em andamento: tem andamento_etapa mas não está aprovado, vencido ou a vencer
    if (!empty(\$curso['andamento_etapa']) && 
        \$curso['aprovacao'] !== 'Sim' && 
        \$curso['status_prazo'] !== 'vencido' && 
        \$curso['status_prazo'] !== 'a_vencer') {
        \$em_andamento++;
    }
}

Descrição: Cursos que têm progresso (andamento_etapa) mas não estão finalizados.
Critérios: Tem andamento_etapa E não aprovado E não vencido E não a vencer.
");
echo "</pre>";

echo "<h4>3. Cursos Vencidos (total_vencidos):</h4>";
echo "<pre style='background: #ffffff; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
// Usar cálculo já existente dos colaboradores únicos
\$total_vencidos_colaboradores += \$colaborador['cursos_vencidos'];

Descrição: Soma de todos os cursos vencidos de todos os colaboradores únicos.
Base: Usa o cálculo de prazos personalizados já implementado.
");
echo "</pre>";
echo "</div>";

// Teste 3: Verificar responsividade
echo "<h2>3. ✅ Responsividade dos Cards</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📱 Classes Bootstrap Atualizadas:</h3>";

echo "<h4>Distribuição Responsiva (8 cards):</h4>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 8px;'>Breakpoint</th>";
echo "<th style='padding: 8px;'>Classe</th>";
echo "<th style='padding: 8px;'>Cards por Linha</th>";
echo "<th style='padding: 8px;'>Observação</th>";
echo "</tr>";
echo "<tr><td style='padding: 8px;'>XXL (≥1400px)</td><td style='padding: 8px;'>col-xxl-3</td><td style='padding: 8px;'>4 cards</td><td style='padding: 8px;'>2 linhas de 4</td></tr>";
echo "<tr><td style='padding: 8px;'>XL (≥1200px)</td><td style='padding: 8px;'>col-xl-4</td><td style='padding: 8px;'>3 cards</td><td style='padding: 8px;'>3 linhas: 3+3+2</td></tr>";
echo "<tr><td style='padding: 8px;'>LG (≥992px)</td><td style='padding: 8px;'>col-lg-6</td><td style='padding: 8px;'>2 cards</td><td style='padding: 8px;'>4 linhas de 2</td></tr>";
echo "<tr><td style='padding: 8px;'>MD (≥768px)</td><td style='padding: 8px;'>col-md-6</td><td style='padding: 8px;'>2 cards</td><td style='padding: 8px;'>4 linhas de 2</td></tr>";
echo "<tr><td style='padding: 8px;'>SM (<768px)</td><td style='padding: 8px;'>-</td><td style='padding: 8px;'>1 card</td><td style='padding: 8px;'>8 linhas de 1</td></tr>";
echo "</table>";

echo "<h4>Estrutura HTML:</h4>";
echo "<pre style='background: #ffffff; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
<div class=\"col-xxl-3 col-xl-4 col-lg-6 col-md-6 mb-3\">
    <div class=\"card stats-card\">
        <div class=\"card-body text-center\">
            <div class=\"stats-number\"><?php echo number_format(\$valor); ?></div>
            <div class=\"stats-label\">Label do Card</div>
        </div>
    </div>
</div>
");
echo "</pre>";

echo "<h4>Benefícios da Nova Distribuição:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Telas Grandes (XXL):</strong> 4 cards por linha, layout compacto</li>";
echo "<li>✅ <strong>Telas Médias (XL):</strong> 3 cards por linha, bem distribuído</li>";
echo "<li>✅ <strong>Tablets (LG/MD):</strong> 2 cards por linha, legível</li>";
echo "<li>✅ <strong>Mobile (SM):</strong> 1 card por linha, fácil leitura</li>";
echo "</ul>";
echo "</div>";

// Teste 4: Verificar integração com filtros
echo "<h2>4. ✅ Integração com Filtros</h2>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔄 Comportamento com Filtros:</h3>";

echo "<h4>Métricas que Atualizam com Filtros:</h4>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 8px;'>Card</th>";
echo "<th style='padding: 8px;'>Atualiza com Filtro</th>";
echo "<th style='padding: 8px;'>Observação</th>";
echo "</tr>";
echo "<tr><td style='padding: 8px;'>Total de Colaboradores</td><td style='padding: 8px;'>✅ Sim</td><td style='padding: 8px;'>Baseado em colaboradores filtrados</td></tr>";
echo "<tr><td style='padding: 8px;'>Trilhas Disponíveis</td><td style='padding: 8px;'>❌ Não</td><td style='padding: 8px;'>Valor fixo do sistema</td></tr>";
echo "<tr><td style='padding: 8px;'>Cursos Cadastrados</td><td style='padding: 8px;'>❌ Não</td><td style='padding: 8px;'>Valor fixo do sistema</td></tr>";
echo "<tr><td style='padding: 8px;'>Atribuições</td><td style='padding: 8px;'>✅ Sim</td><td style='padding: 8px;'>Soma dos cursos dos colaboradores filtrados</td></tr>";
echo "<tr><td style='padding: 8px;'>Cursos Aprovados</td><td style='padding: 8px;'>❌ Não</td><td style='padding: 8px;'>Valor fixo do sistema</td></tr>";
echo "<tr><td style='padding: 8px;'>Cursos em Andamento</td><td style='padding: 8px;'>✅ Sim</td><td style='padding: 8px;'>Calculado dos colaboradores filtrados</td></tr>";
echo "<tr><td style='padding: 8px;'>Cursos Vencidos</td><td style='padding: 8px;'>✅ Sim</td><td style='padding: 8px;'>Soma dos vencidos dos colaboradores filtrados</td></tr>";
echo "<tr><td style='padding: 8px;'>Média de Aproveitamento</td><td style='padding: 8px;'>❌ Não</td><td style='padding: 8px;'>Valor fixo do sistema</td></tr>";
echo "</table>";

echo "<h4>Exemplos de Comportamento:</h4>";
echo "<ul>";
echo "<li><strong>Filtro por PA:</strong> Atribuições, Em Andamento e Vencidos mostram apenas do PA selecionado</li>";
echo "<li><strong>Filtro por Status 'Vencido':</strong> Total de Colaboradores mostra apenas quem tem cursos vencidos</li>";
echo "<li><strong>Filtro por Trilha:</strong> Métricas refletem apenas colaboradores dessa trilha</li>";
echo "<li><strong>Múltiplos Filtros:</strong> Métricas refletem intersecção de todos os filtros</li>";
echo "</ul>";
echo "</div>";

// Teste 5: Como testar os novos cards
echo "<h2>5. 🧪 Como Testar os Novos Cards</h2>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📋 Passos para Verificação:</h3>";

echo "<h4>Teste 1: Visualização dos Cards</h4>";
echo "<ol>";
echo "<li><strong>Acesse:</strong> analise_colaboradores.php</li>";
echo "<li><strong>Observe:</strong> Seção de cards no topo da página</li>";
echo "<li><strong>Conte:</strong> Deve haver 8 cards no total</li>";
echo "<li><strong>Verifique:</strong> Ordem correta dos cards</li>";
echo "<li><strong>Confirme:</strong> Novos cards: Atribuições, Em Andamento, Vencidos</li>";
echo "</ol>";

echo "<h4>Teste 2: Responsividade</h4>";
echo "<ol>";
echo "<li><strong>Redimensione:</strong> Janela do navegador</li>";
echo "<li><strong>Observe:</strong> Cards se reorganizam conforme breakpoints</li>";
echo "<li><strong>Teste:</strong> Desktop (4 por linha), Tablet (2 por linha), Mobile (1 por linha)</li>";
echo "<li><strong>Confirme:</strong> Layout sempre legível</li>";
echo "</ol>";

echo "<h4>Teste 3: Valores das Métricas</h4>";
echo "<ol>";
echo "<li><strong>Anote:</strong> Valores dos novos cards</li>";
echo "<li><strong>Compare:</strong> Atribuições deve ser ≥ outros valores</li>";
echo "<li><strong>Verifique:</strong> Em Andamento + Vencidos ≤ Atribuições</li>";
echo "<li><strong>Confirme:</strong> Números fazem sentido</li>";
echo "</ol>";

echo "<h4>Teste 4: Filtros</h4>";
echo "<ol>";
echo "<li><strong>Aplique:</strong> Filtro por PA específico</li>";
echo "<li><strong>Observe:</strong> Atribuições, Em Andamento e Vencidos diminuem</li>";
echo "<li><strong>Aplique:</strong> Filtro por Status 'Vencido'</li>";
echo "<li><strong>Confirme:</strong> Total de Colaboradores e Vencidos são consistentes</li>";
echo "</ol>";
echo "</div>";

// Resumo final
echo "<h2>6. 📋 Resumo dos Novos Cards</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Implementação Completa</h3>";

echo "<h4>✅ Cards Adicionados:</h4>";
echo "<ul>";
echo "<li><strong>Atribuições:</strong> Total de cursos atribuídos a todos os colaboradores</li>";
echo "<li><strong>Cursos em Andamento:</strong> Cursos com progresso mas não finalizados</li>";
echo "<li><strong>Cursos Vencidos:</strong> Cursos que passaram do prazo</li>";
echo "</ul>";

echo "<h4>✅ Posicionamento Correto:</h4>";
echo "<ul>";
echo "<li><strong>Atribuições:</strong> Após 'Cursos Cadastrados' (posição 4)</li>";
echo "<li><strong>Em Andamento:</strong> Após 'Cursos Aprovados' (posição 6)</li>";
echo "<li><strong>Vencidos:</strong> Após 'Em Andamento' (posição 7)</li>";
echo "</ul>";

echo "<h4>✅ Características Técnicas:</h4>";
echo "<ul>";
echo "<li><strong>Responsividade:</strong> 8 cards bem distribuídos</li>";
echo "<li><strong>Cálculos Precisos:</strong> Baseados em colaboradores únicos</li>";
echo "<li><strong>Integração com Filtros:</strong> Métricas dinâmicas atualizam</li>";
echo "<li><strong>Performance:</strong> Cálculos eficientes</li>";
echo "</ul>";

echo "<h4>🚀 Benefícios:</h4>";
echo "<ul>";
echo "<li><strong>Visão Completa:</strong> Métricas abrangentes do sistema</li>";
echo "<li><strong>Análise Detalhada:</strong> Separação clara entre status</li>";
echo "<li><strong>Monitoramento:</strong> Fácil identificação de problemas</li>";
echo "<li><strong>Usabilidade:</strong> Interface rica em informações</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📊 Ver Novos Cards</a>";
echo "<a href='analise_colaboradores.php?pa=88 - UAD' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🔍 Testar com Filtro</a>";
echo "</p>";
?>
