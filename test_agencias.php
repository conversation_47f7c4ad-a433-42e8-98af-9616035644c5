<?php
/**
 * Teste de Relacionamento de Agências
 * 
 * Este arquivo testa o relacionamento entre usuários e agências via API.
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'classes/IntranetAPI.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

echo "<h1>🏢 Teste de Relacionamento de Agências</h1>";

// Teste 1: Verificar API de agências
echo "<h2>1. 🏛️ Verificação da API de Agências</h2>";

try {
    $api = new IntranetAPI();
    $agencias_intranet = $api->listarAgencias();
    
    if ($agencias_intranet !== false) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>API de Agências Funcionando:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Total de agências:</strong> " . count($agencias_intranet) . "</li>";
        echo "</ul>";
        
        echo "<p><strong>Primeiras 5 agências:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 5px;'>ID</th>";
        echo "<th style='padding: 5px;'>Número</th>";
        echo "<th style='padding: 5px;'>Nome</th>";
        echo "</tr>";
        
        for ($i = 0; $i < min(5, count($agencias_intranet)); $i++) {
            $agencia = $agencias_intranet[$i];
            echo "<tr>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($agencia['id'] ?? 'N/A') . "</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($agencia['numero'] ?? 'N/A') . "</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($agencia['nome'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>❌ <strong>Erro na API de Agências</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro na API de Agências:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 2: Verificar API de usuários
echo "<h2>2. 👥 Verificação da API de Usuários</h2>";

try {
    $api = new IntranetAPI();
    $usuarios_intranet = $api->listarUsuarios();
    
    if ($usuarios_intranet !== false) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>API de Usuários Funcionando:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Total de usuários:</strong> " . count($usuarios_intranet) . "</li>";
        echo "</ul>";
        
        echo "<p><strong>Primeiros 3 usuários com agência:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 5px;'>Nome</th>";
        echo "<th style='padding: 5px;'>CPF</th>";
        echo "<th style='padding: 5px;'>Agência ID</th>";
        echo "<th style='padding: 5px;'>Setor</th>";
        echo "</tr>";
        
        $count = 0;
        foreach ($usuarios_intranet as $usuario) {
            if ($count >= 3) break;
            if (!empty($usuario['agencia'])) {
                echo "<tr>";
                echo "<td style='padding: 5px;'>" . htmlspecialchars($usuario['nome'] ?? 'N/A') . "</td>";
                echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($usuario['cpf'] ?? '', 0, 3)) . "***</td>";
                echo "<td style='padding: 5px;'>" . htmlspecialchars($usuario['agencia']) . "</td>";
                echo "<td style='padding: 5px;'>" . htmlspecialchars($usuario['nomeSetor'] ?? 'N/A') . "</td>";
                echo "</tr>";
                $count++;
            }
        }
        echo "</table>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>❌ <strong>Erro na API de Usuários</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro na API de Usuários:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 3: Testar relacionamento
echo "<h2>3. 🔗 Teste de Relacionamento Usuário-Agência</h2>";

try {
    $api = new IntranetAPI();
    $agencias_intranet = $api->listarAgencias();
    $usuarios_intranet = $api->listarUsuarios();
    
    if ($agencias_intranet !== false && $usuarios_intranet !== false) {
        // Criar mapa de agências por ID
        $mapa_agencias = [];
        foreach ($agencias_intranet as $agencia) {
            if (!empty($agencia['id'])) {
                $mapa_agencias[$agencia['id']] = $agencia;
            }
        }
        
        echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>Mapa de Agências Criado:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Total de agências mapeadas:</strong> " . count($mapa_agencias) . "</li>";
        echo "</ul>";
        echo "</div>";
        
        // Testar relacionamento com alguns usuários
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>Teste de Relacionamento:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 5px;'>Usuário</th>";
        echo "<th style='padding: 5px;'>Agência ID</th>";
        echo "<th style='padding: 5px;'>Agência Relacionada</th>";
        echo "<th style='padding: 5px;'>Status</th>";
        echo "</tr>";
        
        $count = 0;
        foreach ($usuarios_intranet as $usuario) {
            if ($count >= 5) break;
            if (!empty($usuario['agencia'])) {
                $agencia_id = $usuario['agencia'];
                $agencia_relacionada = 'N/A';
                $status = '❌ Não encontrada';
                
                if (isset($mapa_agencias[$agencia_id])) {
                    $agencia_data = $mapa_agencias[$agencia_id];
                    $agencia_relacionada = $agencia_data['numero'] . ' - ' . $agencia_data['nome'];
                    $status = '✅ Relacionada';
                }
                
                echo "<tr>";
                echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($usuario['nome'] ?? '', 0, 20)) . "...</td>";
                echo "<td style='padding: 5px;'>" . htmlspecialchars($agencia_id) . "</td>";
                echo "<td style='padding: 5px;'>" . htmlspecialchars($agencia_relacionada) . "</td>";
                echo "<td style='padding: 5px;'>$status</td>";
                echo "</tr>";
                $count++;
            }
        }
        echo "</table>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>❌ <strong>Erro: Não foi possível obter dados das APIs</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro no relacionamento:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 4: Simular lógica do card
echo "<h2>4. 🎯 Simulação da Lógica do Card</h2>";

try {
    // Buscar alguns colaboradores do banco
    $query_colaboradores = "
        SELECT cpf, usuario, email, funcao
        FROM edu_relatorio_educacao 
        WHERE cpf IS NOT NULL AND cpf != '' 
        LIMIT 3
    ";
    
    $stmt = $pdo_edu->prepare($query_colaboradores);
    $stmt->execute();
    $colaboradores_teste = $stmt->fetchAll();
    
    if (!empty($colaboradores_teste)) {
        $api = new IntranetAPI();
        $usuarios_intranet = $api->listarUsuarios();
        $agencias_intranet = $api->listarAgencias();
        
        // Criar mapas
        $mapa_usuarios_cpf = [];
        if ($usuarios_intranet !== false) {
            foreach ($usuarios_intranet as $usuario) {
                if (!empty($usuario['cpf'])) {
                    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                    $mapa_usuarios_cpf[$cpf_normalizado] = $usuario;
                }
            }
        }
        
        $mapa_agencias = [];
        if ($agencias_intranet !== false) {
            foreach ($agencias_intranet as $agencia) {
                if (!empty($agencia['id'])) {
                    $mapa_agencias[$agencia['id']] = $agencia;
                }
            }
        }
        
        echo "<div style='background: #e2e3e5; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>Simulação dos Cards:</strong></p>";
        
        foreach ($colaboradores_teste as $colaborador) {
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
            $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;
            
            // Unificar informações
            $nome_exibir = $usuario_intranet['nome'] ?? $colaborador['usuario'];
            $email_exibir = $usuario_intranet['email'] ?? $colaborador['email'] ?? 'N/A';
            $funcao_exibir = $usuario_intranet['nomeFuncao'] ?? $colaborador['funcao'] ?? 'N/A';
            $setor_exibir = $usuario_intranet['nomeSetor'] ?? 'N/A';
            
            // Buscar informações da agência
            $agencia_info = 'N/A';
            if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
                $agencia_id = $usuario_intranet['agencia'];
                if (isset($mapa_agencias[$agencia_id])) {
                    $agencia_data = $mapa_agencias[$agencia_id];
                    $agencia_info = $agencia_data['numero'] . ' - ' . $agencia_data['nome'];
                } else {
                    $agencia_info = $agencia_id; // Fallback
                }
            }
            
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<h6><strong>$nome_exibir</strong></h6>";
            echo "<p style='margin: 2px 0;'><strong>CPF:</strong> " . substr($colaborador['cpf'], 0, 3) . ".***.***-**</p>";
            echo "<p style='margin: 2px 0;'><strong>E-mail:</strong> $email_exibir</p>";
            echo "<p style='margin: 2px 0;'><strong>Agência:</strong> $agencia_info</p>";
            echo "<p style='margin: 2px 0;'><strong>Setor:</strong> $setor_exibir</p>";
            echo "<p style='margin: 2px 0;'><strong>Função:</strong> $funcao_exibir</p>";
            echo "<p style='margin: 2px 0;'><strong>Status Intranet:</strong> " . ($usuario_intranet ? '✅ Encontrado' : '❌ Não encontrado') . "</p>";
            echo "</div>";
        }
        echo "</div>";
        
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>⚠️ <strong>Nenhum colaborador encontrado no banco para teste</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro na simulação:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Resumo final
echo "<h2>5. 📋 Resumo das Modificações</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔧 Modificações Implementadas</h3>";

echo "<h4>✅ Unificação de Informações:</h4>";
echo "<ul>";
echo "<li><strong>Nome:</strong> Prioriza nome da Intranet, fallback para nome do sistema</li>";
echo "<li><strong>E-mail:</strong> Prioriza email da Intranet, fallback para email do sistema</li>";
echo "<li><strong>Função:</strong> Prioriza função da Intranet, fallback para função do sistema</li>";
echo "<li><strong>Setor:</strong> Obtido da Intranet (nomeSetor)</li>";
echo "</ul>";

echo "<h4>✅ Relacionamento de Agências:</h4>";
echo "<ul>";
echo "<li><strong>Mapeamento:</strong> Agências mapeadas por ID (não por número)</li>";
echo "<li><strong>Relacionamento:</strong> usuário.agencia → agencia.id</li>";
echo "<li><strong>Exibição:</strong> 'numero - nome' (ex: '88 - UAD')</li>";
echo "<li><strong>Fallback:</strong> Exibe ID da agência se não encontrar relacionamento</li>";
echo "</ul>";

echo "<h4>✅ Layout do Card:</h4>";
echo "<ul>";
echo "<li><strong>Nome:</strong> Unificado no cabeçalho</li>";
echo "<li><strong>CPF:</strong> Mantido com formatação</li>";
echo "<li><strong>E-mail:</strong> Unificado</li>";
echo "<li><strong>Agência:</strong> Formato 'numero - nome'</li>";
echo "<li><strong>Setor:</strong> Da Intranet</li>";
echo "<li><strong>Função:</strong> Unificada</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>👥 Testar Cards Unificados</a>";
echo "</p>";
?>
