<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'edu_auth_check.php';

// Verificar se usuário pode gerenciar importações (apenas gestores)
if (!$edu_permissions->canManageImports()) {
    header('Location: ../../access_denied_edu.php?projeto=educacao-corporativa&nivel=gestor');
    exit;
}

$message = '';
$message_type = '';

// Função para tratar CPF - adicionar zeros à esquerda se necessário
function formatCpf($cpf) {
    if (empty($cpf)) {
        return '';
    }
    
    // Limpar caracteres não numéricos
    $cleaned = preg_replace('/[^0-9]/', '', $cpf);
    
    if (empty($cleaned)) {
        return '';
    }
    
    // Completar com zeros à esquerda até 11 dígitos para CPF
    return str_pad($cleaned, 11, '0', STR_PAD_LEFT);
}

// Função para converter data do formato brasileiro para MySQL
function convertDate($date) {
    if (empty($date)) {
        return null;
    }
    
    // Tentar diferentes formatos de data
    $formats = ['d/m/Y', 'd-m-Y', 'Y-m-d'];
    
    foreach ($formats as $format) {
        $dateObj = DateTime::createFromFormat($format, $date);
        if ($dateObj !== false) {
            return $dateObj->format('Y-m-d');
        }
    }
    
    return null;
}

// Função para limpar valores numéricos
function cleanNumericValue($value) {
    if (empty($value)) {
        return null;
    }

    // Remover caracteres não numéricos exceto vírgula e ponto
    $cleaned = preg_replace('/[^0-9,.]/', '', $value);

    // Converter vírgula para ponto (formato brasileiro)
    $cleaned = str_replace(',', '.', $cleaned);

    return is_numeric($cleaned) ? floatval($cleaned) : null;
}

// Função para tratar campos de carga horária no formato hh:mm
function cleanTimeValue($value) {
    if (empty($value)) {
        return null;
    }

    $value = trim($value);

    // Se já está no formato hh:mm, manter
    if (preg_match('/^\d{1,2}:\d{2}$/', $value)) {
        return $value;
    }

    // Se é um número como "1025", converter para "10:25"
    if (preg_match('/^\d{3,4}$/', $value)) {
        $hours = substr($value, 0, -2);
        $minutes = substr($value, -2);
        return $hours . ':' . $minutes;
    }

    // Se é um número decimal, converter minutos totais para hh:mm
    if (is_numeric($value)) {
        $totalMinutes = intval($value);
        $hours = intval($totalMinutes / 60);
        $minutes = $totalMinutes % 60;
        return sprintf('%d:%02d', $hours, $minutes);
    }

    return $value;
}

// Função para detectar e converter codificação
function detectAndConvertEncoding($text) {
    if (empty($text)) {
        return $text;
    }

    // Lista de codificações para testar
    $encodings = ['UTF-8', 'ISO-8859-1', 'Windows-1252', 'CP1252'];

    // Detectar codificação
    $detected = mb_detect_encoding($text, $encodings, true);

    // Se não for UTF-8, converter
    if ($detected && $detected !== 'UTF-8') {
        return mb_convert_encoding($text, 'UTF-8', $detected);
    }

    return $text;
}

// Função para processar linha do CSV com tratamento de codificação
function processCSVLine($line) {
    if (is_array($line)) {
        return array_map('detectAndConvertEncoding', $line);
    }
    return detectAndConvertEncoding($line);
}

// Remover processamento antigo - agora usa AJAX
if (false && $_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['csv_file'])) {
    try {
        $file = $_FILES['csv_file'];
        
        // Validações básicas
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('Erro no upload do arquivo.');
        }
        
        if ($file['size'] > EDU_MAX_FILE_SIZE) {
            throw new Exception('Arquivo muito grande. Tamanho máximo: ' . (EDU_MAX_FILE_SIZE / 1024 / 1024) . 'MB');
        }
        
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, EDU_ALLOWED_EXTENSIONS)) {
            throw new Exception('Tipo de arquivo não permitido. Use apenas: ' . implode(', ', EDU_ALLOWED_EXTENSIONS));
        }
        
        // Mover arquivo para diretório de uploads
        $filename = uniqid() . '_' . $file['name'];
        $filepath = EDU_UPLOAD_PATH . $filename;
        
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            throw new Exception('Erro ao salvar arquivo.');
        }
        
        // Registrar importação no banco
        $stmt = $pdo_edu->prepare("
            INSERT INTO edu_importacoes (nome_arquivo, tamanho_arquivo, total_registros, usuario_id, status) 
            VALUES (?, ?, 0, ?, 'processando')
        ");
        $stmt->execute([$file['name'], $file['size'], $_SESSION['user_id']]);
        $importacao_id = $pdo_edu->lastInsertId();
        
        // Processar arquivo CSV
        $handle = fopen($filepath, 'r');
        if ($handle === false) {
            throw new Exception('Não foi possível abrir o arquivo CSV.');
        }

        // Definir delimitador (tentar vírgula e ponto e vírgula)
        $delimiter = ',';
        $first_line = fgets($handle);
        rewind($handle);

        // Detectar e converter codificação da primeira linha
        $first_line_converted = detectAndConvertEncoding($first_line);

        if (substr_count($first_line_converted, ';') > substr_count($first_line_converted, ',')) {
            $delimiter = ';';
        }

        // Ler cabeçalho e aplicar tratamento de codificação
        $header = fgetcsv($handle, 0, $delimiter);
        if ($header !== false) {
            $header = processCSVLine($header);
        }
        
        // Contar total de linhas
        $total_rows = 0;
        while (fgetcsv($handle, 0, $delimiter) !== false) {
            $total_rows++;
        }
        rewind($handle);
        fgetcsv($handle, 0, $delimiter); // Pular cabeçalho novamente
        
        // Atualizar total de registros
        $stmt = $pdo_edu->prepare("UPDATE edu_importacoes SET total_registros = ? WHERE id = ?");
        $stmt->execute([$total_rows, $importacao_id]);
        
        // Limpar tabela antes da importação (conforme solicitado)
        $pdo_edu->exec("TRUNCATE TABLE edu_relatorio_educacao");
        
        $imported = 0;
        $errors = 0;
        $error_details = [];
        
        $pdo_edu->beginTransaction();
        
        // Preparar statement de inserção
        $insert_stmt = $pdo_edu->prepare("
            INSERT INTO edu_relatorio_educacao (
                codigo_unidade, hierarquia_unidade, usuario, identificador, situacao_usuario,
                cpf, email, data_admissao, funcao, superior_imediato, tipo_trilha, etapa,
                codigo_trilha, trilha, aprovado_trilha, aproveitamento, situacao_trilha,
                iniciar_trilha_em, concluir_trilha_ate, data_aprovacao_trilha, carga_horaria_trilha,
                prazo_etapa_trilha_jornada, andamento_etapa, total_horas_essenciais_etapa,
                horas_essenciais_feitas, horas_complementares_feitas, codigo_recurso, recurso,
                nota_recurso, aprovacao, carga_horaria_recurso, data_conclusao, validade_recurso,
                responsavel_associacao, usuario_importacao
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $line_number = 1;
        while (($row = fgetcsv($handle, 0, $delimiter)) !== false) {
            $line_number++;

            try {
                // Aplicar tratamento de codificação na linha
                $row = processCSVLine($row);

                // Pular linhas vazias
                if (empty(array_filter($row))) {
                    continue;
                }

                // Mapear dados das colunas (conforme especificação)
                $data = [
                    trim($row[0] ?? ''),  // codigo_unidade
                    trim($row[1] ?? ''),  // hierarquia_unidade
                    trim($row[2] ?? ''),  // usuario
                    trim($row[3] ?? ''),  // identificador
                    trim($row[4] ?? ''),  // situacao_usuario
                    formatCpf($row[5] ?? ''),  // cpf (com tratamento de zeros à esquerda)
                    trim($row[6] ?? ''),  // email
                    convertDate($row[7] ?? ''),  // data_admissao
                    trim($row[8] ?? ''),  // funcao
                    trim($row[9] ?? ''),  // superior_imediato
                    trim($row[10] ?? ''), // tipo_trilha
                    trim($row[11] ?? ''), // etapa
                    trim($row[12] ?? ''), // codigo_trilha
                    trim($row[13] ?? ''), // trilha
                    trim($row[14] ?? ''), // aprovado_trilha
                    cleanNumericValue($row[15] ?? ''), // aproveitamento
                    trim($row[16] ?? ''), // situacao_trilha
                    convertDate($row[17] ?? ''), // iniciar_trilha_em
                    convertDate($row[18] ?? ''), // concluir_trilha_ate
                    convertDate($row[19] ?? ''), // data_aprovacao_trilha
                    cleanTimeValue($row[20] ?? ''), // carga_horaria_trilha (formato hh:mm)
                    trim($row[21] ?? ''), // prazo_etapa_trilha_jornada
                    trim($row[22] ?? ''), // andamento_etapa
                    cleanNumericValue($row[23] ?? ''), // total_horas_essenciais_etapa
                    cleanNumericValue($row[24] ?? ''), // horas_essenciais_feitas
                    cleanNumericValue($row[25] ?? ''), // horas_complementares_feitas
                    trim($row[26] ?? ''), // codigo_recurso
                    trim($row[27] ?? ''), // recurso
                    cleanNumericValue($row[28] ?? ''), // nota_recurso
                    trim($row[29] ?? ''), // aprovacao
                    cleanTimeValue($row[30] ?? ''), // carga_horaria_recurso (formato hh:mm)
                    convertDate($row[31] ?? ''), // data_conclusao
                    convertDate($row[32] ?? ''), // validade_recurso
                    trim($row[33] ?? ''), // responsavel_associacao
                    $_SESSION['user_id'] // usuario_importacao
                ];
                
                // Validação básica - CPF é obrigatório
                if (empty($data[5])) {
                    throw new Exception("CPF é obrigatório");
                }
                
                $insert_stmt->execute($data);
                $imported++;
                
            } catch (Exception $e) {
                $errors++;
                $error_details[] = "Linha $line_number: " . $e->getMessage();
                
                // Limitar número de erros exibidos
                if (count($error_details) >= 10) {
                    $error_details[] = "... e mais " . ($errors - 10) . " erros";
                    break;
                }
            }
        }
        
        fclose($handle);
        
        // Atualizar status da importação
        $status = ($errors == 0) ? 'concluido' : 'erro';
        $detalhes_erro = empty($error_details) ? null : implode("\n", $error_details);
        
        $stmt = $pdo_edu->prepare("
            UPDATE edu_importacoes 
            SET registros_importados = ?, registros_erro = ?, status = ?, detalhes_erro = ?, data_conclusao = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$imported, $errors, $status, $detalhes_erro, $importacao_id]);
        
        if ($errors == 0) {
            $pdo_edu->commit();
            $message = "Importação concluída com sucesso! $imported registros importados.";
            $message_type = 'success';
        } else {
            $pdo_edu->rollBack();
            $message = "Importação concluída com erros. $imported registros importados, $errors erros encontrados.";
            if (!empty($error_details)) {
                $message .= "<br><br><strong>Detalhes dos erros:</strong><br>" . implode('<br>', $error_details);
            }
            $message_type = 'warning';
        }
        
        // Remover arquivo temporário
        unlink($filepath);
        
    } catch (Exception $e) {
        if (isset($pdo_edu) && $pdo_edu->inTransaction()) {
            $pdo_edu->rollBack();
        }
        
        $message = "Erro durante a importação: " . $e->getMessage();
        $message_type = 'danger';
        
        // Remover arquivo se existir
        if (isset($filepath) && file_exists($filepath)) {
            unlink($filepath);
        }
        
        // Atualizar status da importação se foi criada
        if (isset($importacao_id)) {
            try {
                $stmt = $pdo_edu->prepare("
                    UPDATE edu_importacoes 
                    SET status = 'erro', detalhes_erro = ?, data_conclusao = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([$e->getMessage(), $importacao_id]);
            } catch (Exception $update_error) {
                error_log('Erro ao atualizar status da importação: ' . $update_error->getMessage());
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Importar Relatório - <?php echo EDU_PROJECT_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;
            --sicoob-roxo: #49479D;
            --sicoob-branco: #FFFFFF;
            --sicoob-cinza: #58595B;
        }

        body {
            background-color: #f8f9fa;
        }



        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: var(--sicoob-branco);
            border-radius: 12px 12px 0 0 !important;
            border: none;
        }

        .upload-area {
            border: 2px dashed var(--sicoob-turquesa);
            border-radius: 12px;
            padding: 3rem 2rem;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: var(--sicoob-verde-claro);
            background-color: #e9ecef;
        }

        .upload-area.dragover {
            border-color: var(--sicoob-verde-claro);
            background-color: rgba(201, 210, 0, 0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--sicoob-turquesa), var(--sicoob-verde-claro));
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 174, 157, 0.3);
        }

        .btn-secondary {
            background-color: var(--sicoob-cinza);
            border-color: var(--sicoob-cinza);
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
        }

        .progress {
            height: 8px;
            border-radius: 4px;
            background-color: #e9ecef;
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--sicoob-turquesa), var(--sicoob-verde-claro));
        }

        .file-info {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .column-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 1rem;
        }

        .column-list {
            columns: 2;
            column-gap: 2rem;
            list-style: none;
            padding: 0;
        }

        .column-list li {
            break-inside: avoid;
            padding: 0.25rem 0;
            border-bottom: 1px solid #dee2e6;
        }

        .column-list li:last-child {
            border-bottom: none;
        }

        .alert {
            border-radius: 8px;
            border: none;
        }

        .progress-stat {
            padding: 1rem 0;
        }

        .progress-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--sicoob-verde-escuro);
        }

        .progress-label {
            font-size: 0.85rem;
            color: var(--sicoob-cinza);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .progress {
            background-color: #e9ecef;
            border-radius: 8px;
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--sicoob-turquesa), var(--sicoob-verde-claro));
            border-radius: 8px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #progressSection .card {
            border: 2px solid var(--sicoob-turquesa);
            animation: pulse-border 2s infinite;
        }

        @keyframes pulse-border {
            0% { border-color: var(--sicoob-turquesa); }
            50% { border-color: var(--sicoob-verde-claro); }
            100% { border-color: var(--sicoob-turquesa); }
        }

        .result-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border: 1px solid #28a745;
            color: #155724;
        }

        .result-error {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            border: 1px solid #dc3545;
            color: #721c24;
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <div class="container mt-4">
        <!-- Mensagens -->
        <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Formulário de Upload -->
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-csv me-2"></i> Importar Relatório CSV
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data" id="uploadForm">
                            <div class="upload-area" id="uploadArea">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <h5>Arraste o arquivo aqui ou clique para selecionar</h5>
                                <p class="text-muted">Formato aceito: .csv (máximo <?php echo EDU_MAX_FILE_SIZE / 1024 / 1024; ?>MB)</p>
                                <input type="file" name="csv_file" id="csv_file" class="d-none" accept=".csv" required>
                            </div>

                            <div id="fileInfo" class="file-info d-none">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file-csv fa-2x text-primary me-3"></i>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1" id="fileName"></h6>
                                        <small class="text-muted" id="fileSize"></small>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-danger" id="removeFile">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
                                <a href="index.php" class="btn btn-secondary me-md-2">
                                    <i class="fas fa-arrow-left me-2"></i>Voltar
                                </a>
                                <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                                    <i class="fas fa-upload me-2"></i>Importar Arquivo
                                </button>
                            </div>
                        </form>

                        <!-- Barra de Progresso -->
                        <div id="progressSection" class="mt-4" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-sync-alt fa-spin me-2"></i>Progresso da Importação
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="progress mb-3" style="height: 25px;">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                                             role="progressbar" style="width: 0%" id="progressBar">
                                            0%
                                        </div>
                                    </div>

                                    <div class="row text-center">
                                        <div class="col-md-3">
                                            <div class="progress-stat">
                                                <div class="progress-number" id="processedCount">0</div>
                                                <div class="progress-label">Processados</div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="progress-stat">
                                                <div class="progress-number" id="totalCount">0</div>
                                                <div class="progress-label">Total</div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="progress-stat">
                                                <div class="progress-number" id="currentBatch">1</div>
                                                <div class="progress-label">Lote Atual</div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="progress-stat">
                                                <div class="progress-number" id="estimatedTime">--</div>
                                                <div class="progress-label">Tempo Restante</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <div id="statusMessage" class="text-center text-muted">
                                            Preparando importação...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Resultado da Importação -->
                        <div id="resultSection" class="mt-4" style="display: none;">
                        </div>
                    </div>
                </div>
            </div>
        </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informações sobre as Colunas -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i> Formato do Arquivo CSV
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-exclamation-circle me-2"></i>Importante:</h6>
                            <ul class="mb-0">
                                <li>O arquivo deve estar no formato CSV com as colunas na ordem especificada abaixo</li>
                                <li>A cada importação, todos os dados anteriores serão removidos e substituídos pelos novos dados</li>
                                <li>CPFs serão automaticamente formatados com zeros à esquerda quando necessário</li>
                                <li>Datas devem estar no formato dd/mm/aaaa ou dd-mm-aaaa</li>
                                <li><strong>Carga Horária</strong> aceita formatos hh:mm (ex: 10:25) ou números (ex: 1025 vira 10:25)</li>
                                <li><strong>Acentos e caracteres especiais</strong> são automaticamente tratados (UTF-8, ISO-8859-1, Windows-1252)</li>
                            </ul>
                        </div>

                        <div class="column-info">
                            <h6 class="mb-3">Colunas esperadas no arquivo CSV (nesta ordem):</h6>
                            <ul class="column-list">
                                <li><strong>1.</strong> Código da Unidade</li>
                                <li><strong>2.</strong> Hierarquia da Unidade</li>
                                <li><strong>3.</strong> Usuário</li>
                                <li><strong>4.</strong> Identificador</li>
                                <li><strong>5.</strong> Situação do Usuário</li>
                                <li><strong>6.</strong> CPF</li>
                                <li><strong>7.</strong> E-mail</li>
                                <li><strong>8.</strong> Data de Admissão</li>
                                <li><strong>9.</strong> Função</li>
                                <li><strong>10.</strong> Superior imediato</li>
                                <li><strong>11.</strong> Tipo de trilha</li>
                                <li><strong>12.</strong> Etapa</li>
                                <li><strong>13.</strong> Código da Trilha</li>
                                <li><strong>14.</strong> Trilha</li>
                                <li><strong>15.</strong> Aprovado na Trilha</li>
                                <li><strong>16.</strong> Aproveitamento</li>
                                <li><strong>17.</strong> Situação da Trilha</li>
                                <li><strong>18.</strong> Iniciar trilha em</li>
                                <li><strong>19.</strong> Concluir trilha até</li>
                                <li><strong>20.</strong> Data de aprovação na trilha</li>
                                <li><strong>21.</strong> Carga Horária da Trilha</li>
                                <li><strong>22.</strong> Prazo da etapa da trilha jornada</li>
                                <li><strong>23.</strong> Andamento da etapa</li>
                                <li><strong>24.</strong> Total de horas essenciais da etapa</li>
                                <li><strong>25.</strong> Horas essenciais feitas</li>
                                <li><strong>26.</strong> Horas complementares feitas</li>
                                <li><strong>27.</strong> Código do Recurso</li>
                                <li><strong>28.</strong> Recurso</li>
                                <li><strong>29.</strong> Nota do Recurso</li>
                                <li><strong>30.</strong> Aprovação</li>
                                <li><strong>31.</strong> Carga Horária do Recurso</li>
                                <li><strong>32.</strong> Data da Conclusão</li>
                                <li><strong>33.</strong> Validade Recurso</li>
                                <li><strong>34.</strong> Responsável pela Associação</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('csv_file');
            const fileInfo = document.getElementById('fileInfo');
            const fileName = document.getElementById('fileName');
            const fileSize = document.getElementById('fileSize');
            const removeFile = document.getElementById('removeFile');
            const submitBtn = document.getElementById('submitBtn');
            const uploadForm = document.getElementById('uploadForm');

            // Elementos de progresso
            const progressSection = document.getElementById('progressSection');
            const progressBar = document.getElementById('progressBar');
            const processedCount = document.getElementById('processedCount');
            const totalCount = document.getElementById('totalCount');
            const currentBatch = document.getElementById('currentBatch');
            const estimatedTime = document.getElementById('estimatedTime');
            const statusMessage = document.getElementById('statusMessage');
            const resultSection = document.getElementById('resultSection');

            let progressInterval = null;
            let currentImportacao = null;

            // Eventos de drag and drop
            uploadArea.addEventListener('click', () => fileInput.click());

            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    handleFileSelect();
                }
            });

            // Evento de seleção de arquivo
            fileInput.addEventListener('change', handleFileSelect);

            // Remover arquivo
            removeFile.addEventListener('click', () => {
                fileInput.value = '';
                fileInfo.classList.add('d-none');
                uploadArea.style.display = 'block';
                submitBtn.disabled = true;
            });

            function handleFileSelect() {
                const file = fileInput.files[0];
                if (file) {
                    // Validar tipo de arquivo
                    if (!file.name.toLowerCase().endsWith('.csv')) {
                        alert('Por favor, selecione um arquivo CSV.');
                        fileInput.value = '';
                        return;
                    }

                    // Validar tamanho
                    const maxSize = <?php echo EDU_MAX_FILE_SIZE; ?>;
                    if (file.size > maxSize) {
                        alert('Arquivo muito grande. Tamanho máximo: ' + (maxSize / 1024 / 1024) + 'MB');
                        fileInput.value = '';
                        return;
                    }

                    // Mostrar informações do arquivo
                    fileName.textContent = file.name;
                    fileSize.textContent = formatFileSize(file.size);

                    uploadArea.style.display = 'none';
                    fileInfo.classList.remove('d-none');
                    submitBtn.disabled = false;
                }
            }

            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            function showProgressInterface() {
                // Ocultar formulário e mostrar progresso
                uploadForm.style.display = 'none';
                progressSection.style.display = 'block';
                resultSection.style.display = 'none';

                // Resetar valores
                progressBar.style.width = '0%';
                progressBar.textContent = '0%';
                processedCount.textContent = '0';
                currentBatch.textContent = '1';
                estimatedTime.textContent = '--';
            }

            function startProgressMonitoring() {
                // Processar primeiro lote
                processNextBatch();

                // Monitorar progresso a cada 1.5 segundos
                progressInterval = setInterval(checkProgress, 1500);
            }

            function processNextBatch() {
                if (!currentImportacao) return;

                const formData = new FormData();
                formData.append('importacao_id', currentImportacao);

                fetch('processar_importacao.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    updateProgress(data);
                })
                .catch(error => {
                    console.error('Erro no processamento:', error);
                    showError('Erro durante o processamento.');
                });
            }

            function checkProgress() {
                if (!currentImportacao) return;

                const formData = new FormData();
                formData.append('importacao_id', currentImportacao);

                fetch('check_progress.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    updateProgress(data);
                })
                .catch(error => {
                    console.error('Erro ao verificar progresso:', error);
                });
            }

            function updateProgress(data) {
                if (!data) return;

                if (data.status === 'completed') {
                    // Importação concluída
                    clearInterval(progressInterval);
                    progressBar.style.width = '100%';
                    progressBar.textContent = '100%';
                    processedCount.textContent = data.processed.toLocaleString();
                    estimatedTime.textContent = '0s';
                    statusMessage.textContent = 'Importação concluída!';

                    setTimeout(() => {
                        showResult(data.message, data.errors > 0 ? 'warning' : 'success');
                    }, 1000);

                } else if (data.status === 'error') {
                    // Erro na importação
                    clearInterval(progressInterval);
                    showError(data.message);

                } else if (data.status === 'processing') {
                    // Atualizar progresso
                    const percent = data.progress_percent || 0;
                    progressBar.style.width = percent + '%';
                    progressBar.textContent = Math.round(percent) + '%';
                    processedCount.textContent = data.processed.toLocaleString();
                    currentBatch.textContent = data.current_batch || 1;

                    // Atualizar tempo estimado
                    if (data.estimated_remaining) {
                        estimatedTime.textContent = formatTime(data.estimated_remaining);
                    }

                    // Atualizar mensagem de status
                    statusMessage.textContent = `Processando lote ${data.current_batch || 1} - ${data.processed.toLocaleString()} de ${data.total.toLocaleString()} registros`;

                    // Processar próximo lote
                    setTimeout(processNextBatch, 200);
                }
            }

            function formatTime(seconds) {
                if (seconds < 60) {
                    return Math.round(seconds) + 's';
                } else if (seconds < 3600) {
                    return Math.round(seconds / 60) + 'm';
                } else {
                    return Math.round(seconds / 3600) + 'h';
                }
            }

            function showResult(message, type = 'success') {
                progressSection.style.display = 'none';
                resultSection.style.display = 'block';

                const alertClass = type === 'success' ? 'result-success' : 'result-error';
                const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';

                resultSection.innerHTML = `
                    <div class="card">
                        <div class="card-body ${alertClass}">
                            <h5><i class="fas ${icon} me-2"></i>Resultado da Importação</h5>
                            <p class="mb-0">${message}</p>
                            <div class="mt-3">
                                <a href="index.php" class="btn btn-primary">
                                    <i class="fas fa-home me-2"></i>Voltar ao Início
                                </a>
                                <button type="button" class="btn btn-secondary ms-2" onclick="location.reload()">
                                    <i class="fas fa-redo me-2"></i>Nova Importação
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }

            function showError(message) {
                clearInterval(progressInterval);
                showResult(message, 'error');
            }

            // Processar upload com progresso em tempo real
            uploadForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);

                // Mostrar interface de progresso
                showProgressInterface();

                // Enviar arquivo para processamento
                fetch('processar_importacao.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentImportacao = data.importacao_id;
                        totalCount.textContent = data.total_registros.toLocaleString();
                        statusMessage.textContent = `Iniciando processamento de ${data.total_registros.toLocaleString()} registros...`;

                        // Iniciar monitoramento do progresso
                        startProgressMonitoring();
                    } else {
                        showError(data.message);
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    showError('Erro ao enviar arquivo. Tente novamente.');
                });
            });
        });
    </script>
</body>
</html>
