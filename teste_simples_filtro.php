<?php
/**
 * Teste simples para verificar apenas a lógica de filtro
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/IntranetAPI.php';

echo "<h1>🧪 Teste Simples: Lógica de Filtro</h1>\n";

// Criar instância da API
$api = new IntranetAPI();

// Buscar dados da API SEM CACHE
echo "<h2>1. 📊 Buscar Dados da API</h2>\n";
$usuarios_intranet_todos = $api->listarUsuarios(false, false);
$usuarios_intranet_ativos = $api->listarUsuariosAtivos(false);

echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Resultado da API:</strong></p>";
echo "<ul>";
echo "<li><strong>Todos os usuários:</strong> " . (is_array($usuarios_intranet_todos) ? count($usuarios_intranet_todos) : 'ERRO') . "</li>";
echo "<li><strong>Usuários ativos:</strong> " . (is_array($usuarios_intranet_ativos) ? count($usuarios_intranet_ativos) : 'ERRO') . "</li>";
echo "</ul>";
echo "</div>";

if (!is_array($usuarios_intranet_todos) || !is_array($usuarios_intranet_ativos)) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>ERRO:</strong> Não foi possível buscar dados da API</p>";
    echo "</div>";
    exit;
}

echo "<h2>2. 🗺️ Criar Mapas</h2>\n";

// Criar mapas
$mapa_usuarios_ativos = [];
$mapa_usuarios_todos = [];

foreach ($usuarios_intranet_ativos as $usuario) {
    if (!empty($usuario['cpf'])) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
        $mapa_usuarios_ativos[$cpf_normalizado] = $usuario;
    }
}

foreach ($usuarios_intranet_todos as $usuario) {
    if (!empty($usuario['cpf'])) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
        $mapa_usuarios_todos[$cpf_normalizado] = $usuario;
    }
}

echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Mapas criados:</strong></p>";
echo "<ul>";
echo "<li><strong>Mapa de ativos:</strong> " . count($mapa_usuarios_ativos) . " CPFs</li>";
echo "<li><strong>Mapa de todos:</strong> " . count($mapa_usuarios_todos) . " CPFs</li>";
echo "</ul>";
echo "</div>";

echo "<h2>3. 🔍 Buscar ALICE BEATRIZ</h2>\n";

// Buscar ALICE especificamente
$alice_query = "SELECT cpf, usuario FROM edu_relatorio_educacao WHERE usuario LIKE '%ALICE BEATRIZ%' LIMIT 1";
$stmt_alice = $pdo_edu->prepare($alice_query);
$stmt_alice->execute();
$alice_data = $stmt_alice->fetch();

if ($alice_data) {
    $alice_cpf = str_pad(preg_replace('/[^0-9]/', '', $alice_data['cpf']), 11, '0', STR_PAD_LEFT);
    $alice_ativo = $mapa_usuarios_ativos[$alice_cpf] ?? null;
    $alice_todos = $mapa_usuarios_todos[$alice_cpf] ?? null;
    
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #e9ecef;'>";
    echo "<th style='padding: 10px;'>Verificação</th>";
    echo "<th style='padding: 10px;'>Resultado</th>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td style='padding: 10px;'><strong>CPF normalizado</strong></td>";
    echo "<td style='padding: 10px;'>" . $alice_cpf . "</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td style='padding: 10px;'><strong>Existe no mapa TODOS</strong></td>";
    echo "<td style='padding: 10px;'>" . ($alice_todos ? '✅ SIM' : '❌ NÃO') . "</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td style='padding: 10px;'><strong>Existe no mapa ATIVOS</strong></td>";
    echo "<td style='padding: 10px;'>" . ($alice_ativo ? '✅ SIM' : '❌ NÃO') . "</td>";
    echo "</tr>";
    
    // Testar a condição de filtro
    $condicao_filtro = ($alice_todos && !$alice_ativo);
    
    echo "<tr style='background: " . ($condicao_filtro ? '#f8d7da' : '#d4edda') . ";'>";
    echo "<td style='padding: 10px;'><strong>Condição: (todos && !ativo)</strong></td>";
    echo "<td style='padding: 10px;'>" . ($condicao_filtro ? '✅ VERDADEIRA' : '❌ FALSA') . "</td>";
    echo "</tr>";
    
    echo "<tr style='background: " . ($condicao_filtro ? '#f8d7da' : '#d4edda') . ";'>";
    echo "<td style='padding: 10px;'><strong>Ação</strong></td>";
    echo "<td style='padding: 10px;'>" . ($condicao_filtro ? '🚫 FILTRAR (continue)' : '✅ PROCESSAR') . "</td>";
    echo "</tr>";
    
    echo "<tr style='background: " . ($condicao_filtro ? '#f8d7da' : '#d4edda') . ";'>";
    echo "<td style='padding: 10px;'><strong>Deve aparecer na página</strong></td>";
    echo "<td style='padding: 10px;'>" . ($condicao_filtro ? '❌ NÃO' : '✅ SIM') . "</td>";
    echo "</tr>";
    
    echo "</table>";
    echo "</div>";
    
    if ($alice_todos) {
        echo "<div style='background: #e2e3e5; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>📋 Dados de ALICE no mapa TODOS:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Status:</strong> " . ($alice_todos['status'] ?? 'indefinido') . "</li>";
        echo "<li><strong>Nome:</strong> " . htmlspecialchars($alice_todos['nome'] ?? 'indefinido') . "</li>";
        echo "<li><strong>Agência:</strong> " . ($alice_todos['agencia'] ?? 'indefinida') . "</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    if ($alice_ativo) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>📋 Dados de ALICE no mapa ATIVOS:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Status:</strong> " . ($alice_ativo['status'] ?? 'indefinido') . "</li>";
        echo "<li><strong>Nome:</strong> " . htmlspecialchars($alice_ativo['nome'] ?? 'indefinido') . "</li>";
        echo "<li><strong>Agência:</strong> " . ($alice_ativo['agencia'] ?? 'indefinida') . "</li>";
        echo "</ul>";
        echo "</div>";
    }
    
} else {
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>⚠️ ALICE BEATRIZ DA SILVA não foi encontrada no banco de dados</p>";
    echo "</div>";
}

echo "<h2>4. 🧪 Teste da Lógica com Outros Colaboradores</h2>\n";

// Buscar alguns colaboradores para teste
$colaboradores_query = "
    SELECT cpf, MAX(usuario) as usuario
    FROM edu_relatorio_educacao
    GROUP BY cpf
    ORDER BY MAX(usuario)
    LIMIT 10
";

$stmt_colaboradores = $pdo_edu->prepare($colaboradores_query);
$stmt_colaboradores->execute();
$colaboradores_teste = $stmt_colaboradores->fetchAll();

echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='padding: 5px;'>CPF</th>";
echo "<th style='padding: 5px;'>Nome</th>";
echo "<th style='padding: 5px;'>Todos?</th>";
echo "<th style='padding: 5px;'>Ativo?</th>";
echo "<th style='padding: 5px;'>Condição</th>";
echo "<th style='padding: 5px;'>Ação</th>";
echo "</tr>";

$total_testados = 0;
$total_filtrados = 0;
$total_processados = 0;

foreach ($colaboradores_teste as $colaborador) {
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    
    $usuario_intranet_ativo = $mapa_usuarios_ativos[$cpf_normalizado] ?? null;
    $usuario_intranet_todos = $mapa_usuarios_todos[$cpf_normalizado] ?? null;
    
    $total_testados++;
    
    $existe_todos = $usuario_intranet_todos ? 'SIM' : 'NÃO';
    $existe_ativo = $usuario_intranet_ativo ? 'SIM' : 'NÃO';
    
    // Aplicar a lógica exata
    $condicao_filtro = ($usuario_intranet_todos && !$usuario_intranet_ativo);
    
    if ($condicao_filtro) {
        $acao = 'FILTRAR';
        $cor_linha = '#f8d7da';
        $total_filtrados++;
    } else {
        $acao = 'PROCESSAR';
        $cor_linha = '#d4edda';
        $total_processados++;
    }
    
    echo "<tr style='background-color: $cor_linha;'>";
    echo "<td style='padding: 5px;'>" . substr($colaborador['cpf'], 0, 3) . "***</td>";
    echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($colaborador['usuario'], 0, 20)) . "</td>";
    echo "<td style='padding: 5px;'>$existe_todos</td>";
    echo "<td style='padding: 5px;'>$existe_ativo</td>";
    echo "<td style='padding: 5px;'>" . ($condicao_filtro ? 'VERDADEIRA' : 'FALSA') . "</td>";
    echo "<td style='padding: 5px;'><strong>$acao</strong></td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📊 Resumo do Teste:</h3>";
echo "<ul>";
echo "<li><strong>Total testados:</strong> $total_testados</li>";
echo "<li><strong>Total filtrados:</strong> $total_filtrados</li>";
echo "<li><strong>Total processados:</strong> $total_processados</li>";
echo "</ul>";
echo "</div>";

echo "<h2>5. 🎯 Conclusão</h2>\n";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>✅ Resultado do Teste:</h3>";
echo "<p>A lógica de filtro está funcionando corretamente neste teste isolado.</p>";
echo "<p><strong>Se ALICE ainda aparece na página principal, o problema pode ser:</strong></p>";
echo "<ul>";
echo "<li>Cache do navegador</li>";
echo "<li>Cache de sessão PHP</li>";
echo "<li>Dados em cache na aplicação</li>";
echo "<li>Problema na aplicação da lógica na página principal</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔧 Próximos Passos:</h3>";
echo "<ol>";
echo "<li>Limpe completamente o cache do navegador</li>";
echo "<li>Abra a página em aba anônima</li>";
echo "<li>Teste com: <a href='analise_colaboradores.php?aba=colaboradores&nocache=" . time() . "'>Página Principal</a></li>";
echo "</ol>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
?>
