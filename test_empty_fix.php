<?php
/**
 * Teste de Correção - Colaboradores Vazios
 * 
 * Este arquivo testa se o problema de "Nenhum colaborador encontrado" foi corrigido.
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'classes/IntranetAPI.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

echo "<h1>🔍 Teste de Correção - Colaboradores Vazios</h1>";

// Teste 1: Verificar problema identificado
echo "<h2>1. ❌ Problema Identificado</h2>";

echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Problema:</strong></p>";
echo "<ul>";
echo "<li>❌ <strong>Mensagem de Erro:</strong> 'Nenhum colaborador encontrado'</li>";
echo "<li>❌ <strong>Causa:</strong> Variável \$colaboradores_por_pa vazia após paginação</li>";
echo "<li>❌ <strong>Condição:</strong> empty(\$colaboradores) verificando variável errada</li>";
echo "<li>❌ <strong>Paginação:</strong> array_slice retornando array vazio</li>";
echo "</ul>";
echo "</div>";

// Teste 2: Verificar dados disponíveis
echo "<h2>2. 📊 Verificação de Dados Disponíveis</h2>";

try {
    // Verificar se há colaboradores no banco
    $query_count = "SELECT COUNT(DISTINCT cpf) as total FROM edu_relatorio_educacao WHERE cpf IS NOT NULL AND cpf != ''";
    $stmt = $pdo_edu->prepare($query_count);
    $stmt->execute();
    $total_colaboradores_db = $stmt->fetch()['total'];
    
    echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Dados no Banco:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Total de colaboradores únicos:</strong> $total_colaboradores_db</li>";
    echo "</ul>";
    echo "</div>";
    
    if ($total_colaboradores_db > 0) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>Dados disponíveis no banco de dados</strong></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>⚠️ <strong>Nenhum colaborador encontrado no banco de dados</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro ao verificar dados:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 3: Simular fluxo corrigido
echo "<h2>3. 🔧 Teste do Fluxo Corrigido</h2>";

try {
    // Simular busca de dados da API
    $api = new IntranetAPI();
    $usuarios_api = $api->listarUsuarios();
    $agencias_api = $api->listarAgencias();
    
    // Criar mapeamentos
    $mapa_usuarios_cpf = [];
    foreach ($usuarios_api as $usuario) {
        if (!empty($usuario['cpf'])) {
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
            $mapa_usuarios_cpf[$cpf_normalizado] = $usuario;
        }
    }
    
    $mapa_agencias = [];
    foreach ($agencias_api as $agencia) {
        $mapa_agencias[$agencia['id']] = $agencia;
    }
    
    // Simular busca de TODOS os colaboradores
    $query_todos = "
        SELECT 
            cpf, usuario, email, funcao, codigo_unidade,
            COUNT(DISTINCT trilha) as total_trilhas,
            COUNT(DISTINCT recurso) as total_cursos,
            SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as cursos_aprovados,
            COUNT(CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00' THEN 1 END) as cursos_concluidos,
            COUNT(CASE WHEN validade_recurso IS NOT NULL AND validade_recurso != '0000-00-00' AND validade_recurso < CURDATE() THEN 1 END) as cursos_vencidos,
            COUNT(CASE WHEN validade_recurso IS NOT NULL AND validade_recurso != '0000-00-00' AND validade_recurso <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND validade_recurso >= CURDATE() THEN 1 END) as cursos_a_vencer
        FROM edu_relatorio_educacao
        WHERE cpf IS NOT NULL AND cpf != ''
        GROUP BY cpf, usuario, email, funcao, codigo_unidade
        ORDER BY usuario
        LIMIT 10
    ";
    
    $stmt = $pdo_edu->prepare($query_todos);
    $stmt->execute();
    $todos_colaboradores = $stmt->fetchAll();
    
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>✅ <strong>Busca de Colaboradores:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Colaboradores encontrados:</strong> " . count($todos_colaboradores) . "</li>";
    echo "</ul>";
    echo "</div>";
    
    if (!empty($todos_colaboradores)) {
        // Simular agrupamento por PA
        $colaboradores_por_pa = [];
        
        foreach ($todos_colaboradores as $colaborador) {
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
            $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;
            
            // Determinar PA
            $pa_info = ['id' => 'sem_pa', 'numero' => 'S/PA', 'nome' => 'Sem PA Definido'];
            
            if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
                $agencia_id = $usuario_intranet['agencia'];
                if (isset($mapa_agencias[$agencia_id])) {
                    $agencia_data = $mapa_agencias[$agencia_id];
                    $pa_info = [
                        'id' => $agencia_id,
                        'numero' => $agencia_data['numero'],
                        'nome' => $agencia_data['nome']
                    ];
                } else {
                    $pa_info = ['id' => $agencia_id, 'numero' => $agencia_id, 'nome' => 'PA ' . $agencia_id];
                }
            }
            
            $pa_key = $pa_info['numero'] . ' - ' . $pa_info['nome'];
            
            if (!isset($colaboradores_por_pa[$pa_key])) {
                $colaboradores_por_pa[$pa_key] = [
                    'info' => $pa_info,
                    'colaboradores' => [],
                    'total_colaboradores' => 0,
                    'total_cursos' => 0,
                    'total_concluidos' => 0,
                    'total_vencidos' => 0,
                    'total_a_vencer' => 0
                ];
            }
            
            $colaboradores_por_pa[$pa_key]['colaboradores'][] = $colaborador;
            $colaboradores_por_pa[$pa_key]['total_colaboradores']++;
            $colaboradores_por_pa[$pa_key]['total_cursos'] += $colaborador['total_cursos'];
            $colaboradores_por_pa[$pa_key]['total_concluidos'] += $colaborador['cursos_concluidos'];
            $colaboradores_por_pa[$pa_key]['total_vencidos'] += $colaborador['cursos_vencidos'];
            $colaboradores_por_pa[$pa_key]['total_a_vencer'] += $colaborador['cursos_a_vencer'];
        }
        
        // Ordenar PAs
        uksort($colaboradores_por_pa, function($a, $b) {
            preg_match('/^(\d+|S\/PA)/', $a, $matches_a);
            preg_match('/^(\d+|S\/PA)/', $b, $matches_b);
            
            $num_a = $matches_a[1] === 'S/PA' ? 9999 : (int)$matches_a[1];
            $num_b = $matches_b[1] === 'S/PA' ? 9999 : (int)$matches_b[1];
            
            return $num_a <=> $num_b;
        });
        
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>Agrupamento por PA:</strong></p>";
        echo "<ul>";
        echo "<li><strong>PAs identificados:</strong> " . count($colaboradores_por_pa) . "</li>";
        echo "</ul>";
        echo "</div>";
        
        // Simular paginação
        $total_pas = count($colaboradores_por_pa);
        $pas_per_page = 5;
        $total_pages = max(1, ceil($total_pas / $pas_per_page));
        
        // Testar diferentes páginas
        for ($page = 1; $page <= min(3, $total_pages); $page++) {
            $offset_pa = ($page - 1) * $pas_per_page;
            $colaboradores_por_pa_paginados = array_slice($colaboradores_por_pa, $offset_pa, $pas_per_page, true);
            
            echo "<div style='background: #e2e3e5; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p><strong>Página $page:</strong></p>";
            echo "<ul>";
            echo "<li><strong>PAs nesta página:</strong> " . count($colaboradores_por_pa_paginados) . "</li>";
            echo "<li><strong>Status:</strong> " . (empty($colaboradores_por_pa_paginados) ? '❌ Vazio' : '✅ Com dados') . "</li>";
            if (!empty($colaboradores_por_pa_paginados)) {
                echo "<li><strong>PAs:</strong> " . implode(', ', array_keys($colaboradores_por_pa_paginados)) . "</li>";
            }
            echo "</ul>";
            echo "</div>";
        }
        
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>⚠️ <strong>Nenhum colaborador encontrado na consulta de teste</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro no teste:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 4: Verificar correções implementadas
echo "<h2>4. ✅ Correções Implementadas</h2>";

echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Correções Aplicadas:</strong></p>";
echo "<ul>";
echo "<li>✅ <strong>Condição Corrigida:</strong> empty(\$colaboradores_por_pa) em vez de empty(\$colaboradores)</li>";
echo "<li>✅ <strong>Validação de Página:</strong> Verificar se página solicitada é válida</li>";
echo "<li>✅ <strong>Fallback:</strong> Criar seção padrão se não há PAs mas há colaboradores</li>";
echo "<li>✅ <strong>Páginas Mínimas:</strong> Garantir pelo menos 1 página</li>";
echo "</ul>";
echo "</div>";

// Resumo final
echo "<h2>5. 📋 Resumo da Correção</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔧 Problema e Solução</h3>";

echo "<h4>❌ Problema Original:</h4>";
echo "<ul>";
echo "<li><strong>Condição Errada:</strong> empty(\$colaboradores) verificando variável não utilizada</li>";
echo "<li><strong>Paginação Vazia:</strong> array_slice retornando array vazio para páginas inválidas</li>";
echo "<li><strong>Sem Fallback:</strong> Nenhuma verificação para casos extremos</li>";
echo "</ul>";

echo "<h4>✅ Solução Implementada:</h4>";
echo "<ul>";
echo "<li><strong>Condição Correta:</strong> empty(\$colaboradores_por_pa)</li>";
echo "<li><strong>Validação de Página:</strong> if (\$filtros['page'] > \$total_pages) \$filtros['page'] = 1;</li>";
echo "<li><strong>Fallback Inteligente:</strong> Criar seção padrão quando necessário</li>";
echo "<li><strong>Páginas Mínimas:</strong> max(1, ceil(\$total_pas / \$pas_per_page))</li>";
echo "</ul>";

echo "<h4>✅ Fluxo Corrigido:</h4>";
echo "<ol>";
echo "<li>Buscar todos os colaboradores</li>";
echo "<li>Agrupar por PA</li>";
echo "<li>Validar página solicitada</li>";
echo "<li>Aplicar paginação aos PAs</li>";
echo "<li>Verificar se resultado está vazio</li>";
echo "<li>Aplicar fallback se necessário</li>";
echo "<li>Exibir dados ou mensagem apropriada</li>";
echo "</ol>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>👥 Testar Correção</a>";
echo "<a href='analise_colaboradores.php?aba=colaboradores&page=999' style='background: #dc3545; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🔍 Testar Página Inválida</a>";
echo "</p>";
?>
