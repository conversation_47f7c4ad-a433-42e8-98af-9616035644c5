<?php
/**
 * Script de instalação e verificação da API da Intranet
 * 
 * Este script verifica se a integração com a API está funcionando
 * corretamente e cria os diretórios necessários.
 */

require_once 'config/config.php';
require_once 'edu_auth_check.php';
require_once 'classes/IntranetAPI.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

$results = [];
$overall_status = true;

// 1. Verificar configurações
$results['config'] = [
    'title' => 'Configurações da API',
    'status' => true,
    'details' => [
        'URL da API' => EDU_API_URL,
        'Usuário da API' => EDU_API_USER,
        'Token da API' => substr(EDU_API_TOKEN, 0, 10) . '...',
        'Tempo de Cache' => EDU_API_CACHE_TIME . ' segundos',
        'Diretório de Cache' => EDU_API_CACHE_PATH,
        'Debug Mode' => EDU_DEBUG_MODE ? 'Ativo' : 'Inativo'
    ]
];

// 2. Verificar diretórios
$directories = [
    'Cache' => EDU_API_CACHE_PATH,
    'Logs' => EDU_LOG_PATH,
    'Uploads' => EDU_UPLOAD_PATH
];

$dir_status = true;
$dir_details = [];

foreach ($directories as $name => $path) {
    $exists = is_dir($path);
    $writable = $exists ? is_writable($path) : false;
    
    if (!$exists) {
        $created = mkdir($path, 0755, true);
        $writable = $created ? is_writable($path) : false;
    }
    
    $status = $exists && $writable;
    if (!$status) $dir_status = false;
    
    $dir_details[$name] = [
        'path' => $path,
        'exists' => $exists,
        'writable' => $writable,
        'status' => $status
    ];
}

$results['directories'] = [
    'title' => 'Diretórios do Sistema',
    'status' => $dir_status,
    'details' => $dir_details
];

if (!$dir_status) $overall_status = false;

// 3. Verificar extensões PHP
$extensions = ['curl', 'json'];
$ext_status = true;
$ext_details = [];

foreach ($extensions as $ext) {
    $loaded = extension_loaded($ext);
    if (!$loaded) $ext_status = false;
    
    $ext_details[$ext] = [
        'loaded' => $loaded,
        'status' => $loaded
    ];
}

$results['extensions'] = [
    'title' => 'Extensões PHP',
    'status' => $ext_status,
    'details' => $ext_details
];

if (!$ext_status) $overall_status = false;

// 4. Testar conectividade com a API
$api = new IntranetAPI();
$api_status = true;
$api_details = [];

// Teste de usuários
$start_time = microtime(true);
$usuarios = $api->listarUsuarios(false); // Sem cache para teste real
$usuarios_time = round((microtime(true) - $start_time) * 1000, 2);

$usuarios_status = $usuarios !== false;
if (!$usuarios_status) $api_status = false;

$api_details['Usuários'] = [
    'status' => $usuarios_status,
    'count' => is_array($usuarios) ? count($usuarios) : 0,
    'time' => $usuarios_time . 'ms'
];

// Teste de agências
$start_time = microtime(true);
$agencias = $api->listarAgencias(false); // Sem cache para teste real
$agencias_time = round((microtime(true) - $start_time) * 1000, 2);

$agencias_status = $agencias !== false;
if (!$agencias_status) $api_status = false;

$api_details['Agências'] = [
    'status' => $agencias_status,
    'count' => is_array($agencias) ? count($agencias) : 0,
    'time' => $agencias_time . 'ms'
];

$results['api'] = [
    'title' => 'Conectividade da API',
    'status' => $api_status,
    'details' => $api_details
];

if (!$api_status) $overall_status = false;

// 5. Testar sistema de cache
$cache_status = true;
$cache_details = [];

// Teste de escrita no cache
$test_data = ['test' => 'data', 'timestamp' => time()];
$cache_file = EDU_API_CACHE_PATH . 'test_cache.json';

$write_success = file_put_contents($cache_file, json_encode($test_data)) !== false;
$read_success = false;
$delete_success = false;

if ($write_success) {
    $read_data = json_decode(file_get_contents($cache_file), true);
    $read_success = $read_data && $read_data['test'] === 'data';
    
    if ($read_success) {
        $delete_success = unlink($cache_file);
    }
}

$cache_test_status = $write_success && $read_success && $delete_success;
if (!$cache_test_status) $cache_status = false;

$cache_details['Teste de Cache'] = [
    'write' => $write_success,
    'read' => $read_success,
    'delete' => $delete_success,
    'status' => $cache_test_status
];

$results['cache'] = [
    'title' => 'Sistema de Cache',
    'status' => $cache_status,
    'details' => $cache_details
];

if (!$cache_status) $overall_status = false;

?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instalação API - <?php echo EDU_PROJECT_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-turquesa: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-branco: #FFFFFF;
        }

        body { background-color: #f8f9fa; }
        .navbar { background-color: var(--sicoob-verde-escuro) !important; }
        .navbar-brand, .navbar-nav .nav-link { color: var(--sicoob-branco) !important; }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: var(--sicoob-branco);
            border-radius: 12px 12px 0 0 !important;
        }
        
        .status-success { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-icon { font-size: 1.2rem; margin-right: 0.5rem; }
        
        .detail-item {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .detail-item:last-child {
            border-bottom: none;
        }
        
        .overall-status {
            font-size: 1.5rem;
            font-weight: bold;
            text-align: center;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
        }
        
        .status-ok {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 1px solid #28a745;
        }
        
        .status-error {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 1px solid #dc3545;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap me-2"></i>
                <?php echo EDU_PROJECT_NAME; ?>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="test_api.php">
                    <i class="fas fa-flask me-1"></i> Testes
                </a>
                <a class="nav-link" href="index.php">
                    <i class="fas fa-home me-1"></i> Voltar
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-cogs me-2"></i>Instalação e Verificação da API</h2>
                <p class="text-muted">Verificação completa da integração com a API da Intranet</p>
            </div>
        </div>

        <!-- Status Geral -->
        <div class="overall-status <?php echo $overall_status ? 'status-ok' : 'status-error'; ?>">
            <i class="fas <?php echo $overall_status ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> me-2"></i>
            <?php if ($overall_status): ?>
                ✅ API da Intranet configurada e funcionando corretamente!
            <?php else: ?>
                ❌ Problemas encontrados na configuração da API
            <?php endif; ?>
        </div>

        <!-- Resultados Detalhados -->
        <?php foreach ($results as $section_key => $section): ?>
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas <?php echo $section['status'] ? 'fa-check-circle status-success' : 'fa-times-circle status-error'; ?> status-icon"></i>
                    <?php echo $section['title']; ?>
                </h5>
            </div>
            <div class="card-body">
                <?php foreach ($section['details'] as $key => $detail): ?>
                <div class="detail-item">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <strong><?php echo htmlspecialchars($key); ?>:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php if (is_array($detail)): ?>
                                <?php if (isset($detail['status'])): ?>
                                    <span class="<?php echo $detail['status'] ? 'status-success' : 'status-error'; ?>">
                                        <i class="fas <?php echo $detail['status'] ? 'fa-check' : 'fa-times'; ?> me-1"></i>
                                        <?php echo $detail['status'] ? 'OK' : 'Erro'; ?>
                                    </span>
                                    <?php if (isset($detail['count'])): ?>
                                        - <?php echo number_format($detail['count']); ?> registros
                                    <?php endif; ?>
                                    <?php if (isset($detail['time'])): ?>
                                        (<?php echo $detail['time']; ?>)
                                    <?php endif; ?>
                                <?php else: ?>
                                    <?php foreach ($detail as $subkey => $subvalue): ?>
                                        <small class="d-block">
                                            <strong><?php echo ucfirst($subkey); ?>:</strong> 
                                            <?php if (is_bool($subvalue)): ?>
                                                <span class="<?php echo $subvalue ? 'status-success' : 'status-error'; ?>">
                                                    <?php echo $subvalue ? 'Sim' : 'Não'; ?>
                                                </span>
                                            <?php else: ?>
                                                <?php echo htmlspecialchars($subvalue); ?>
                                            <?php endif; ?>
                                        </small>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            <?php else: ?>
                                <?php echo htmlspecialchars($detail); ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endforeach; ?>

        <!-- Próximos Passos -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-arrow-right me-2"></i>Próximos Passos
                </h5>
            </div>
            <div class="card-body">
                <?php if ($overall_status): ?>
                <div class="alert alert-success">
                    <h6><i class="fas fa-thumbs-up me-2"></i>Instalação Concluída!</h6>
                    <p class="mb-2">A API da Intranet está configurada e funcionando. Você pode agora:</p>
                    <ul class="mb-0">
                        <li><a href="test_api.php">Executar testes detalhados da API</a></li>
                        <li><a href="exemplo_api.php">Ver exemplos de uso da API</a></li>
                        <li>Implementar integrações personalizadas</li>
                        <li>Configurar sincronizações automáticas</li>
                    </ul>
                </div>
                <?php else: ?>
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Ação Necessária</h6>
                    <p class="mb-2">Corrija os problemas identificados acima:</p>
                    <ul class="mb-0">
                        <li>Verifique as configurações da API</li>
                        <li>Confirme as permissões de diretórios</li>
                        <li>Teste a conectividade de rede</li>
                        <li>Consulte os logs para mais detalhes</li>
                    </ul>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
