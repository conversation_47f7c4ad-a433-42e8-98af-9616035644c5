<?php
// Relatório Simples de Cursos - VERSÃO ULTRA OTIMIZADA
// Este arquivo gera um relatório básico sem cálculos complexos de prazos

set_time_limit(180); // 3 minutos

echo '<table border="1" style="border-collapse: collapse;">';
echo '<tr style="background-color: #17a2b8; color: white; font-weight: bold;">';
echo '<td colspan="10" style="padding: 10px; text-align: center; font-size: 16px;">';
echo 'RELATÓRIO SIMPLES DE CURSOS';
echo '</td>';
echo '</tr>';

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td style="padding: 8px;">Gerado em:</td>';
echo '<td colspan="9" style="padding: 8px;">' . date('d/m/Y H:i:s') . '</td>';
echo '</tr>';

echo '<tr style="background-color: #d1ecf1;">';
echo '<td colspan="10" style="padding: 8px; text-align: center; color: #0c5460;">';
echo '<strong>Versão Otimizada:</strong> Relatório simplificado para melhor performance. Prazos baseados apenas no campo padrão.';
echo '</td>';
echo '</tr>';

// Construir filtros
$where_conditions = [];
$params = [];

if (!empty($filtros['trilha'])) {
    $where_conditions[] = "trilha LIKE ?";
    $params[] = '%' . $filtros['trilha'] . '%';
}

if (!empty($filtros['curso'])) {
    $where_conditions[] = "recurso LIKE ?";
    $params[] = '%' . $filtros['curso'] . '%';
}

// Query ultra otimizada
$query = "
    SELECT
        trilha,
        recurso,
        codigo_recurso,
        carga_horaria_recurso,
        COUNT(DISTINCT cpf) as total_participantes,
        SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as total_aprovados,
        COUNT(CASE WHEN andamento_etapa IS NOT NULL AND andamento_etapa != '' AND (aprovacao != 'Sim' OR aprovacao IS NULL) THEN 1 END) as em_andamento,
        COUNT(CASE WHEN concluir_trilha_ate < CURDATE() AND (aprovacao != 'Sim' OR aprovacao IS NULL) THEN 1 END) as vencidos,
        COUNT(CASE WHEN concluir_trilha_ate >= CURDATE() AND concluir_trilha_ate <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND (aprovacao != 'Sim' OR aprovacao IS NULL) THEN 1 END) as a_vencer,
        AVG(CASE WHEN nota_recurso > 0 THEN nota_recurso ELSE NULL END) as media_notas,
        AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento
    FROM edu_relatorio_educacao
    " . (!empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "") . "
    GROUP BY trilha, recurso, codigo_recurso, carga_horaria_recurso
    ORDER BY trilha, recurso
    LIMIT 100
";

$stmt = $pdo_edu->prepare($query);
$stmt->execute($params);
$cursos = $stmt->fetchAll();

// Cabeçalhos
echo '<tr style="background-color: #17a2b8; color: white; font-weight: bold;">';
echo '<td style="padding: 8px;">Trilha</td>';
echo '<td style="padding: 8px;">Curso</td>';
echo '<td style="padding: 8px;">Código</td>';
echo '<td style="padding: 8px;">Carga Horária</td>';
echo '<td style="padding: 8px;">Participantes</td>';
echo '<td style="padding: 8px;">Aprovados</td>';
echo '<td style="padding: 8px;">Em Andamento</td>';
echo '<td style="padding: 8px;">Vencidos</td>';
echo '<td style="padding: 8px;">A Vencer</td>';
echo '<td style="padding: 8px;">% Aprovação</td>';
echo '</tr>';

// Dados
foreach ($cursos as $curso) {
    $percentual_aprovacao = $curso['total_participantes'] > 0 ? 
        ($curso['total_aprovados'] / $curso['total_participantes']) * 100 : 0;
    
    $cor_linha = ($percentual_aprovacao >= 80) ? '#d4edda' : 
                 (($percentual_aprovacao >= 60) ? '#fff3cd' : '#f8d7da');
    
    echo '<tr style="background-color: ' . $cor_linha . ';">';
    echo '<td style="padding: 6px;">' . htmlspecialchars($curso['trilha']) . '</td>';
    echo '<td style="padding: 6px;">' . htmlspecialchars($curso['recurso']) . '</td>';
    echo '<td style="padding: 6px;">' . htmlspecialchars($curso['codigo_recurso']) . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . ($curso['carga_horaria_recurso'] ?? 'N/A') . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . $curso['total_participantes'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; color: #28a745; font-weight: bold;">' . $curso['total_aprovados'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; color: #007bff;">' . $curso['em_andamento'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; color: #dc3545; font-weight: bold;">' . $curso['vencidos'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; color: #fd7e14;">' . $curso['a_vencer'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; font-weight: bold;">' . number_format($percentual_aprovacao, 1) . '%</td>';
    echo '</tr>';
}

// Resumo
echo '<tr><td colspan="10" style="padding: 10px;"></td></tr>';

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td colspan="10" style="padding: 10px; text-align: center; font-size: 14px;">RESUMO GERAL</td>';
echo '</tr>';

$total_participantes = array_sum(array_column($cursos, 'total_participantes'));
$total_aprovados = array_sum(array_column($cursos, 'total_aprovados'));
$total_em_andamento = array_sum(array_column($cursos, 'em_andamento'));
$total_vencidos = array_sum(array_column($cursos, 'vencidos'));
$total_a_vencer = array_sum(array_column($cursos, 'a_vencer'));

echo '<tr>';
echo '<td colspan="2" style="padding: 8px; font-weight: bold;">Total de Cursos:</td>';
echo '<td style="padding: 8px; text-align: center; font-weight: bold;">' . count($cursos) . '</td>';
echo '<td style="padding: 8px; font-weight: bold;">Total Participantes:</td>';
echo '<td style="padding: 8px; text-align: center; font-weight: bold;">' . $total_participantes . '</td>';
echo '<td style="padding: 8px; font-weight: bold;">Total Aprovados:</td>';
echo '<td style="padding: 8px; text-align: center; font-weight: bold; color: #28a745;">' . $total_aprovados . '</td>';
echo '<td style="padding: 8px; font-weight: bold;">Taxa Geral:</td>';
echo '<td colspan="2" style="padding: 8px; text-align: center; font-weight: bold; color: #007bff;">' . 
     ($total_participantes > 0 ? number_format(($total_aprovados / $total_participantes) * 100, 1) : 0) . '%</td>';
echo '</tr>';

echo '<tr>';
echo '<td colspan="2" style="padding: 8px; font-weight: bold;">Em Andamento:</td>';
echo '<td style="padding: 8px; text-align: center; color: #007bff; font-weight: bold;">' . $total_em_andamento . '</td>';
echo '<td style="padding: 8px; font-weight: bold;">Vencidos:</td>';
echo '<td style="padding: 8px; text-align: center; color: #dc3545; font-weight: bold;">' . $total_vencidos . '</td>';
echo '<td style="padding: 8px; font-weight: bold;">A Vencer:</td>';
echo '<td style="padding: 8px; text-align: center; color: #fd7e14; font-weight: bold;">' . $total_a_vencer . '</td>';
echo '<td style="padding: 8px; font-weight: bold;">Problemas:</td>';
echo '<td colspan="2" style="padding: 8px; text-align: center; font-weight: bold; color: #dc3545;">' . 
     ($total_vencidos + $total_a_vencer) . '</td>';
echo '</tr>';

echo '</table>';
?>
