<?php
// Verificar se as variáveis necessárias estão definidas
if (!isset($user)) {
    // Se não estiver definida, tentar obter do sistema de autenticação
    if (isset($_SESSION['user_id'])) {
        $user = [
            'nome_completo' => $_SESSION['nome_completo'] ?? 'Usuário',
            'nivel_acesso' => $_SESSION['nivel_acesso'] ?? 'comum'
        ];
    } else {
        $user = [
            'nome_completo' => 'Usuário',
            'nivel_acesso' => 'comum'
        ];
    }
}

// Garantir que temos acesso ao banco de dados se necessário
if (!isset($pdo)) {
    require_once __DIR__ . '/../config/database.php';
}

// Determinar a página atual para destacar no menu
$current_page = basename($_SERVER['PHP_SELF']);
?>

<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
        <a class="navbar-brand" href="index.php">
            <i class="fas fa-graduation-cap me-2"></i>
            <?php echo defined('EDU_PROJECT_NAME') ? EDU_PROJECT_NAME : 'Educação Corporativa'; ?>
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link <?php echo ($current_page == 'index.php' || $current_page == 'analise_colaboradores.php') ? 'active' : ''; ?>" href="analise_colaboradores.php">
                        <i class="fas fa-home me-1"></i> Início
                    </a>
                </li>
                <?php if ($edu_permissions->canManageImports()): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo ($current_page == 'importar.php') ? 'active' : ''; ?>" href="importar.php">
                        <i class="fas fa-file-upload me-1"></i> Importar
                    </a>
                </li>
                <?php endif; ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo ($current_page == 'relatorios.php') ? 'active' : ''; ?>" href="relatorios.php">
                        <i class="fas fa-chart-bar me-1"></i> Relatórios
                    </a>
                </li>
                <?php if ($edu_permissions->canManageTrilhas()): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo ($current_page == 'gerenciar_trilhas.php') ? 'active' : ''; ?>" href="gerenciar_trilhas.php">
                        <i class="fas fa-cogs me-1"></i> Trilhas
                    </a>
                </li>
                <?php endif; ?>
                <?php if ($edu_permissions->isGestor() || $edu_permissions->isAdmin()): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo ($current_page == 'emails.php') ? 'active' : ''; ?>" href="emails.php">
                        <i class="fas fa-envelope me-1"></i> E-mails
                    </a>
                </li>
                <?php endif; ?>
                <?php if ($edu_permissions->canManagePermissions()): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo ($current_page == 'permissions.php') ? 'active' : ''; ?>" href="permissions.php">
                        <i class="fas fa-user-shield me-1"></i> Permissões
                    </a>
                </li>
                <?php endif; ?>

            </ul>
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        <?php
                        // Buscar nome do usuário com fallbacks
                        $nome_completo = 'Usuário'; // Valor padrão

                        // 1º: Tentar da variável $user (se definida pelo edu_auth_check.php)
                        if (isset($user['nome_completo']) && !empty($user['nome_completo']) && $user['nome_completo'] !== 'Usuário') {
                            $nome_completo = $user['nome_completo'];
                        }
                        // 2º: Tentar da sessão
                        elseif (isset($_SESSION['nome_completo']) && !empty($_SESSION['nome_completo'])) {
                            $nome_completo = $_SESSION['nome_completo'];
                        }
                        // 3º: Buscar diretamente do banco de dados
                        elseif (isset($_SESSION['user_id']) && isset($pdo)) {
                            try {
                                $stmt = $pdo->prepare("SELECT nome_completo FROM usuarios WHERE id = ?");
                                $stmt->execute([$_SESSION['user_id']]);
                                $usuario_db = $stmt->fetch();
                                if ($usuario_db && !empty($usuario_db['nome_completo'])) {
                                    $nome_completo = $usuario_db['nome_completo'];
                                }
                            } catch (Exception $e) {
                                // Manter "Usuário" se houver erro
                            }
                        }

                        // Mostrar apenas o primeiro nome para economizar espaço
                        $primeiro_nome = explode(' ', $nome_completo)[0];
                        echo htmlspecialchars($primeiro_nome);
                        ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><h6 class="dropdown-header">
                            <i class="fas fa-id-badge me-1"></i>
                            <?php
                            // Usar informações do sistema de educação se disponível
                            if (isset($edu_permissions)) {
                                echo htmlspecialchars($edu_permissions->getNivelTexto());
                            } else {
                                $nivel_texto = [
                                    'admin' => 'Administrador',
                                    'gestor' => 'Gestor',
                                    'comum' => 'Usuário'
                                ];
                                echo $nivel_texto[$user['nivel_acesso']] ?? 'Usuário';
                            }
                            ?>
                        </h6></li>
                        <li><small class="dropdown-item-text text-muted">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($nome_completo); ?>
                        </small></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../../dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i> Dashboard
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="../../logout.php">
                            <i class="fas fa-sign-out-alt me-1"></i> Sair
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<style>
:root {
    --sicoob-verde-escuro: #003641;
    --sicoob-verde-medio: #00AE9D;
    --sicoob-verde-claro: #C9D200;
    --sicoob-turquesa: #00AE9D;
    --sicoob-roxo: #49479D;
    --sicoob-branco: #FFFFFF;
    --sicoob-cinza: #58595B;
}

.navbar {
    background-color: var(--sicoob-verde-escuro) !important;
    box-shadow: 0 2px 8px rgba(0, 54, 65, 0.2);
    padding: 0.3rem 0;
    white-space: nowrap;
}

.navbar-brand {
    color: var(--sicoob-branco) !important;
    font-weight: 600;
    font-size: 1.2rem;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.navbar-brand:hover {
    color: var(--sicoob-verde-claro) !important;
    transform: scale(1.02);
}

.navbar-nav {
    flex-wrap: nowrap !important;
}

.navbar-nav .nav-link {
    color: var(--sicoob-branco) !important;
    padding: 0.5rem 0.8rem;
    transition: all 0.2s ease;
    font-weight: 500;
    position: relative;
    border-radius: 4px;
    margin: 0 0.05rem;
    font-size: 0.9rem;
    white-space: nowrap;
}

.navbar-nav .nav-link:hover {
    background-color: var(--sicoob-turquesa);
    color: var(--sicoob-branco) !important;
}

.navbar-nav .nav-link.active {
    background-color: var(--sicoob-verde-claro);
    color: var(--sicoob-verde-escuro) !important;
    font-weight: 600;
}

.navbar-nav .nav-link.active:hover {
    background-color: var(--sicoob-verde-claro);
    color: var(--sicoob-verde-escuro) !important;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 4px 12px rgba(0, 54, 65, 0.15);
    border-radius: 8px;
    padding: 0.5rem;
    margin-top: 0.5rem;
    background-color: var(--sicoob-branco);
}

.dropdown-item {
    padding: 0.7rem 1rem;
    transition: all 0.2s ease;
    border-radius: 4px;
    color: var(--sicoob-verde-escuro);
}

.dropdown-item:hover {
    background-color: rgba(0, 54, 65, 0.05);
    color: var(--sicoob-verde-escuro);
    transform: translateX(5px);
}

.dropdown-item.text-danger:hover {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.dropdown-item i {
    margin-right: 0.7rem;
    width: 20px;
    text-align: center;
    color: var(--sicoob-turquesa);
}

.dropdown-item.text-danger i {
    color: #dc3545;
}

.dropdown-header {
    color: var(--sicoob-verde-escuro);
    font-weight: 600;
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
}

.dropdown-item-text {
    padding: 0.25rem 1rem;
    font-size: 0.8rem;
    color: var(--sicoob-cinza) !important;
}

.navbar-toggler {
    border-color: rgba(255, 255, 255, 0.3);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Responsividade */
@media (max-width: 1199.98px) {
    .navbar-brand {
        font-size: 1.1rem;
    }

    .navbar-nav .nav-link {
        padding: 0.4rem 0.6rem;
        font-size: 0.85rem;
    }
}

@media (max-width: 991.98px) {
    .navbar {
        white-space: normal;
    }

    .navbar-collapse {
        padding: 0.5rem 0;
    }

    .navbar-nav {
        flex-wrap: wrap !important;
    }

    .navbar-nav .nav-link {
        padding: 0.6rem 1rem;
        margin: 0.1rem 0;
        white-space: normal;
    }

    .dropdown-menu {
        border: none;
        box-shadow: none;
        background-color: rgba(255, 255, 255, 0.1);
        padding: 0.5rem;
    }

    .dropdown-item {
        color: var(--sicoob-branco);
    }

    .dropdown-item:hover {
        background-color: var(--sicoob-turquesa);
        color: var(--sicoob-branco);
        transform: none;
    }

    .dropdown-item i {
        color: var(--sicoob-verde-claro);
    }

    .dropdown-header {
        color: var(--sicoob-verde-claro);
    }
}
</style>
