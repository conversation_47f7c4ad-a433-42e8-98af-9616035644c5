<?php
/**
 * Debug das Fotos dos Colaboradores
 * 
 * Verificar por que as fotos não estão aparecendo
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'classes/IntranetAPI.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

echo "<h1>🔍 Debug das Fotos dos Colaboradores</h1>";

// Inicializar API da Intranet
$api = new IntranetAPI();

echo "<h2>1. 📡 Teste da API da Intranet</h2>";

// Buscar dados da API
$usuarios_intranet = $api->listarUsuarios();

if ($usuarios_intranet === false) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>❌ Erro na API da Intranet</h3>";
    echo "<p>A API não retornou dados válidos.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>✅ API da Intranet Funcionando</h3>";
    echo "<p><strong>Total de usuários retornados:</strong> " . count($usuarios_intranet) . "</p>";
    
    // Contar usuários com foto
    $usuarios_com_foto = 0;
    $usuarios_com_foto_url = 0;
    $exemplos_foto = [];
    
    foreach ($usuarios_intranet as $usuario) {
        if (!empty($usuario['foto'])) {
            $usuarios_com_foto++;
        }
        if (!empty($usuario['foto_url'])) {
            $usuarios_com_foto_url++;
            if (count($exemplos_foto) < 3) {
                $exemplos_foto[] = $usuario;
            }
        }
    }
    
    echo "<p><strong>Usuários com campo 'foto':</strong> $usuarios_com_foto</p>";
    echo "<p><strong>Usuários com campo 'foto_url':</strong> $usuarios_com_foto_url</p>";
    echo "</div>";
    
    // Mostrar exemplos de usuários com foto
    if (!empty($exemplos_foto)) {
        echo "<h3>📸 Exemplos de Usuários com Foto:</h3>";
        foreach ($exemplos_foto as $i => $usuario) {
            echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #007bff;'>";
            echo "<h4>Usuário " . ($i + 1) . ":</h4>";
            echo "<p><strong>Nome:</strong> " . htmlspecialchars($usuario['nome'] ?? 'N/A') . "</p>";
            echo "<p><strong>CPF:</strong> " . htmlspecialchars($usuario['cpf'] ?? 'N/A') . "</p>";
            echo "<p><strong>Campo 'foto':</strong> " . htmlspecialchars($usuario['foto'] ?? 'N/A') . "</p>";
            echo "<p><strong>Campo 'foto_url':</strong> " . htmlspecialchars($usuario['foto_url'] ?? 'N/A') . "</p>";
            
            if (!empty($usuario['foto_url'])) {
                echo "<p><strong>Teste da Imagem:</strong></p>";
                echo "<img src='" . htmlspecialchars($usuario['foto_url']) . "' alt='Teste' style='width: 80px; height: 80px; border-radius: 50%; object-fit: cover; border: 2px solid #007bff;' onerror=\"this.style.border='2px solid #dc3545'; this.alt='❌ Erro ao carregar';\">";
            }
            echo "</div>";
        }
    }
}

echo "<h2>2. 🗃️ Teste do Mapeamento por CPF</h2>";

// Criar mapa de usuários por CPF (como no analise_colaboradores.php)
$mapa_usuarios_cpf = [];
if ($usuarios_intranet !== false) {
    foreach ($usuarios_intranet as $usuario) {
        if (!empty($usuario['cpf'])) {
            // Normalizar CPF (remover pontos e traços, completar com zeros)
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
            $mapa_usuarios_cpf[$cpf_normalizado] = $usuario;
        }
    }
}

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🗂️ Mapa de Usuários por CPF</h3>";
echo "<p><strong>Total de usuários mapeados:</strong> " . count($mapa_usuarios_cpf) . "</p>";

// Contar usuários com foto no mapa
$mapeados_com_foto = 0;
foreach ($mapa_usuarios_cpf as $cpf => $usuario) {
    if (!empty($usuario['foto_url'])) {
        $mapeados_com_foto++;
    }
}
echo "<p><strong>Usuários mapeados com foto_url:</strong> $mapeados_com_foto</p>";
echo "</div>";

echo "<h2>3. 👥 Teste com Colaboradores Reais</h2>";

// Buscar alguns colaboradores da base de educação
$query_colaboradores = "
    SELECT DISTINCT cpf, usuario, email
    FROM edu_relatorio_educacao
    LIMIT 10
";

$stmt = $pdo_edu->prepare($query_colaboradores);
$stmt->execute();
$colaboradores_teste = $stmt->fetchAll();

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🧪 Teste com Colaboradores da Base</h3>";

foreach ($colaboradores_teste as $colaborador) {
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;
    
    echo "<div style='background: #ffffff; padding: 10px; border-radius: 5px; margin: 10px 0; border: 1px solid #dee2e6;'>";
    echo "<h4>" . htmlspecialchars($colaborador['usuario']) . "</h4>";
    echo "<p><strong>CPF:</strong> " . htmlspecialchars($colaborador['cpf']) . " (normalizado: $cpf_normalizado)</p>";
    
    if ($usuario_intranet) {
        echo "<p style='color: green;'>✅ <strong>Encontrado na Intranet</strong></p>";
        echo "<p><strong>Nome Intranet:</strong> " . htmlspecialchars($usuario_intranet['nome'] ?? 'N/A') . "</p>";
        echo "<p><strong>Email Intranet:</strong> " . htmlspecialchars($usuario_intranet['email'] ?? 'N/A') . "</p>";
        echo "<p><strong>Campo 'foto':</strong> " . htmlspecialchars($usuario_intranet['foto'] ?? 'N/A') . "</p>";
        echo "<p><strong>Campo 'foto_url':</strong> " . htmlspecialchars($usuario_intranet['foto_url'] ?? 'N/A') . "</p>";
        
        if (!empty($usuario_intranet['foto_url'])) {
            echo "<p><strong>Teste da Foto:</strong></p>";
            echo "<img src='" . htmlspecialchars($usuario_intranet['foto_url']) . "' alt='Foto' style='width: 60px; height: 60px; border-radius: 50%; object-fit: cover; border: 2px solid #28a745;' onerror=\"this.style.border='2px solid #dc3545'; this.alt='❌ Erro ao carregar';\">";
        } else {
            echo "<p style='color: orange;'>⚠️ <strong>Sem foto_url</strong></p>";
        }
    } else {
        echo "<p style='color: red;'>❌ <strong>Não encontrado na Intranet</strong></p>";
    }
    echo "</div>";
}
echo "</div>";

echo "<h2>4. 🔧 Diagnóstico do Problema</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔍 Possíveis Causas:</h3>";

echo "<h4>1. Verificar se a API está retornando o campo 'foto':</h4>";
if ($usuarios_com_foto > 0) {
    echo "<p style='color: green;'>✅ API retorna campo 'foto' para $usuarios_com_foto usuários</p>";
} else {
    echo "<p style='color: red;'>❌ API não retorna campo 'foto' para nenhum usuário</p>";
}

echo "<h4>2. Verificar se o processamento foto_url está funcionando:</h4>";
if ($usuarios_com_foto_url > 0) {
    echo "<p style='color: green;'>✅ Processamento foto_url funcionando para $usuarios_com_foto_url usuários</p>";
} else {
    echo "<p style='color: red;'>❌ Processamento foto_url não está funcionando</p>";
}

echo "<h4>3. Verificar se o mapeamento por CPF está funcionando:</h4>";
if ($mapeados_com_foto > 0) {
    echo "<p style='color: green;'>✅ Mapeamento por CPF funcionando para $mapeados_com_foto usuários</p>";
} else {
    echo "<p style='color: red;'>❌ Mapeamento por CPF não encontra usuários com foto</p>";
}

echo "<h4>4. Verificar URLs das fotos:</h4>";
if (!empty($exemplos_foto)) {
    echo "<p>📋 <strong>Exemplos de URLs geradas:</strong></p>";
    foreach ($exemplos_foto as $i => $usuario) {
        echo "<p>" . ($i + 1) . ". " . htmlspecialchars($usuario['foto_url']) . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Nenhuma URL de foto foi gerada</p>";
}

echo "</div>";

echo "<h2>5. 🛠️ Soluções Possíveis</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>💡 Próximos Passos:</h3>";

echo "<h4>Se o problema for na API:</h4>";
echo "<ul>";
echo "<li>Verificar se a API da Intranet está retornando o campo 'foto'</li>";
echo "<li>Verificar credenciais e permissões da API</li>";
echo "<li>Testar a API diretamente</li>";
echo "</ul>";

echo "<h4>Se o problema for no processamento:</h4>";
echo "<ul>";
echo "<li>Verificar se a classe IntranetAPI está processando corretamente</li>";
echo "<li>Verificar se o cache não está interferindo</li>";
echo "<li>Limpar cache da API</li>";
echo "</ul>";

echo "<h4>Se o problema for no mapeamento:</h4>";
echo "<ul>";
echo "<li>Verificar normalização de CPFs</li>";
echo "<li>Verificar se os CPFs da base educação coincidem com os da Intranet</li>";
echo "<li>Verificar se há colaboradores com CPFs válidos</li>";
echo "</ul>";

echo "<h4>Se o problema for nas URLs:</h4>";
echo "<ul>";
echo "<li>Verificar se o servidor de fotos está acessível</li>";
echo "<li>Verificar se as URLs estão sendo construídas corretamente</li>";
echo "<li>Testar URLs manualmente</li>";
echo "</ul>";

echo "</div>";

echo "<p><em>Debug executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📊 Voltar para Análise</a>";
echo "<a href='test_api.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🧪 Testar API</a>";
echo "</p>";
?>
