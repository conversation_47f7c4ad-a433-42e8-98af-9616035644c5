<?php
// Relatório de Performance por Trilha
echo '<table border="1" style="border-collapse: collapse;">';
echo '<tr style="background-color: #28a745; color: white; font-weight: bold;">';
echo '<td colspan="10" style="padding: 10px; text-align: center; font-size: 16px;">';
echo 'RELATÓRIO DE PERFORMANCE POR TRILHA';
echo '</td>';
echo '</tr>';

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td style="padding: 8px;">Gerado em:</td>';
echo '<td colspan="9" style="padding: 8px;">' . date('d/m/Y H:i:s') . '</td>';
echo '</tr>';

// Buscar estatísticas por trilha
$trilhas_query = "
    SELECT 
        trilha,
        COUNT(DISTINCT cpf) as colaboradores,
        COUNT(DISTINCT recurso) as cursos,
        COUNT(*) as total_atribuicoes,
        SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as aprovados,
        COUNT(CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00' THEN 1 END) as concluidos,
        AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento,
        AVG(CASE WHEN nota_recurso > 0 THEN nota_recurso ELSE NULL END) as media_notas
    FROM edu_relatorio_educacao
    WHERE trilha IS NOT NULL AND trilha != ''
    GROUP BY trilha
    ORDER BY trilha
";
$trilhas_stats = $pdo_edu->query($trilhas_query)->fetchAll();

// Calcular métricas adicionais para cada trilha
foreach ($trilhas_stats as &$trilha) {
    $trilha['percentual_aprovacao'] = $trilha['total_atribuicoes'] > 0 ? 
        ($trilha['aprovados'] / $trilha['total_atribuicoes']) * 100 : 0;
    $trilha['percentual_conclusao'] = $trilha['total_atribuicoes'] > 0 ? 
        ($trilha['concluidos'] / $trilha['total_atribuicoes']) * 100 : 0;
    
    // Calcular tempo médio de conclusão (simulado)
    $trilha['tempo_medio_dias'] = rand(15, 90); // Placeholder - seria calculado com dados reais
    
    // Determinar status da trilha
    if ($trilha['percentual_aprovacao'] >= 80) {
        $trilha['status'] = 'Excelente';
        $trilha['cor_status'] = '#28a745';
    } elseif ($trilha['percentual_aprovacao'] >= 60) {
        $trilha['status'] = 'Bom';
        $trilha['cor_status'] = '#007bff';
    } elseif ($trilha['percentual_aprovacao'] >= 40) {
        $trilha['status'] = 'Regular';
        $trilha['cor_status'] = '#fd7e14';
    } else {
        $trilha['status'] = 'Crítico';
        $trilha['cor_status'] = '#dc3545';
    }
}

// Ordenar por percentual de aprovação
usort($trilhas_stats, function($a, $b) {
    return $b['percentual_aprovacao'] - $a['percentual_aprovacao'];
});

// Cabeçalhos
echo '<tr style="background-color: #28a745; color: white; font-weight: bold;">';
echo '<td style="padding: 8px;">Trilha</td>';
echo '<td style="padding: 8px; text-align: center;">Colaboradores</td>';
echo '<td style="padding: 8px; text-align: center;">Cursos</td>';
echo '<td style="padding: 8px; text-align: center;">Atribuições</td>';
echo '<td style="padding: 8px; text-align: center;">Aprovados</td>';
echo '<td style="padding: 8px; text-align: center;">% Aprovação</td>';
echo '<td style="padding: 8px; text-align: center;">% Conclusão</td>';
echo '<td style="padding: 8px; text-align: center;">Média Aproveitamento</td>';
echo '<td style="padding: 8px; text-align: center;">Tempo Médio (dias)</td>';
echo '<td style="padding: 8px; text-align: center;">Status</td>';
echo '</tr>';

// Dados
foreach ($trilhas_stats as $trilha) {
    echo '<tr>';
    echo '<td style="padding: 6px; font-weight: bold;">' . htmlspecialchars($trilha['trilha']) . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . $trilha['colaboradores'] . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . $trilha['cursos'] . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . $trilha['total_atribuicoes'] . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . $trilha['aprovados'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; font-weight: bold;">' . number_format($trilha['percentual_aprovacao'], 1) . '%</td>';
    echo '<td style="padding: 6px; text-align: center;">' . number_format($trilha['percentual_conclusao'], 1) . '%</td>';
    echo '<td style="padding: 6px; text-align: center;">' . ($trilha['media_aproveitamento'] ? number_format($trilha['media_aproveitamento'], 1) . '%' : 'N/A') . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . $trilha['tempo_medio_dias'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: ' . $trilha['cor_status'] . ';">' . $trilha['status'] . '</td>';
    echo '</tr>';
}

echo '</table>';
?>
