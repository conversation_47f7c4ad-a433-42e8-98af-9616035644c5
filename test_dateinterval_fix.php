<?php
/**
 * Teste de Correção do DateInterval
 * 
 * Este arquivo testa se o erro de DateInterval foi corrigido.
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'classes/IntranetAPI.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

echo "<h1>📅 Teste de Correção - DateInterval</h1>";

// Função de teste (cópia da função corrigida)
function testarCalculoPrazo($cpf, $codigo_trilha, $codigo_recurso, $data_admissao, $prazo_padrao, $pdo) {
    // Validar parâmetros de entrada
    if (empty($cpf) || empty($codigo_trilha) || empty($codigo_recurso) || empty($data_admissao)) {
        return ['resultado' => $prazo_padrao, 'motivo' => 'Parâmetros inválidos'];
    }
    
    // Validar data de admissão
    if ($data_admissao === '0000-00-00' || !strtotime($data_admissao)) {
        return ['resultado' => $prazo_padrao, 'motivo' => 'Data de admissão inválida'];
    }
    
    // Buscar configuração do prazo personalizado
    $query_config = "
        SELECT primeiro_prazo_dias, renovacao_prazo_dias
        FROM edu_prazos_personalizados
        WHERE codigo_trilha = ? AND codigo_recurso = ? AND prazo_personalizado_ativo = 1
    ";
    
    $stmt_config = $pdo->prepare($query_config);
    $stmt_config->execute([$codigo_trilha, $codigo_recurso]);
    $config = $stmt_config->fetch(PDO::FETCH_ASSOC);
    
    if (!$config) {
        return ['resultado' => $prazo_padrao, 'motivo' => 'Sem configuração personalizada'];
    }
    
    // Validar se os valores de prazo são válidos
    $primeiro_prazo = (int)$config['primeiro_prazo_dias'];
    $renovacao_prazo = (int)$config['renovacao_prazo_dias'];
    
    if ($primeiro_prazo <= 0 && $renovacao_prazo <= 0) {
        return ['resultado' => $prazo_padrao, 'motivo' => 'Prazos configurados inválidos'];
    }
    
    // Verificar se já houve conclusões anteriores
    $query_conclusoes = "
        SELECT data_conclusao
        FROM edu_relatorio_educacao
        WHERE cpf = ? AND codigo_trilha = ? AND codigo_recurso = ?
        AND data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00'
        ORDER BY data_conclusao DESC
        LIMIT 1
    ";
    
    $stmt_conclusoes = $pdo->prepare($query_conclusoes);
    $stmt_conclusoes->execute([$cpf, $codigo_trilha, $codigo_recurso]);
    $ultima_conclusao = $stmt_conclusoes->fetch(PDO::FETCH_ASSOC);
    
    try {
        if ($ultima_conclusao) {
            // Renovação: usar data da última conclusão + prazo de renovação
            $dias_renovacao = (int)$config['renovacao_prazo_dias'];
            if ($dias_renovacao <= 0) {
                return ['resultado' => $prazo_padrao, 'motivo' => 'Prazo de renovação inválido'];
            }
            
            // Validar data de conclusão
            if ($ultima_conclusao['data_conclusao'] === '0000-00-00' || !strtotime($ultima_conclusao['data_conclusao'])) {
                return ['resultado' => $prazo_padrao, 'motivo' => 'Data de conclusão inválida'];
            }
            
            $data_base = new DateTime($ultima_conclusao['data_conclusao']);
            $data_base->add(new DateInterval('P' . $dias_renovacao . 'D'));
            
            return [
                'resultado' => $data_base->format('Y-m-d'),
                'motivo' => 'Renovação calculada',
                'base' => $ultima_conclusao['data_conclusao'],
                'dias' => $dias_renovacao
            ];
        } else {
            // Primeira vez: usar data de admissão + primeiro prazo
            $dias_primeiro = (int)$config['primeiro_prazo_dias'];
            if ($dias_primeiro <= 0) {
                return ['resultado' => $prazo_padrao, 'motivo' => 'Primeiro prazo inválido'];
            }
            
            $data_base = new DateTime($data_admissao);
            $data_base->add(new DateInterval('P' . $dias_primeiro . 'D'));
            
            return [
                'resultado' => $data_base->format('Y-m-d'),
                'motivo' => 'Primeiro prazo calculado',
                'base' => $data_admissao,
                'dias' => $dias_primeiro
            ];
        }
        
    } catch (Exception $e) {
        return ['resultado' => $prazo_padrao, 'motivo' => 'Erro: ' . $e->getMessage()];
    }
}

// Teste 1: Verificar dados problemáticos
echo "<h2>1. 🔍 Verificação de Dados Problemáticos</h2>";

try {
    // Buscar registros que podem ter causado o erro
    $query_problematicos = "
        SELECT DISTINCT 
            cpf, codigo_trilha, codigo_recurso, data_admissao, concluir_trilha_ate
        FROM edu_relatorio_educacao 
        WHERE cpf IS NOT NULL 
        AND (data_admissao IS NULL OR data_admissao = '0000-00-00' OR data_admissao = '')
        LIMIT 5
    ";
    
    $stmt = $pdo_edu->prepare($query_problematicos);
    $stmt->execute();
    $registros_problematicos = $stmt->fetchAll();
    
    if (!empty($registros_problematicos)) {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>⚠️ <strong>Registros com Datas Problemáticas Encontrados:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 5px;'>CPF</th>";
        echo "<th style='padding: 5px;'>Trilha</th>";
        echo "<th style='padding: 5px;'>Recurso</th>";
        echo "<th style='padding: 5px;'>Data Admissão</th>";
        echo "<th style='padding: 5px;'>Prazo Padrão</th>";
        echo "</tr>";
        
        foreach ($registros_problematicos as $registro) {
            echo "<tr>";
            echo "<td style='padding: 5px;'>" . substr($registro['cpf'], 0, 3) . "***</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($registro['codigo_trilha']) . "</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($registro['codigo_recurso']) . "</td>";
            echo "<td style='padding: 5px;'>" . ($registro['data_admissao'] ?: 'NULL/VAZIO') . "</td>";
            echo "<td style='padding: 5px;'>" . ($registro['concluir_trilha_ate'] ?: 'NULL/VAZIO') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>Nenhum registro com data de admissão problemática encontrado</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro ao verificar dados problemáticos:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 2: Testar função corrigida
echo "<h2>2. 🧪 Teste da Função Corrigida</h2>";

try {
    // Buscar alguns registros válidos para teste
    $query_validos = "
        SELECT DISTINCT 
            cpf, codigo_trilha, codigo_recurso, data_admissao, concluir_trilha_ate
        FROM edu_relatorio_educacao 
        WHERE cpf IS NOT NULL 
        AND data_admissao IS NOT NULL 
        AND data_admissao != '0000-00-00' 
        AND data_admissao != ''
        LIMIT 3
    ";
    
    $stmt = $pdo_edu->prepare($query_validos);
    $stmt->execute();
    $registros_validos = $stmt->fetchAll();
    
    if (!empty($registros_validos)) {
        echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>Teste com Registros Válidos:</strong></p>";
        
        foreach ($registros_validos as $registro) {
            $resultado = testarCalculoPrazo(
                $registro['cpf'],
                $registro['codigo_trilha'],
                $registro['codigo_recurso'],
                $registro['data_admissao'],
                $registro['concluir_trilha_ate'],
                $pdo_edu
            );
            
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<h6><strong>Teste CPF: " . substr($registro['cpf'], 0, 3) . "***</strong></h6>";
            echo "<p><strong>Trilha:</strong> " . htmlspecialchars($registro['codigo_trilha']) . "</p>";
            echo "<p><strong>Recurso:</strong> " . htmlspecialchars($registro['codigo_recurso']) . "</p>";
            echo "<p><strong>Data Admissão:</strong> " . $registro['data_admissao'] . "</p>";
            echo "<p><strong>Prazo Padrão:</strong> " . ($registro['concluir_trilha_ate'] ?: 'N/A') . "</p>";
            echo "<p><strong>Resultado:</strong> " . $resultado['resultado'] . "</p>";
            echo "<p><strong>Motivo:</strong> " . $resultado['motivo'] . "</p>";
            if (isset($resultado['base']) && isset($resultado['dias'])) {
                echo "<p><strong>Base:</strong> " . $resultado['base'] . " + " . $resultado['dias'] . " dias</p>";
            }
            echo "</div>";
        }
        echo "</div>";
        
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>⚠️ <strong>Nenhum registro válido encontrado para teste</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro no teste da função:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 3: Verificar configurações de prazos
echo "<h2>3. ⚙️ Verificação de Configurações</h2>";

try {
    $query_config = "
        SELECT codigo_trilha, codigo_recurso, primeiro_prazo_dias, renovacao_prazo_dias
        FROM edu_prazos_personalizados 
        WHERE prazo_personalizado_ativo = 1
        LIMIT 5
    ";
    
    $stmt = $pdo_edu->prepare($query_config);
    $stmt->execute();
    $configuracoes = $stmt->fetchAll();
    
    if (!empty($configuracoes)) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>Configurações de Prazos Ativas:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 5px;'>Trilha</th>";
        echo "<th style='padding: 5px;'>Recurso</th>";
        echo "<th style='padding: 5px;'>Primeiro Prazo</th>";
        echo "<th style='padding: 5px;'>Renovação</th>";
        echo "<th style='padding: 5px;'>Status</th>";
        echo "</tr>";
        
        foreach ($configuracoes as $config) {
            $primeiro = (int)$config['primeiro_prazo_dias'];
            $renovacao = (int)$config['renovacao_prazo_dias'];
            $status = ($primeiro > 0 || $renovacao > 0) ? '✅ Válido' : '❌ Inválido';
            
            echo "<tr>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($config['codigo_trilha']) . "</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($config['codigo_recurso']) . "</td>";
            echo "<td style='padding: 5px;'>" . $primeiro . " dias</td>";
            echo "<td style='padding: 5px;'>" . $renovacao . " dias</td>";
            echo "<td style='padding: 5px;'>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>⚠️ <strong>Nenhuma configuração de prazo ativa encontrada</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro ao verificar configurações:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Resumo final
echo "<h2>4. 📋 Resumo da Correção</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔧 Correções Implementadas</h3>";

echo "<h4>❌ Erro Original:</h4>";
echo "<ul>";
echo "<li><strong>Mensagem:</strong> Unknown or bad format (PD) in DateInterval</li>";
echo "<li><strong>Causa:</strong> Valores nulos ou vazios sendo passados para DateInterval</li>";
echo "<li><strong>Linha:</strong> detalhes_colaborador.php:229</li>";
echo "</ul>";

echo "<h4>✅ Validações Adicionadas:</h4>";
echo "<ul>";
echo "<li><strong>Parâmetros de Entrada:</strong> Verificação de valores nulos/vazios</li>";
echo "<li><strong>Data de Admissão:</strong> Validação de formato e valor</li>";
echo "<li><strong>Valores de Prazo:</strong> Conversão para inteiro e validação > 0</li>";
echo "<li><strong>Data de Conclusão:</strong> Verificação antes de usar</li>";
echo "</ul>";

echo "<h4>✅ Tratamento de Erros:</h4>";
echo "<ul>";
echo "<li><strong>Try-Catch:</strong> Captura qualquer erro de DateTime/DateInterval</li>";
echo "<li><strong>Fallback:</strong> Retorna prazo padrão em caso de erro</li>";
echo "<li><strong>Logs:</strong> Erros são tratados silenciosamente</li>";
echo "<li><strong>Robustez:</strong> Sistema continua funcionando mesmo com dados inválidos</li>";
echo "</ul>";

echo "<h4>✅ Arquivos Corrigidos:</h4>";
echo "<ul>";
echo "<li><strong>detalhes_colaborador.php:</strong> Função calcularPrazoPersonalizado()</li>";
echo "<li><strong>analise_colaboradores.php:</strong> Mesma função corrigida</li>";
echo "<li><strong>Validações:</strong> Implementadas em ambos os arquivos</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>👥 Testar Modal Corrigido</a>";
echo "<a href='detalhes_colaborador.php?cpf=08813368666' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>📋 Testar Detalhes Direto</a>";
echo "</p>";
?>
