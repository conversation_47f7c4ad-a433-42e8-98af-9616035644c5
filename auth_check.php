<?php
// Herda o sistema de autenticação do projeto principal
session_start();
require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../classes/Permissions.php';
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/classes/IntranetAPI.php';

// Criar alias para compatibilidade com código que usa $pdo_edu
$pdo_edu = $pdo;

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit;
}

// Criar instância de Permissions
$permissions = new Permissions($pdo, $_SESSION['user_id']);

// Função para verificar acesso à página
function checkPageAccess($required_level) {
    global $permissions;
    
    switch ($required_level) {
        case 'admin':
            if (!$permissions->isAdmin()) {
                header('Location: ../../access_denied.php');
                exit;
            }
            break;
        case 'gestor':
            if (!$permissions->isGestor() && !$permissions->isAdmin()) {
                header('Location: ../../access_denied.php');
                exit;
            }
            break;
    }
}

// Obter informações do usuário logado
try {
    $stmt = $pdo->prepare("SELECT id, username, nome_completo, email FROM usuarios WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        session_destroy();
        header('Location: ../../login.php');
        exit;
    }
} catch (PDOException $e) {
    error_log('Erro ao buscar dados do usuário: ' . $e->getMessage());
    header('Location: ../../login.php');
    exit;
}
?>
