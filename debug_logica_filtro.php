<?php
/**
 * Debug específico da lógica de filtro para identificar onde está o problema
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/IntranetAPI.php';

echo "<h1>🐛 Debug: Lógica de Filtro Detalhada</h1>\n";

// Criar instância da API
$api = new IntranetAPI();

// Buscar dados da API SEM CACHE
echo "<h2>1. 📊 Dados da API (SEM CACHE)</h2>\n";
$usuarios_intranet_todos = $api->listarUsuarios(false, false);
$usuarios_intranet_ativos = $api->listarUsuariosAtivos(false);

echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>API Status:</strong></p>";
echo "<ul>";
echo "<li><strong>Total usuários:</strong> " . count($usuarios_intranet_todos) . "</li>";
echo "<li><strong>Usuários ativos:</strong> " . count($usuarios_intranet_ativos) . "</li>";
echo "<li><strong>Usuários inativos:</strong> " . (count($usuarios_intranet_todos) - count($usuarios_intranet_ativos)) . "</li>";
echo "</ul>";
echo "</div>";

// Criar mapas
$mapa_usuarios_ativos = [];
$mapa_usuarios_todos = [];

foreach ($usuarios_intranet_ativos as $usuario) {
    if (!empty($usuario['cpf'])) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
        $mapa_usuarios_ativos[$cpf_normalizado] = $usuario;
    }
}

foreach ($usuarios_intranet_todos as $usuario) {
    if (!empty($usuario['cpf'])) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
        $mapa_usuarios_todos[$cpf_normalizado] = $usuario;
    }
}

echo "<h2>2. 🔍 Buscar Colaboradores do Banco</h2>\n";

// Buscar colaboradores do banco (mesma query da página principal)
$colaboradores_query = "
    SELECT
        cpf,
        MAX(usuario) as usuario,
        MAX(email) as email,
        MAX(funcao) as funcao,
        MAX(codigo_unidade) as codigo_unidade,
        MAX(data_admissao) as data_admissao,
        COUNT(DISTINCT trilha) as total_trilhas,
        COUNT(DISTINCT CONCAT(codigo_trilha, '|', codigo_recurso)) as total_cursos,
        COUNT(DISTINCT CASE WHEN aprovacao = 'Sim' THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_aprovados,
        COUNT(DISTINCT CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00' THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_concluidos,
        0 as cursos_a_vencer,
        0 as cursos_vencidos,
        AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento,
        MAX(data_importacao) as ultima_atualizacao
    FROM edu_relatorio_educacao
    GROUP BY cpf
    ORDER BY MAX(usuario)
    LIMIT 20
";

$stmt_colaboradores = $pdo_edu->prepare($colaboradores_query);
$stmt_colaboradores->execute();
$todos_colaboradores = $stmt_colaboradores->fetchAll();

echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Colaboradores do banco:</strong> " . count($todos_colaboradores) . " (primeiros 20)</p>";
echo "</div>";

echo "<h2>3. 🔄 Simulação EXATA da Lógica da Página Principal</h2>\n";

$colaboradores_por_pa = [];
$cpfs_processados = [];

echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='padding: 5px;'>CPF</th>";
echo "<th style='padding: 5px;'>Nome</th>";
echo "<th style='padding: 5px;'>Existe Todos?</th>";
echo "<th style='padding: 5px;'>Existe Ativos?</th>";
echo "<th style='padding: 5px;'>Condição Filtro</th>";
echo "<th style='padding: 5px;'>Ação</th>";
echo "<th style='padding: 5px;'>Resultado</th>";
echo "</tr>";

$total_processados = 0;
$total_filtrados = 0;
$total_adicionados = 0;

foreach ($todos_colaboradores as $colaborador) {
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    
    // Verificar se já processamos este CPF
    if (isset($cpfs_processados[$cpf_normalizado])) {
        continue; // Pular colaborador duplicado
    }
    $cpfs_processados[$cpf_normalizado] = true;
    
    $total_processados++;
    
    // Verificar status na Intranet
    $usuario_intranet_ativo = $mapa_usuarios_ativos[$cpf_normalizado] ?? null;
    $usuario_intranet_todos = $mapa_usuarios_todos[$cpf_normalizado] ?? null;
    
    $existe_todos = $usuario_intranet_todos ? 'SIM' : 'NÃO';
    $existe_ativos = $usuario_intranet_ativo ? 'SIM' : 'NÃO';
    
    // APLICAR A LÓGICA EXATA DA PÁGINA PRINCIPAL
    $deve_filtrar = ($usuario_intranet_todos && !$usuario_intranet_ativo);
    $condicao_filtro = $deve_filtrar ? 'VERDADEIRA' : 'FALSA';
    
    if ($deve_filtrar) {
        // Usuário existe na Intranet mas está inativo - PULAR
        $acao = 'continue (FILTRAR)';
        $resultado = '❌ NÃO ADICIONA';
        $cor_linha = '#f8d7da';
        $total_filtrados++;
        
        echo "<tr style='background-color: $cor_linha;'>";
        echo "<td style='padding: 5px;'>" . substr($colaborador['cpf'], 0, 3) . "***</td>";
        echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($colaborador['usuario'], 0, 15)) . "</td>";
        echo "<td style='padding: 5px;'>$existe_todos</td>";
        echo "<td style='padding: 5px;'>$existe_ativos</td>";
        echo "<td style='padding: 5px;'><strong>$condicao_filtro</strong></td>";
        echo "<td style='padding: 5px;'><strong>$acao</strong></td>";
        echo "<td style='padding: 5px;'><strong>$resultado</strong></td>";
        echo "</tr>";
        
        continue; // PULAR - não adicionar aos PAs
    }
    
    // Se chegou aqui, deve ser adicionado
    $total_adicionados++;
    
    // Determinar PA
    $pa_info = ['id' => 'sem_pa', 'numero' => 'S/PA', 'nome' => 'Sem PA Definido'];
    
    if ($usuario_intranet_ativo && !empty($usuario_intranet_ativo['agencia'])) {
        $agencia_id = $usuario_intranet_ativo['agencia'];
        $pa_info = ['id' => $agencia_id, 'numero' => $agencia_id, 'nome' => 'PA ' . $agencia_id];
    }
    
    $pa_key = $pa_info['numero'] . ' - ' . $pa_info['nome'];
    
    if (!isset($colaboradores_por_pa[$pa_key])) {
        $colaboradores_por_pa[$pa_key] = [
            'info' => $pa_info,
            'colaboradores' => [],
            'total_colaboradores' => 0
        ];
    }
    
    $colaboradores_por_pa[$pa_key]['colaboradores'][] = $colaborador;
    $colaboradores_por_pa[$pa_key]['total_colaboradores']++;
    
    $acao = 'ADICIONAR ao PA';
    $resultado = '✅ ADICIONA (' . $pa_key . ')';
    $cor_linha = '#d4edda';
    
    echo "<tr style='background-color: $cor_linha;'>";
    echo "<td style='padding: 5px;'>" . substr($colaborador['cpf'], 0, 3) . "***</td>";
    echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($colaborador['usuario'], 0, 15)) . "</td>";
    echo "<td style='padding: 5px;'>$existe_todos</td>";
    echo "<td style='padding: 5px;'>$existe_ativos</td>";
    echo "<td style='padding: 5px;'><strong>$condicao_filtro</strong></td>";
    echo "<td style='padding: 5px;'><strong>$acao</strong></td>";
    echo "<td style='padding: 5px;'><strong>$resultado</strong></td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

echo "<h2>4. 📊 Resultado da Simulação</h2>\n";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📊 Estatísticas:</h3>";
echo "<ul>";
echo "<li><strong>Total processados:</strong> $total_processados</li>";
echo "<li><strong>Total filtrados (inativos):</strong> $total_filtrados</li>";
echo "<li><strong>Total adicionados aos PAs:</strong> $total_adicionados</li>";
echo "<li><strong>PAs criados:</strong> " . count($colaboradores_por_pa) . "</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📋 PAs Resultantes:</h3>";
foreach ($colaboradores_por_pa as $pa_key => $pa_data) {
    echo "<p><strong>$pa_key:</strong> {$pa_data['total_colaboradores']} colaboradores</p>";
}
echo "</div>";

// Verificar ALICE especificamente
$alice_query = "SELECT cpf, usuario FROM edu_relatorio_educacao WHERE usuario LIKE '%ALICE BEATRIZ%' LIMIT 1";
$stmt_alice = $pdo_edu->prepare($alice_query);
$stmt_alice->execute();
$alice_data = $stmt_alice->fetch();

if ($alice_data) {
    $alice_cpf = str_pad(preg_replace('/[^0-9]/', '', $alice_data['cpf']), 11, '0', STR_PAD_LEFT);
    $alice_ativo = $mapa_usuarios_ativos[$alice_cpf] ?? null;
    $alice_todos = $mapa_usuarios_todos[$alice_cpf] ?? null;
    
    $alice_deve_filtrar = ($alice_todos && !$alice_ativo);
    
    echo "<h2>5. 🔍 Verificação Específica: ALICE BEATRIZ</h2>\n";
    
    echo "<div style='background: " . ($alice_deve_filtrar ? '#f8d7da' : '#d4edda') . "; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>ALICE BEATRIZ DA SILVA:</h3>";
    echo "<ul>";
    echo "<li><strong>Existe na API (todos):</strong> " . ($alice_todos ? 'SIM' : 'NÃO') . "</li>";
    echo "<li><strong>Existe na API (ativos):</strong> " . ($alice_ativo ? 'SIM' : 'NÃO') . "</li>";
    echo "<li><strong>Condição de filtro:</strong> " . ($alice_deve_filtrar ? 'VERDADEIRA' : 'FALSA') . "</li>";
    echo "<li><strong>Deve ser filtrada:</strong> " . ($alice_deve_filtrar ? '✅ SIM' : '❌ NÃO') . "</li>";
    echo "<li><strong>Deve aparecer na página:</strong> " . ($alice_deve_filtrar ? '❌ NÃO' : '✅ SIM') . "</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<p><em>Debug executado em: " . date('d/m/Y H:i:s') . "</em></p>";
?>
