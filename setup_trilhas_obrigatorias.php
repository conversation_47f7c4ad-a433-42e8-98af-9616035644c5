<?php
require_once 'config/database.php';

// Script para criar tabelas de trilhas obrigatórias
echo "<h2>Setup de Trilhas Obrigatórias</h2>";

try {
    // Verificar se as tabelas já existem
    $check_table = $pdo_edu->query("SHOW TABLES LIKE 'edu_trilhas_obrigatorias'");
    if ($check_table->rowCount() > 0) {
        echo "<p style='color: green;'>✅ Tabela 'edu_trilhas_obrigatorias' já existe.</p>";
    } else {
        // Criar tabela edu_trilhas_obrigatorias
        $sql_trilhas = "
        CREATE TABLE edu_trilhas_obrigatorias (
            id INT AUTO_INCREMENT PRIMARY KEY,
            codigo_trilha VARCHAR(50) NOT NULL,
            trilha VARCHAR(255) NOT NULL,
            obrigatoria BOOLEAN DEFAULT FALSE,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            usuario_criacao INT,
            usuario_atualizacao INT,
            
            UNIQUE KEY unique_codigo_trilha (codigo_trilha),
            INDEX idx_obrigatoria (obrigatoria),
            INDEX idx_trilha (trilha)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo_edu->exec($sql_trilhas);
        echo "<p style='color: green;'>✅ Tabela 'edu_trilhas_obrigatorias' criada com sucesso!</p>";
    }

    // Verificar tabela de log
    $check_log = $pdo_edu->query("SHOW TABLES LIKE 'edu_log_trilhas_obrigatorias'");
    if ($check_log->rowCount() > 0) {
        echo "<p style='color: green;'>✅ Tabela 'edu_log_trilhas_obrigatorias' já existe.</p>";
    } else {
        // Criar tabela de log
        $sql_log = "
        CREATE TABLE edu_log_trilhas_obrigatorias (
            id INT AUTO_INCREMENT PRIMARY KEY,
            trilha_id INT NOT NULL,
            acao ENUM('criada', 'atualizada', 'marcada_obrigatoria', 'desmarcada_obrigatoria') NOT NULL,
            valores_anteriores JSON,
            valores_novos JSON,
            usuario_id INT NOT NULL,
            data_acao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            INDEX idx_trilha_id (trilha_id),
            INDEX idx_usuario_id (usuario_id),
            INDEX idx_data_acao (data_acao),
            
            FOREIGN KEY (trilha_id) REFERENCES edu_trilhas_obrigatorias(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo_edu->exec($sql_log);
        echo "<p style='color: green;'>✅ Tabela 'edu_log_trilhas_obrigatorias' criada com sucesso!</p>";
    }

    // Inserir trilhas existentes
    $insert_sql = "
    INSERT IGNORE INTO edu_trilhas_obrigatorias (codigo_trilha, trilha, obrigatoria, usuario_criacao)
    SELECT DISTINCT 
        codigo_trilha, 
        trilha, 
        FALSE,
        1
    FROM edu_relatorio_educacao 
    WHERE codigo_trilha IS NOT NULL 
    AND codigo_trilha != ''
    ORDER BY trilha
    ";
    
    $stmt = $pdo_edu->prepare($insert_sql);
    $stmt->execute();
    $inserted = $stmt->rowCount();
    
    echo "<p style='color: green;'>✅ {$inserted} trilhas inseridas/atualizadas na tabela.</p>";
    
    echo "<h3>Setup Concluído!</h3>";
    echo "<p>Agora você pode:</p>";
    echo "<ul>";
    echo "<li>Acessar <a href='gerenciar_trilhas.php'>Gerenciar Trilhas</a> para marcar trilhas como obrigatórias</li>";
    echo "<li>Usar os filtros de trilhas obrigatórias em <a href='analise_colaboradores.php'>Análise de Colaboradores</a></li>";
    echo "<li>Gerar relatórios com filtros de trilhas obrigatórias em <a href='relatorios.php'>Relatórios</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erro: " . $e->getMessage() . "</p>";
}
?>
