<?php
/**
 * Teste da Correção do Campo andamento_etapa
 * 
 * Verificar se a correção do campo andamento_etapa na função buscarCursosColaborador foi implementada corretamente.
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

echo "<h1>🔧 Teste da Correção do Campo andamento_etapa</h1>";

// Teste 1: Verificar problema identificado
echo "<h2>1. ❌ Problema Identificado</h2>";

echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🚨 Campo Faltante na Consulta SQL:</h3>";
echo "<p><strong>A função buscarCursosColaborador não estava incluindo o campo 'andamento_etapa' na consulta</strong></p>";

echo "<h4>Sintomas do Problema:</h4>";
echo "<ul>";
echo "<li>❌ <strong>Banco de Dados:</strong> 5,459 registros com andamento_etapa preenchido</li>";
echo "<li>❌ <strong>Card Principal:</strong> 'Cursos em Andamento' ainda mostrava 0</li>";
echo "<li>❌ <strong>Consulta SQL:</strong> Campo andamento_etapa não estava sendo selecionado</li>";
echo "<li>❌ <strong>Resultado:</strong> Variável \$curso['andamento_etapa'] sempre vazia</li>";
echo "</ul>";

echo "<h4>Causa Raiz:</h4>";
echo "<ul>";
echo "<li>❌ <strong>SQL Incompleto:</strong> SELECT não incluía andamento_etapa</li>";
echo "<li>❌ <strong>Campo Ausente:</strong> \$curso['andamento_etapa'] sempre NULL</li>";
echo "<li>❌ <strong>Condição Falsa:</strong> !empty(\$curso['andamento_etapa']) sempre false</li>";
echo "<li>❌ <strong>Contagem Zero:</strong> Nenhum curso identificado como 'em andamento'</li>";
echo "</ul>";
echo "</div>";

// Teste 2: Verificar correção implementada
echo "<h2>2. ✅ Correção Implementada</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔧 Campo Adicionado à Consulta:</h3>";

echo "<h4>ANTES (Campo ausente):</h4>";
echo "<pre style='background: #ffffff; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
SELECT
    codigo_trilha,
    trilha,
    codigo_recurso,
    recurso,
    aprovacao,
    data_conclusao,
    concluir_trilha_ate,
    data_admissao,
    nota_recurso,
    aproveitamento,
    validade_recurso
    -- andamento_etapa AUSENTE!
FROM edu_relatorio_educacao
WHERE cpf = ?
");
echo "</pre>";

echo "<h4>DEPOIS (Campo incluído):</h4>";
echo "<pre style='background: #ffffff; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
SELECT
    codigo_trilha,
    trilha,
    codigo_recurso,
    recurso,
    aprovacao,
    data_conclusao,
    concluir_trilha_ate,
    data_admissao,
    nota_recurso,
    aproveitamento,
    validade_recurso,
    andamento_etapa  -- ✅ ADICIONADO!
FROM edu_relatorio_educacao
WHERE cpf = ?
");
echo "</pre>";

echo "<h4>Impacto da Correção:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Campo Disponível:</strong> \$curso['andamento_etapa'] agora contém dados</li>";
echo "<li>✅ <strong>Condição Funcional:</strong> !empty(\$curso['andamento_etapa']) funciona corretamente</li>";
echo "<li>✅ <strong>Contagem Correta:</strong> Cursos em andamento são identificados</li>";
echo "<li>✅ <strong>Card Atualizado:</strong> 'Cursos em Andamento' mostra valor correto</li>";
echo "</ul>";
echo "</div>";

// Teste 3: Verificar dados após correção
echo "<h2>3. 📊 Verificação Após Correção</h2>";

try {
    // Simular a função corrigida
    function buscarCursosColaboradorTeste($cpf, $pdo) {
        $query = "
            SELECT
                codigo_trilha,
                trilha,
                codigo_recurso,
                recurso,
                aprovacao,
                data_conclusao,
                andamento_etapa
            FROM edu_relatorio_educacao
            WHERE cpf = ?
            ORDER BY trilha, recurso
        ";
        
        $stmt = $pdo->prepare($query);
        $stmt->execute([$cpf]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Buscar um CPF que tem andamento_etapa
    $query_cpf_teste = "
        SELECT cpf, usuario, andamento_etapa
        FROM edu_relatorio_educacao 
        WHERE andamento_etapa IS NOT NULL 
        AND andamento_etapa != '' 
        AND aprovacao != 'Sim'
        LIMIT 1
    ";
    
    $stmt_cpf = $pdo_edu->prepare($query_cpf_teste);
    $stmt_cpf->execute();
    $colaborador_teste = $stmt_cpf->fetch();
    
    if ($colaborador_teste) {
        echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>🧪 Teste com Colaborador Real:</h3>";
        
        echo "<h4>Colaborador Selecionado:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>CPF</th>";
        echo "<th style='padding: 8px;'>Nome</th>";
        echo "<th style='padding: 8px;'>Andamento (exemplo)</th>";
        echo "</tr>";
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($colaborador_teste['cpf']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($colaborador_teste['usuario']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($colaborador_teste['andamento_etapa']) . "</td>";
        echo "</tr>";
        echo "</table>";
        
        // Buscar cursos deste colaborador
        $cursos_teste = buscarCursosColaboradorTeste($colaborador_teste['cpf'], $pdo_edu);
        
        echo "<h4>Cursos do Colaborador:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Curso</th>";
        echo "<th style='padding: 8px;'>Aprovação</th>";
        echo "<th style='padding: 8px;'>Andamento</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "</tr>";
        
        $em_andamento_count = 0;
        foreach ($cursos_teste as $curso) {
            $status = 'Outro';
            if (!empty($curso['andamento_etapa']) && $curso['aprovacao'] !== 'Sim') {
                $status = 'EM ANDAMENTO';
                $em_andamento_count++;
            } elseif ($curso['aprovacao'] === 'Sim') {
                $status = 'Aprovado';
            }
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($curso['recurso']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($curso['aprovacao'] ?? 'NULL') . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($curso['andamento_etapa'] ?? 'NULL') . "</td>";
            echo "<td style='padding: 8px; font-weight: bold; color: " . ($status === 'EM ANDAMENTO' ? '#17a2b8' : '#6c757d') . ";'>" . $status . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h4>Resultado do Teste:</h4>";
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>Cursos em Andamento encontrados:</strong> " . $em_andamento_count;
        echo "</div>";
        
        if ($em_andamento_count > 0) {
            echo "<p style='color: #28a745; font-weight: bold;'>✅ SUCESSO: Campo andamento_etapa está sendo lido corretamente!</p>";
        } else {
            echo "<p style='color: #dc3545; font-weight: bold;'>❌ PROBLEMA: Ainda não está funcionando corretamente.</p>";
        }
        
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>ℹ️ Nenhum colaborador com andamento_etapa encontrado para teste</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro no teste:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 4: Verificar estatísticas gerais
echo "<h2>4. 📈 Estatísticas Gerais</h2>";

try {
    // Contar total de registros com andamento_etapa
    $query_stats = "
        SELECT 
            COUNT(*) as total_registros,
            COUNT(CASE WHEN andamento_etapa IS NOT NULL AND andamento_etapa != '' THEN 1 END) as com_andamento,
            COUNT(CASE WHEN andamento_etapa IS NOT NULL AND andamento_etapa != '' AND aprovacao != 'Sim' THEN 1 END) as em_andamento_real,
            COUNT(DISTINCT CASE WHEN andamento_etapa IS NOT NULL AND andamento_etapa != '' AND aprovacao != 'Sim' THEN cpf END) as colaboradores_em_andamento
        FROM edu_relatorio_educacao
    ";
    
    $stmt_stats = $pdo_edu->prepare($query_stats);
    $stmt_stats->execute();
    $stats = $stmt_stats->fetch();
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>📊 Estatísticas do Banco:</h3>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Métrica</th>";
    echo "<th style='padding: 8px;'>Valor</th>";
    echo "<th style='padding: 8px;'>Descrição</th>";
    echo "</tr>";
    echo "<tr>";
    echo "<td style='padding: 8px;'>Total de Registros</td>";
    echo "<td style='padding: 8px;'>" . number_format($stats['total_registros']) . "</td>";
    echo "<td style='padding: 8px;'>Todos os registros na tabela</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td style='padding: 8px;'>Com Andamento</td>";
    echo "<td style='padding: 8px;'>" . number_format($stats['com_andamento']) . "</td>";
    echo "<td style='padding: 8px;'>Registros com andamento_etapa preenchido</td>";
    echo "</tr>";
    echo "<tr style='background: #d4edda;'>";
    echo "<td style='padding: 8px;'><strong>Em Andamento Real</strong></td>";
    echo "<td style='padding: 8px;'><strong>" . number_format($stats['em_andamento_real']) . "</strong></td>";
    echo "<td style='padding: 8px;'><strong>Com andamento E não aprovado</strong></td>";
    echo "</tr>";
    echo "<tr style='background: #d4edda;'>";
    echo "<td style='padding: 8px;'><strong>Colaboradores Únicos</strong></td>";
    echo "<td style='padding: 8px;'><strong>" . number_format($stats['colaboradores_em_andamento']) . "</strong></td>";
    echo "<td style='padding: 8px;'><strong>CPFs únicos em andamento</strong></td>";
    echo "</tr>";
    echo "</table>";
    
    echo "<h4>Expectativa:</h4>";
    echo "<ul>";
    echo "<li><strong>Card 'Cursos em Andamento':</strong> Deve mostrar " . number_format($stats['em_andamento_real']) . " (total de cursos)</li>";
    echo "<li><strong>Colaboradores com badge 'Em Andamento':</strong> Máximo de " . number_format($stats['colaboradores_em_andamento']) . " (CPFs únicos)</li>";
    echo "</ul>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro nas estatísticas:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 5: Como verificar a correção
echo "<h2>5. 🧪 Como Verificar a Correção</h2>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📋 Passos para Verificação:</h3>";

echo "<h4>Teste 1: Card Principal</h4>";
echo "<ol>";
echo "<li><strong>Acesse:</strong> analise_colaboradores.php</li>";
echo "<li><strong>Observe:</strong> Card 'Cursos em Andamento' no topo</li>";
echo "<li><strong>Verifique:</strong> Deve mostrar um número > 0</li>";
echo "<li><strong>Compare:</strong> Com as estatísticas acima</li>";
echo "</ol>";

echo "<h4>Teste 2: Consistência com Badges</h4>";
echo "<ol>";
echo "<li><strong>Conte:</strong> Colaboradores com badge 'Em Andamento'</li>";
echo "<li><strong>Compare:</strong> Com valor do card principal</li>";
echo "<li><strong>Lógica:</strong> Card conta cursos, badges contam colaboradores</li>";
echo "<li><strong>Relação:</strong> Card ≥ Badges (um colaborador pode ter múltiplos cursos)</li>";
echo "</ol>";

echo "<h4>Teste 3: Filtro por Status</h4>";
echo "<ol>";
echo "<li><strong>Aplique:</strong> Filtro Status = 'Em Andamento'</li>";
echo "<li><strong>Observe:</strong> Apenas colaboradores com esse status</li>";
echo "<li><strong>Verifique:</strong> Card principal atualiza</li>";
echo "<li><strong>Confirme:</strong> Números são consistentes</li>";
echo "</ol>";

echo "<h4>Teste 4: Debug Manual</h4>";
echo "<ol>";
echo "<li><strong>Escolha:</strong> Um colaborador com badge 'Em Andamento'</li>";
echo "<li><strong>Clique:</strong> 'Ver Detalhes'</li>";
echo "<li><strong>Observe:</strong> Lista de cursos no modal</li>";
echo "<li><strong>Identifique:</strong> Cursos com andamento_etapa preenchido</li>";
echo "</ol>";
echo "</div>";

// Resumo final
echo "<h2>6. 📋 Resumo da Correção</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Correção Crítica Implementada</h3>";

echo "<h4>✅ Problema Resolvido:</h4>";
echo "<ul>";
echo "<li><strong>Campo Adicionado:</strong> andamento_etapa incluído na consulta SQL</li>";
echo "<li><strong>Dados Disponíveis:</strong> \$curso['andamento_etapa'] agora contém valores</li>";
echo "<li><strong>Condição Funcional:</strong> !empty(\$curso['andamento_etapa']) funciona</li>";
echo "<li><strong>Contagem Correta:</strong> Cursos em andamento são identificados</li>";
echo "</ul>";

echo "<h4>✅ Impacto da Correção:</h4>";
echo "<ul>";
echo "<li><strong>Card Principal:</strong> 'Cursos em Andamento' mostra valor correto</li>";
echo "<li><strong>Consistência:</strong> Dados alinhados com badges dos colaboradores</li>";
echo "<li><strong>Filtros:</strong> Filtro por 'Em Andamento' funciona corretamente</li>";
echo "<li><strong>Confiabilidade:</strong> Métricas refletem realidade do banco</li>";
echo "</ul>";

echo "<h4>🚀 Benefícios:</h4>";
echo "<ul>";
echo "<li><strong>Precisão:</strong> Dados corretos e confiáveis</li>";
echo "<li><strong>Funcionalidade:</strong> Todas as features funcionando</li>";
echo "<li><strong>Usabilidade:</strong> Interface consistente e informativa</li>";
echo "<li><strong>Monitoramento:</strong> Acompanhamento real do progresso</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📊 Verificar Card</a>";
echo "<a href='analise_colaboradores.php?status_curso=em_andamento' style='background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🔄 Testar Filtro</a>";
echo "</p>";
?>
