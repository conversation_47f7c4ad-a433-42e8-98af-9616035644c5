-- Script de Otimização de Performance para Análise de Colaboradores
-- Este script adiciona índices compostos otimizados para melhorar significativamente a performance

-- Verificar se os índices já existem antes de criar
SET @sql = '';

-- Índice composto para consultas por CPF com filtros de status
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name = 'edu_relatorio_educacao' 
AND index_name = 'idx_cpf_aprovacao_data_conclusao';

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_cpf_aprovacao_data_conclusao ON edu_relatorio_educacao (cpf, aprovacao, data_conclusao)',
    'SELECT "Índice idx_cpf_aprovacao_data_conclusao já existe" as status'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Índice composto para consultas por trilha e recurso
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name = 'edu_relatorio_educacao' 
AND index_name = 'idx_trilha_recurso_cpf';

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_trilha_recurso_cpf ON edu_relatorio_educacao (codigo_trilha, codigo_recurso, cpf)',
    'SELECT "Índice idx_trilha_recurso_cpf já existe" as status'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Índice composto para consultas de prazos
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name = 'edu_relatorio_educacao' 
AND index_name = 'idx_prazo_status';

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_prazo_status ON edu_relatorio_educacao (concluir_trilha_ate, aprovacao, data_conclusao)',
    'SELECT "Índice idx_prazo_status já existe" as status'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Índice para consultas por função
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name = 'edu_relatorio_educacao' 
AND index_name = 'idx_funcao_cpf';

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_funcao_cpf ON edu_relatorio_educacao (funcao, cpf)',
    'SELECT "Índice idx_funcao_cpf já existe" as status'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Índice para consultas por andamento
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name = 'edu_relatorio_educacao' 
AND index_name = 'idx_andamento_aprovacao';

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_andamento_aprovacao ON edu_relatorio_educacao (andamento_etapa, aprovacao)',
    'SELECT "Índice idx_andamento_aprovacao já existe" as status'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Índice para consultas por data de admissão (prazos personalizados)
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name = 'edu_relatorio_educacao' 
AND index_name = 'idx_data_admissao_cpf';

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_data_admissao_cpf ON edu_relatorio_educacao (data_admissao, cpf)',
    'SELECT "Índice idx_data_admissao_cpf já existe" as status'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Índice composto para estatísticas gerais
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name = 'edu_relatorio_educacao' 
AND index_name = 'idx_stats_geral';

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_stats_geral ON edu_relatorio_educacao (trilha, recurso, aprovacao, aproveitamento)',
    'SELECT "Índice idx_stats_geral já existe" as status'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Índice para tabela de prazos personalizados
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name = 'edu_prazos_personalizados' 
AND index_name = 'idx_prazos_ativo';

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_prazos_ativo ON edu_prazos_personalizados (prazo_personalizado_ativo, codigo_trilha, codigo_recurso)',
    'SELECT "Índice idx_prazos_ativo já existe" as status'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Otimizar tabela após criação dos índices
OPTIMIZE TABLE edu_relatorio_educacao;

-- Atualizar estatísticas das tabelas
ANALYZE TABLE edu_relatorio_educacao;
ANALYZE TABLE edu_prazos_personalizados;

SELECT 'Otimização de performance concluída com sucesso!' as resultado;
