<?php
// Configuração específica do banco para Educação Corporativa
// Usa o mesmo banco sicoob_access_control do ecossistema

// Incluir configuração principal se não estiver carregada
if (!isset($pdo)) {
    require_once __DIR__ . '/../../../config/database.php';
}

try {
    // Verificar se a conexão principal existe
    if (!isset($pdo)) {
        // Criar conexão direta se necessário
        $host = 'dashboard.sicoobcredilivre.com.br';
        $dbname = 'sicoob_access_control';
        $username = 'sis';
        $password = 'Sicoob@123';

        $pdo_edu = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo_edu->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo_edu->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    } else {
        // Usar a mesma conexão do projeto principal
        $pdo_edu = $pdo;
    }

    // Garantir que a conexão está usando UTF-8
    $pdo_edu->exec("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo_edu->exec("SET CHARACTER SET utf8mb4");

    // Teste de conexão
    $pdo_edu->query("SELECT 1");

} catch (PDOException $e) {
    error_log('Erro de conexão com o banco de dados (Educação Corporativa): ' . $e->getMessage());
    die('Erro de conexão com o banco de dados. Por favor, verifique as configurações.');
}
?>
