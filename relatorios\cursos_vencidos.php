<?php
// Relatório de Cursos com Participantes Vencidos - VERSÃO OTIMIZADA
set_time_limit(300); // 5 minutos

// Função otimizada para calcular status de prazo (mesma lógica da análise de colaboradores)
function calcularStatusPrazoOtimizado($curso, $prazos_config) {
    $key = $curso['codigo_trilha'] . '|' . $curso['codigo_recurso'];

    // Se tem prazo personalizado e colaborador é elegível
    if (isset($prazos_config[$key]) && $curso['data_admissao'] > '2023-01-01') {
        $config = $prazos_config[$key];
        $primeiro_prazo = (int)$config['primeiro_prazo_dias'];
        $renovacao_prazo = !empty($config['renovacao_prazo_dias']) ? (int)$config['renovacao_prazo_dias'] : 0;

        if ($primeiro_prazo > 0) {
            // Lógica simplificada para prazos personalizados
            if (!empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00') {
                if ($renovacao_prazo <= 0) {
                    return 'concluido_sem_renovacao'; // Sem renovação
                }
                // Calcular prazo de renovação
                $data_conclusao = new DateTime($curso['data_conclusao']);
                $prazo_renovacao = clone $data_conclusao;
                $prazo_renovacao->add(new DateInterval('P' . $renovacao_prazo . 'D'));
                $hoje = new DateTime();

                if ($prazo_renovacao < $hoje) {
                    return 'vencido';
                } elseif ($prazo_renovacao <= (clone $hoje)->add(new DateInterval('P30D'))) {
                    return 'a_vencer';
                }
                return 'em_dia';
            } else {
                // Primeiro prazo
                $data_admissao = new DateTime($curso['data_admissao']);
                $primeiro_prazo_data = clone $data_admissao;
                $primeiro_prazo_data->add(new DateInterval('P' . $primeiro_prazo . 'D'));
                $hoje = new DateTime();

                if ($primeiro_prazo_data < $hoje) {
                    return 'vencido';
                } elseif ($primeiro_prazo_data <= (clone $hoje)->add(new DateInterval('P30D'))) {
                    return 'a_vencer';
                }
                return 'em_dia';
            }
        }
    }

    // Usar status básico já calculado na query
    return $curso['status_prazo_basico'] ?? 'em_dia';
}

// Função otimizada para buscar cursos de um colaborador (mesma lógica da análise de colaboradores)
function buscarCursosColaboradorOtimizado($cpf, $pdo, $prazos_config) {
    $query = "
        SELECT
            codigo_trilha,
            trilha,
            codigo_recurso,
            recurso,
            aprovacao,
            data_conclusao,
            concluir_trilha_ate,
            data_admissao,
            nota_recurso,
            aproveitamento,
            validade_recurso,
            andamento_etapa,
            carga_horaria_recurso,
            CASE
                WHEN concluir_trilha_ate < CURDATE() AND (aprovacao != 'Sim' OR aprovacao IS NULL) THEN 'vencido'
                WHEN concluir_trilha_ate >= CURDATE() AND concluir_trilha_ate <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND (aprovacao != 'Sim' OR aprovacao IS NULL) THEN 'a_vencer'
                ELSE 'em_dia'
            END as status_prazo_basico
        FROM edu_relatorio_educacao
        WHERE cpf = ?
        ORDER BY trilha, recurso
    ";

    $stmt = $pdo->prepare($query);
    $stmt->execute([$cpf]);
    $cursos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Calcular prazos de forma mais eficiente
    foreach ($cursos as &$curso) {
        $status_final = calcularStatusPrazoOtimizado($curso, $prazos_config);
        $curso['status_prazo'] = $status_final;

        // Calcular prazo calculado se necessário para compatibilidade
        $key = $curso['codigo_trilha'] . '|' . $curso['codigo_recurso'];
        if (isset($prazos_config[$key]) && $curso['data_admissao'] > '2023-01-01') {
            $config = $prazos_config[$key];
            $primeiro_prazo = (int)$config['primeiro_prazo_dias'];
            $renovacao_prazo = !empty($config['renovacao_prazo_dias']) ? (int)$config['renovacao_prazo_dias'] : 0;

            if (!empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00' && $renovacao_prazo > 0) {
                $data_conclusao = new DateTime($curso['data_conclusao']);
                $prazo_renovacao = clone $data_conclusao;
                $prazo_renovacao->add(new DateInterval('P' . $renovacao_prazo . 'D'));
                $curso['prazo_calculado'] = $prazo_renovacao->format('Y-m-d');
            } elseif (empty($curso['data_conclusao']) || $curso['data_conclusao'] === '0000-00-00') {
                $data_admissao = new DateTime($curso['data_admissao']);
                $primeiro_prazo_data = clone $data_admissao;
                $primeiro_prazo_data->add(new DateInterval('P' . $primeiro_prazo . 'D'));
                $curso['prazo_calculado'] = $primeiro_prazo_data->format('Y-m-d');
            } else {
                $curso['prazo_calculado'] = null;
            }
            $curso['prazo_personalizado'] = true;
        } else {
            $curso['prazo_calculado'] = $curso['concluir_trilha_ate'];
            $curso['prazo_personalizado'] = false;
        }

        // Calcular dias para o prazo
        if (!empty($curso['prazo_calculado'])) {
            $hoje = new DateTime();
            $prazo = new DateTime($curso['prazo_calculado']);
            $diff = $hoje->diff($prazo);
            $curso['dias_prazo'] = $prazo < $hoje ? -$diff->days : $diff->days;
        } else {
            $curso['dias_prazo'] = null;
        }
    }

    return $cursos;
}

echo '<table border="1" style="border-collapse: collapse;">';
echo '<tr style="background-color: #dc3545; color: white; font-weight: bold;">';
echo '<td colspan="12" style="padding: 10px; text-align: center; font-size: 16px;">';
echo 'RELATÓRIO DE CURSOS COM PARTICIPANTES VENCIDOS';
echo '</td>';
echo '</tr>';

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td style="padding: 8px;">Gerado em:</td>';
echo '<td colspan="11" style="padding: 8px;">' . date('d/m/Y H:i:s') . '</td>';
echo '</tr>';

// Mostrar filtros aplicados se houver
if (!empty($filtros['trilha']) || !empty($filtros['funcao']) || !empty($filtros['periodo']) || !empty($filtros['pa'])) {
    echo '<tr style="background-color: #e9ecef;">';
    echo '<td style="padding: 8px; font-weight: bold;">Filtros Aplicados:</td>';
    echo '<td colspan="11" style="padding: 8px;">';
    $filtros_texto = [];
    if (!empty($filtros['trilha'])) $filtros_texto[] = "Trilha: " . $filtros['trilha'];
    if (!empty($filtros['funcao'])) $filtros_texto[] = "Função: " . $filtros['funcao'];
    if (!empty($filtros['periodo'])) $filtros_texto[] = "Período: " . $filtros['periodo'];
    if (!empty($filtros['pa'])) {
        $agencia_nome = isset($mapa_agencias[$filtros['pa']]) ?
            $mapa_agencias[$filtros['pa']]['numero'] . ' - ' . $mapa_agencias[$filtros['pa']]['nome'] :
            $filtros['pa'];
        $filtros_texto[] = "PA: " . $agencia_nome;
    }
    echo implode(' | ', $filtros_texto);
    echo '</td>';
    echo '</tr>';
}

echo '<tr style="background-color: #fff3cd;">';
echo '<td colspan="12" style="padding: 8px; text-align: center; color: #856404;">';
echo '<strong>Nota:</strong> Relatório limitado a 500 participantes para otimização de performance.';
echo '</td>';
echo '</tr>';

// Buscar todos os colaboradores primeiro (similar ao relatório de colaboradores_vencidos)
$where_conditions = [];
$params = [];

if (!empty($filtros['trilha'])) {
    $where_conditions[] = "trilha LIKE ?";
    $params[] = '%' . $filtros['trilha'] . '%';
}

if (!empty($filtros['funcao'])) {
    $where_conditions[] = "funcao = ?";
    $params[] = $filtros['funcao'];
}

// Query para buscar todos os colaboradores
$colaboradores_query = "
    SELECT
        cpf,
        MAX(usuario) as usuario,
        MAX(email) as email,
        MAX(funcao) as funcao,
        MAX(codigo_unidade) as codigo_unidade,
        MAX(data_admissao) as data_admissao,
        MAX(superior_imediato) as superior_imediato
    FROM edu_relatorio_educacao
    " . (!empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "") . "
    GROUP BY cpf
    ORDER BY MAX(usuario)
";

$stmt_colaboradores = $pdo_edu->prepare($colaboradores_query);
$stmt_colaboradores->execute($params);
$todos_colaboradores = $stmt_colaboradores->fetchAll();

// Buscar participantes com cursos vencidos usando a mesma lógica da análise de colaboradores
$participantes_vencidos = [];

foreach ($todos_colaboradores as $colaborador) {
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;

    // Filtrar por PA se especificado
    if (!empty($filtros['pa'])) {
        if (!$usuario_intranet || $usuario_intranet['agencia'] != $filtros['pa']) {
            continue;
        }
    }

    // Buscar cursos do colaborador usando a mesma lógica da análise de colaboradores
    $cursos_colaborador = buscarCursosColaboradorOtimizado($colaborador['cpf'], $pdo_edu, $prazos_config);
    $cursos_vencidos = [];
    $tem_curso_no_periodo = true;

    // Verificar filtro de período se especificado
    if (!empty($filtros['periodo'])) {
        $tem_curso_no_periodo = false;
        foreach ($cursos_colaborador as $curso) {
            if (!empty($curso['prazo_calculado'])) {
                $prazo_curso = new DateTime($curso['prazo_calculado']);
                if (verificarCursoNoPeriodo($prazo_curso, $filtros['periodo'])) {
                    $tem_curso_no_periodo = true;
                    break;
                }
            }
        }
    }

    if (!$tem_curso_no_periodo) continue;

    // Buscar cursos vencidos
    foreach ($cursos_colaborador as $curso) {
        if ($curso['status_prazo'] === 'vencido') {
            // Adicionar dados do colaborador ao curso
            $curso['cpf'] = $colaborador['cpf'];
            $curso['usuario'] = $colaborador['usuario'];
            $participantes_vencidos[] = $curso;
        }
    }
}

// Aplicar limite de 500 participantes para performance
if (count($participantes_vencidos) > 500) {
    $participantes_vencidos = array_slice($participantes_vencidos, 0, 500);
}

// Agrupar por curso
$cursos_com_vencidos = [];
foreach ($participantes_vencidos as $curso) {
    $key = $curso['codigo_recurso'] . '|' . $curso['recurso'];

    if (!isset($cursos_com_vencidos[$key])) {
        $cursos_com_vencidos[$key] = [
            'codigo_recurso' => $curso['codigo_recurso'],
            'recurso' => $curso['recurso'],
            'trilha' => $curso['trilha'],
            'codigo_trilha' => $curso['codigo_trilha'],
            'carga_horaria_recurso' => $curso['carga_horaria_recurso'] ?? 'N/A',
            'participantes_vencidos' => [],
            'total_vencidos' => 0
        ];
    }

    // Calcular dias de atraso usando o prazo calculado (que já considera prazos personalizados)
    $hoje = new DateTime();
    if (!empty($curso['prazo_calculado'])) {
        $prazo = new DateTime($curso['prazo_calculado']);
        $diff = $hoje->diff($prazo);
        $curso['dias_atraso'] = $diff->days;
    } else {
        $curso['dias_atraso'] = 0;
    }

    $cursos_com_vencidos[$key]['participantes_vencidos'][] = $curso;
    $cursos_com_vencidos[$key]['total_vencidos']++;
}

// Converter para array indexado
$cursos_com_vencidos = array_values($cursos_com_vencidos);

// Mostrar resumo
echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Cursos com Participantes Vencidos:</td>';
echo '<td colspan="11" style="padding: 8px; color: #dc3545; font-weight: bold;">' . count($cursos_com_vencidos) . '</td>';
echo '</tr>';

// Contar participantes únicos (não repetir o mesmo CPF)
$participantes_unicos = [];
foreach ($participantes_vencidos as $curso) {
    $participantes_unicos[$curso['cpf']] = true;
}
$total_participantes_unicos = count($participantes_unicos);

echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Total de Participantes Vencidos:</td>';
echo '<td colspan="11" style="padding: 8px; color: #dc3545; font-weight: bold;">' . $total_participantes_unicos . '</td>';
echo '</tr>';

$total_cursos_vencidos = count($participantes_vencidos);
echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Total de Cursos Vencidos:</td>';
echo '<td colspan="11" style="padding: 8px; color: #dc3545; font-weight: bold;">' . $total_cursos_vencidos . '</td>';
echo '</tr>';

echo '<tr><td colspan="12" style="padding: 5px;"></td></tr>'; // Espaçamento

// Cabeçalhos
echo '<tr style="background-color: #dc3545; color: white; font-weight: bold;">';
echo '<td style="padding: 8px;">Trilha</td>';
echo '<td style="padding: 8px;">Curso</td>';
echo '<td style="padding: 8px;">Código</td>';
echo '<td style="padding: 8px;">Carga Horária</td>';
echo '<td style="padding: 8px;">Total Vencidos</td>';
echo '<td style="padding: 8px;">CPF Participante</td>';
echo '<td style="padding: 8px;">Nome</td>';
echo '<td style="padding: 8px;">Prazo Original</td>';
echo '<td style="padding: 8px;">Dias Atraso</td>';
echo '<td style="padding: 8px;">Andamento</td>';
echo '<td style="padding: 8px;">Status</td>';
echo '<td style="padding: 8px;">Prioridade</td>';
echo '</tr>';

// Dados
foreach ($cursos_com_vencidos as $curso_info) {
    foreach ($curso_info['participantes_vencidos'] as $participante) {
        echo '<tr>';

        // Repetir dados do curso em todas as linhas para compatibilidade com Excel
        echo '<td style="padding: 6px;">' . htmlspecialchars($curso_info['trilha']) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($curso_info['recurso']) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($curso_info['codigo_recurso']) . '</td>';
        echo '<td style="padding: 6px; text-align: center;">' . $curso_info['carga_horaria_recurso'] . '</td>';
        echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: #dc3545;">' . $curso_info['total_vencidos'] . '</td>';

        echo '<td style="padding: 6px;">' . formatarCpf($participante['cpf']) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($participante['usuario']) . '</td>';
        echo '<td style="padding: 6px; text-align: center;">' .
             ($participante['prazo_calculado'] ? date('d/m/Y', strtotime($participante['prazo_calculado'])) : 'N/A') . '</td>';
        echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: #dc3545;">' . $participante['dias_atraso'] . ' dias</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($participante['andamento_etapa'] ?? 'Não iniciado') . '</td>';
        echo '<td style="padding: 6px; color: #dc3545;">Vencido</td>';

        $prioridade = $participante['dias_atraso'] > 60 ? 'CRÍTICA' : ($participante['dias_atraso'] > 30 ? 'ALTA' : 'Média');
        $cor_prioridade = $participante['dias_atraso'] > 60 ? '#6f42c1' : ($participante['dias_atraso'] > 30 ? '#dc3545' : '#fd7e14');
        echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: ' . $cor_prioridade . ';">' . $prioridade . '</td>';
        echo '</tr>';
    }
}

echo '</table>';
?>
