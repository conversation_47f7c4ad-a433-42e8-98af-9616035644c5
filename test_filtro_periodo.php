<?php
/**
 * Teste do Filtro por Período
 * 
 * Este arquivo demonstra como o novo filtro por período funciona
 * para encontrar colaboradores com cursos em períodos específicos.
 */

require_once 'config/database.php';

// Função de teste para verificar se um curso está no período especificado
function verificarCursoNoPeriodo($prazo_curso, $periodo) {
    $hoje = new DateTime();
    
    switch ($periodo) {
        case 'mes_atual':
            // Cursos que vencem no mês atual
            return $prazo_curso->format('Y-m') === $hoje->format('Y-m');
            
        case 'proximo_mes':
            // Cursos que vencem no próximo mês
            $proximo_mes = (clone $hoje)->modify('+1 month');
            return $prazo_curso->format('Y-m') === $proximo_mes->format('Y-m');
            
        case 'proximos_30_dias':
            // Cursos que vencem nos próximos 30 dias
            $limite = (clone $hoje)->modify('+30 days');
            return $prazo_curso >= $hoje && $prazo_curso <= $limite;
            
        case 'proximos_60_dias':
            // Cursos que vencem nos próximos 60 dias
            $limite = (clone $hoje)->modify('+60 days');
            return $prazo_curso >= $hoje && $prazo_curso <= $limite;
            
        case 'vencidos_30_dias':
            // Cursos vencidos nos últimos 30 dias
            $limite = (clone $hoje)->modify('-30 days');
            return $prazo_curso < $hoje && $prazo_curso >= $limite;
            
        default:
            return true;
    }
}

echo "<h2>🧪 Teste do Filtro por Período</h2>";
echo "<p>Demonstração de como o filtro por período identifica cursos em diferentes intervalos de tempo.</p>";

// Datas de teste
$hoje = new DateTime();
$datas_teste = [
    'hoje' => clone $hoje,
    'em_15_dias' => (clone $hoje)->modify('+15 days'),
    'em_45_dias' => (clone $hoje)->modify('+45 days'),
    'proximo_mes' => (clone $hoje)->modify('+1 month'),
    'vencido_10_dias' => (clone $hoje)->modify('-10 days'),
    'vencido_45_dias' => (clone $hoje)->modify('-45 days'),
];

$periodos_teste = [
    'mes_atual' => 'Mês Atual',
    'proximo_mes' => 'Próximo Mês', 
    'proximos_30_dias' => 'Próximos 30 dias',
    'proximos_60_dias' => 'Próximos 60 dias',
    'vencidos_30_dias' => 'Vencidos nos últimos 30 dias'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
echo "<thead style='background: #f8f9fa;'>";
echo "<tr>";
echo "<th style='padding: 10px;'>Data do Curso</th>";
foreach ($periodos_teste as $periodo_key => $periodo_nome) {
    echo "<th style='padding: 10px;'>$periodo_nome</th>";
}
echo "</tr>";
echo "</thead>";
echo "<tbody>";

foreach ($datas_teste as $data_key => $data_obj) {
    echo "<tr>";
    echo "<td style='padding: 8px;'><strong>" . ucfirst(str_replace('_', ' ', $data_key)) . "</strong><br>";
    echo "<small>" . $data_obj->format('d/m/Y') . "</small></td>";
    
    foreach ($periodos_teste as $periodo_key => $periodo_nome) {
        $resultado = verificarCursoNoPeriodo($data_obj, $periodo_key);
        $cor = $resultado ? '#d4edda' : '#f8d7da';
        $icone = $resultado ? '✅' : '❌';
        echo "<td style='padding: 8px; background: $cor; text-align: center;'>$icone</td>";
    }
    echo "</tr>";
}

echo "</tbody>";
echo "</table>";

echo "<h3>📋 Como usar o filtro:</h3>";
echo "<ol>";
echo "<li><strong>Período apenas:</strong> Selecione um período para ver todos os colaboradores que têm cursos com prazos nesse período.</li>";
echo "<li><strong>Período + Status:</strong> Combine período com status para filtros mais específicos:</li>";
echo "<ul>";
echo "<li>Período: 'Próximos 30 dias' + Status: 'A Vencer' = Colaboradores com cursos que vencerão em breve</li>";
echo "<li>Período: 'Vencidos nos últimos 30 dias' + Status: 'Vencido' = Colaboradores com cursos recém-vencidos</li>";
echo "<li>Período: 'Mês Atual' + Status: 'Em Andamento' = Colaboradores com cursos em andamento que vencem este mês</li>";
echo "</ul>";
echo "</ol>";

echo "<h3>🎯 Exemplos práticos:</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h4>Cenário 1: Identificar urgências</h4>";
echo "<p><strong>Filtro:</strong> Período = 'Próximos 30 dias' + Status = 'A Vencer'</p>";
echo "<p><strong>Resultado:</strong> Lista colaboradores que precisam concluir cursos urgentemente.</p>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h4>Cenário 2: Acompanhar vencimentos recentes</h4>";
echo "<p><strong>Filtro:</strong> Período = 'Vencidos nos últimos 30 dias' + Status = 'Vencido'</p>";
echo "<p><strong>Resultado:</strong> Lista colaboradores que perderam prazos recentemente e precisam de atenção.</p>";
echo "</div>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h4>Cenário 3: Planejamento mensal</h4>";
echo "<p><strong>Filtro:</strong> Período = 'Próximo Mês'</p>";
echo "<p><strong>Resultado:</strong> Lista todos os colaboradores que têm cursos com prazo no próximo mês para planejamento.</p>";
echo "</div>";

echo "<p style='margin-top: 20px;'><strong>✨ Implementação concluída!</strong> O filtro por período está agora disponível na página de análise de colaboradores.</p>";
?>
