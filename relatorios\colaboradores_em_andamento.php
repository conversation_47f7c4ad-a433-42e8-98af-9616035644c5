<?php
// Relatório de Colaboradores com Cursos Em Andamento
// Este arquivo gera um relatório específico de colaboradores que possuem cursos em andamento

// Construir query base
$where_conditions = [];
$params = [];

if (!empty($filtros['trilha'])) {
    $where_conditions[] = "trilha LIKE ?";
    $params[] = '%' . $filtros['trilha'] . '%';
}

if (!empty($filtros['funcao'])) {
    $where_conditions[] = "funcao = ?";
    $params[] = $filtros['funcao'];
}

// Query para buscar todos os colaboradores
$colaboradores_query = "
    SELECT
        cpf,
        MAX(usuario) as usuario,
        MAX(email) as email,
        MAX(funcao) as funcao,
        MAX(codigo_unidade) as codigo_unidade,
        MAX(data_admissao) as data_admissao,
        MAX(superior_imediato) as superior_imediato,
        COUNT(DISTINCT trilha) as total_trilhas,
        COUNT(DISTINCT CONCAT(codigo_trilha, '|', codigo_recurso)) as total_cursos,
        MAX(data_importacao) as ultima_atualizacao
    FROM edu_relatorio_educacao
    " . (!empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "") . "
    GROUP BY cpf
    ORDER BY MAX(usuario)
";

$stmt_colaboradores = $pdo_edu->prepare($colaboradores_query);
$stmt_colaboradores->execute($params);
$todos_colaboradores = $stmt_colaboradores->fetchAll();

// Filtrar colaboradores que possuem cursos em andamento
$colaboradores_com_andamento = [];

foreach ($todos_colaboradores as $colaborador) {
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;

    // Filtrar por PA se especificado
    if (!empty($filtros['pa'])) {
        if (!$usuario_intranet || $usuario_intranet['agencia'] != $filtros['pa']) {
            continue;
        }
    }

    $cursos_colaborador = buscarCursosColaborador($colaborador['cpf'], $pdo_edu, $prazos_config);
    $cursos_em_andamento = [];
    $tem_curso_no_periodo = true;

    // Verificar filtro de período se especificado
    if (!empty($filtros['periodo'])) {
        $tem_curso_no_periodo = false;
        foreach ($cursos_colaborador as $curso) {
            if (!empty($curso['prazo_calculado'])) {
                $prazo_curso = new DateTime($curso['prazo_calculado']);
                if (verificarCursoNoPeriodo($prazo_curso, $filtros['periodo'])) {
                    $tem_curso_no_periodo = true;
                    break;
                }
            }
        }
    }

    if (!$tem_curso_no_periodo) continue;

    // Buscar cursos em andamento
    foreach ($cursos_colaborador as $curso) {
        if (!empty($curso['andamento_etapa']) && $curso['aprovacao'] !== 'Sim') {
            $cursos_em_andamento[] = $curso;
        }
    }

    if (!empty($cursos_em_andamento)) {
        $colaborador['cursos_em_andamento'] = $cursos_em_andamento;
        $colaborador['total_em_andamento'] = count($cursos_em_andamento);
        $colaboradores_com_andamento[] = $colaborador;
    }
}

// Ordenar por quantidade de cursos em andamento (maior primeiro)
usort($colaboradores_com_andamento, function($a, $b) {
    return $b['total_em_andamento'] - $a['total_em_andamento'];
});

// Cabeçalho do relatório
echo '<table border="1" style="border-collapse: collapse;">';
echo '<tr style="background-color: #007bff; color: white; font-weight: bold;">';
echo '<td colspan="15" style="padding: 10px; text-align: center; font-size: 16px;">';
echo 'RELATÓRIO DE COLABORADORES COM CURSOS EM ANDAMENTO';
echo '</td>';
echo '</tr>';

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td style="padding: 8px;">Gerado em:</td>';
echo '<td colspan="14" style="padding: 8px;">' . date('d/m/Y H:i:s') . '</td>';
echo '</tr>';

if (!empty($filtros['trilha']) || !empty($filtros['funcao']) || !empty($filtros['periodo']) || !empty($filtros['pa'])) {
    echo '<tr style="background-color: #e9ecef;">';
    echo '<td style="padding: 8px; font-weight: bold;">Filtros Aplicados:</td>';
    echo '<td colspan="14" style="padding: 8px;">';
    $filtros_texto = [];
    if (!empty($filtros['trilha'])) $filtros_texto[] = "Trilha: " . $filtros['trilha'];
    if (!empty($filtros['funcao'])) $filtros_texto[] = "Função: " . $filtros['funcao'];
    if (!empty($filtros['periodo'])) $filtros_texto[] = "Período: " . $filtros['periodo'];
    if (!empty($filtros['pa'])) {
        $agencia_nome = isset($mapa_agencias[$filtros['pa']]) ?
            $mapa_agencias[$filtros['pa']]['numero'] . ' - ' . $mapa_agencias[$filtros['pa']]['nome'] :
            $filtros['pa'];
        $filtros_texto[] = "PA: " . $agencia_nome;
    }
    echo implode(' | ', $filtros_texto);
    echo '</td>';
    echo '</tr>';
}

echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Colaboradores com Cursos Em Andamento:</td>';
echo '<td colspan="14" style="padding: 8px; color: #007bff; font-weight: bold;">' . count($colaboradores_com_andamento) . '</td>';
echo '</tr>';

$total_cursos_andamento = array_sum(array_column($colaboradores_com_andamento, 'total_em_andamento'));
echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Total de Cursos Em Andamento:</td>';
echo '<td colspan="14" style="padding: 8px; color: #007bff; font-weight: bold;">' . $total_cursos_andamento . '</td>';
echo '</tr>';

echo '<tr><td colspan="15" style="padding: 5px;"></td></tr>'; // Espaçamento

// Cabeçalhos das colunas
echo '<tr style="background-color: #007bff; color: white; font-weight: bold;">';
echo '<td style="padding: 8px; text-align: center;">CPF</td>';
echo '<td style="padding: 8px; text-align: center;">Nome</td>';
echo '<td style="padding: 8px; text-align: center;">E-mail</td>';
echo '<td style="padding: 8px; text-align: center;">Função</td>';
echo '<td style="padding: 8px; text-align: center;">PA/Agência</td>';
echo '<td style="padding: 8px; text-align: center;">Setor</td>';
echo '<td style="padding: 8px; text-align: center;">Superior Imediato</td>';
echo '<td style="padding: 8px; text-align: center;">Qtd Em Andamento</td>';
echo '<td style="padding: 8px; text-align: center;">Trilha</td>';
echo '<td style="padding: 8px; text-align: center;">Curso Em Andamento</td>';
echo '<td style="padding: 8px; text-align: center;">Etapa Atual</td>';
echo '<td style="padding: 8px; text-align: center;">Prazo</td>';
echo '<td style="padding: 8px; text-align: center;">Dias Restantes</td>';
echo '<td style="padding: 8px; text-align: center;">Carga Horária</td>';
echo '<td style="padding: 8px; text-align: center;">Progresso</td>';
echo '</tr>';

// Dados dos colaboradores com cursos em andamento
$linha = 0;
foreach ($colaboradores_com_andamento as $colaborador) {
    $linha++;
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;

    // Informações da intranet
    $nome_exibir = $usuario_intranet['nome'] ?? $colaborador['usuario'];
    $email_exibir = $usuario_intranet['email'] ?? $colaborador['email'] ?? 'N/A';
    $funcao_exibir = $usuario_intranet['nomeFuncao'] ?? $colaborador['funcao'] ?? 'N/A';
    $setor_exibir = $usuario_intranet['nomeSetor'] ?? 'N/A';

    // Informações da agência
    $agencia_info = 'N/A';
    if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
        $agencia_id = $usuario_intranet['agencia'];
        if (isset($mapa_agencias[$agencia_id])) {
            $agencia_data = $mapa_agencias[$agencia_id];
            $agencia_info = $agencia_data['numero'] . ' - ' . $agencia_data['nome'];
        } else {
            $agencia_info = $agencia_id;
        }
    }

    // Ordenar cursos em andamento por prazo (mais urgente primeiro)
    $cursos_em_andamento = $colaborador['cursos_em_andamento'];
    usort($cursos_em_andamento, function($a, $b) {
        if ($a['dias_prazo'] === null && $b['dias_prazo'] === null) return 0;
        if ($a['dias_prazo'] === null) return 1;
        if ($b['dias_prazo'] === null) return -1;
        return $a['dias_prazo'] - $b['dias_prazo'];
    });

    foreach ($cursos_em_andamento as $curso) {
        $cor_linha = ($linha % 2 == 0) ? '#e3f2fd' : '#ffffff';
        echo '<tr style="background-color: ' . $cor_linha . ';">';

        // Repetir dados do colaborador em todas as linhas para compatibilidade com Excel
        echo '<td style="padding: 6px;">' . formatarCpf($colaborador['cpf']) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($nome_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($email_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($funcao_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($agencia_info) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($setor_exibir) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($colaborador['superior_imediato'] ?? 'N/A') . '</td>';
        echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: #007bff;">' . $colaborador['total_em_andamento'] . '</td>';

        // Dados do curso em andamento
        echo '<td style="padding: 6px;">' . htmlspecialchars($curso['trilha']) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($curso['recurso']) . '</td>';
        echo '<td style="padding: 6px; font-weight: bold; color: #007bff;">' . htmlspecialchars($curso['andamento_etapa']) . '</td>';

        // Prazo
        echo '<td style="padding: 6px; text-align: center;">' .
             ($curso['prazo_calculado'] ? date('d/m/Y', strtotime($curso['prazo_calculado'])) : 'N/A') . '</td>';

        // Dias restantes
        $dias_texto = 'N/A';
        $cor_dias = '#6c757d';
        if ($curso['dias_prazo'] !== null) {
            if ($curso['dias_prazo'] > 0) {
                $dias_texto = $curso['dias_prazo'] . ' dias';
                if ($curso['dias_prazo'] <= 7) {
                    $cor_dias = '#dc3545'; // Vermelho - urgente
                } elseif ($curso['dias_prazo'] <= 15) {
                    $cor_dias = '#fd7e14'; // Laranja - atenção
                } elseif ($curso['dias_prazo'] <= 30) {
                    $cor_dias = '#ffc107'; // Amarelo - monitorar
                } else {
                    $cor_dias = '#28a745'; // Verde - tranquilo
                }
            } else {
                $dias_texto = abs($curso['dias_prazo']) . ' dias atraso';
                $cor_dias = '#dc3545'; // Vermelho - atrasado
            }
        }

        echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: ' . $cor_dias . ';">' . $dias_texto . '</td>';

        // Carga horária
        echo '<td style="padding: 6px; text-align: center;">' .
             ($curso['carga_horaria_recurso'] ?? 'N/A') . '</td>';

        // Progresso (baseado na etapa atual - simulado)
        $progresso = 'Iniciado';
        $cor_progresso = '#007bff';

        $etapa_lower = strtolower($curso['andamento_etapa']);
        if (strpos($etapa_lower, 'conclu') !== false || strpos($etapa_lower, 'final') !== false) {
            $progresso = 'Finalizando';
            $cor_progresso = '#28a745';
        } elseif (strpos($etapa_lower, 'meio') !== false || strpos($etapa_lower, 'metade') !== false) {
            $progresso = 'Meio do Curso';
            $cor_progresso = '#ffc107';
        } elseif (strpos($etapa_lower, 'inic') !== false || strpos($etapa_lower, 'come') !== false) {
            $progresso = 'Iniciando';
            $cor_progresso = '#17a2b8';
        }

        echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: ' . $cor_progresso . ';">' . $progresso . '</td>';
        echo '</tr>';
    }
}

// Resumo por situação
echo '<tr><td colspan="15" style="padding: 10px;"></td></tr>'; // Espaçamento

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td colspan="15" style="padding: 10px; text-align: center; font-size: 14px;">RESUMO POR SITUAÇÃO DOS PRAZOS</td>';
echo '</tr>';

// Calcular estatísticas de situação
$em_dia = 0; $atencao = 0; $urgente = 0; $atrasado = 0;
foreach ($colaboradores_com_andamento as $colaborador) {
    foreach ($colaborador['cursos_em_andamento'] as $curso) {
        if ($curso['dias_prazo'] === null) {
            $em_dia++;
        } elseif ($curso['dias_prazo'] < 0) {
            $atrasado++;
        } elseif ($curso['dias_prazo'] <= 7) {
            $urgente++;
        } elseif ($curso['dias_prazo'] <= 30) {
            $atencao++;
        } else {
            $em_dia++;
        }
    }
}

echo '<tr>';
echo '<td colspan="3" style="padding: 8px; background-color: #28a745; color: white; text-align: center; font-weight: bold;">EM DIA (>30 dias)</td>';
echo '<td colspan="3" style="padding: 8px; background-color: #ffc107; color: black; text-align: center; font-weight: bold;">ATENÇÃO (8-30 dias)</td>';
echo '<td colspan="3" style="padding: 8px; background-color: #fd7e14; color: white; text-align: center; font-weight: bold;">URGENTE (≤7 dias)</td>';
echo '<td colspan="3" style="padding: 8px; background-color: #dc3545; color: white; text-align: center; font-weight: bold;">ATRASADO</td>';
echo '<td colspan="3" style="padding: 8px; background-color: #6c757d; color: white; text-align: center; font-weight: bold;">TOTAL</td>';
echo '</tr>';

echo '<tr>';
echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #28a745;">' . $em_dia . '</td>';
echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #ffc107;">' . $atencao . '</td>';
echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #fd7e14;">' . $urgente . '</td>';
echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #dc3545;">' . $atrasado . '</td>';
echo '<td colspan="3" style="padding: 8px; text-align: center; font-size: 18px; font-weight: bold; color: #6c757d;">' . $total_cursos_andamento . '</td>';
echo '</tr>';

echo '</table>';
?>
