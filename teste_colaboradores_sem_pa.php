<?php
/**
 * Teste para verificar se colaboradores não encontrados na Intranet 
 * aparecem como "Sem PA Definido"
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/IntranetAPI.php';

// Criar instância da API
$api = new IntranetAPI();

echo "<h1>🔍 Teste: Colaboradores Sem PA Definido</h1>\n";

try {
    // Buscar usuários da API (ativos e todos)
    $usuarios_intranet_ativos = $api->listarUsuariosAtivos(false);
    $usuarios_intranet_todos = $api->listarUsuarios(false, false);

    if ($usuarios_intranet_ativos !== false && $usuarios_intranet_todos !== false) {
        // Criar mapas de CPFs
        $mapa_usuarios_ativos = [];
        $mapa_usuarios_todos = [];
        $cpfs_ativos = [];

        foreach ($usuarios_intranet_ativos as $usuario) {
            if (!empty($usuario['cpf'])) {
                $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                $mapa_usuarios_ativos[$cpf_normalizado] = $usuario;
                $cpfs_ativos[] = $cpf_normalizado;
            }
        }

        foreach ($usuarios_intranet_todos as $usuario) {
            if (!empty($usuario['cpf'])) {
                $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                $mapa_usuarios_todos[$cpf_normalizado] = $usuario;
            }
        }
        
        echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>✅ API da Intranet:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Total de usuários:</strong> " . count($usuarios_intranet_todos) . "</li>";
        echo "<li><strong>Usuários ativos:</strong> " . count($usuarios_intranet_ativos) . "</li>";
        echo "<li><strong>Usuários inativos:</strong> " . (count($usuarios_intranet_todos) - count($usuarios_intranet_ativos)) . "</li>";
        echo "<li><strong>CPFs ativos processados:</strong> " . count($cpfs_ativos) . "</li>";
        echo "</ul>";
        echo "</div>";
        
        // Buscar TODOS os colaboradores da base
        $colaboradores_query = "
            SELECT
                cpf,
                MAX(usuario) as usuario,
                MAX(email) as email,
                COUNT(DISTINCT trilha) as total_trilhas,
                COUNT(DISTINCT CONCAT(codigo_trilha, '|', codigo_recurso)) as total_cursos
            FROM edu_relatorio_educacao
            GROUP BY cpf
            ORDER BY MAX(usuario)
            LIMIT 20
        ";
        
        $stmt_colaboradores = $pdo_edu->prepare($colaboradores_query);
        $stmt_colaboradores->execute();
        $todos_colaboradores = $stmt_colaboradores->fetchAll();
        
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>📊 Análise dos Colaboradores:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Total na base:</strong> " . count($todos_colaboradores) . " (primeiros 20)</li>";
        echo "</ul>";
        echo "</div>";
        
        // Analisar cada colaborador
        $colaboradores_ativos = 0;
        $colaboradores_nao_encontrados = 0;
        $colaboradores_sem_pa = 0;
        
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>📋 Análise Detalhada (primeiros 20 colaboradores):</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='padding: 5px;'>CPF</th>";
        echo "<th style='padding: 5px;'>Nome</th>";
        echo "<th style='padding: 5px;'>Status Intranet</th>";
        echo "<th style='padding: 5px;'>PA/Agência</th>";
        echo "<th style='padding: 5px;'>Classificação</th>";
        echo "</tr>";
        
        foreach ($todos_colaboradores as $colaborador) {
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
            $usuario_intranet_ativo = $mapa_usuarios_ativos[$cpf_normalizado] ?? null;
            $usuario_intranet_todos = $mapa_usuarios_todos[$cpf_normalizado] ?? null;

            $status_intranet = 'Não encontrado';
            $status_cor = '#dc3545';
            $pa_info = 'Sem PA Definido';
            $classificacao = '';
            $deve_exibir = true;

            if ($usuario_intranet_todos && !$usuario_intranet_ativo) {
                // Existe na Intranet mas está INATIVO - NÃO deve ser exibido
                $status_intranet = 'Inativo (filtrado)';
                $status_cor = '#6c757d';
                $classificacao = 'Inativo - NÃO EXIBIR';
                $deve_exibir = false;
            } elseif ($usuario_intranet_ativo) {
                $colaboradores_ativos++;
                $status_intranet = 'Ativo';
                $status_cor = '#28a745';

                if (!empty($usuario_intranet_ativo['agencia'])) {
                    $pa_info = 'PA ' . $usuario_intranet_ativo['agencia'];
                    $classificacao = 'Ativo com PA';
                } else {
                    $pa_info = 'Sem PA Definido';
                    $colaboradores_sem_pa++;
                    $classificacao = 'Ativo sem PA';
                }

                if (isset($usuario_intranet_ativo['status_bloqueio'])) {
                    $status_intranet .= ' - ' . $usuario_intranet_ativo['status_bloqueio']['texto'];
                }
            } else {
                // Não encontrado na Intranet - deve ser exibido como "Sem PA"
                $colaboradores_nao_encontrados++;
                $colaboradores_sem_pa++;
                $classificacao = 'Não encontrado (Sem PA)';
            }
            
            $row_style = $deve_exibir ? '' : 'background-color: #f8f9fa; opacity: 0.6;';

            echo "<tr style='$row_style'>";
            echo "<td style='padding: 5px;'>" . substr($colaborador['cpf'], 0, 3) . "***</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($colaborador['usuario'], 0, 25)) . "</td>";
            echo "<td style='padding: 5px; color: $status_cor; font-weight: bold;'>$status_intranet</td>";
            echo "<td style='padding: 5px;'>$pa_info</td>";
            echo "<td style='padding: 5px;'><strong>$classificacao</strong> " . ($deve_exibir ? '✅' : '❌') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
        // Resumo da análise
        echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>📊 Resumo da Análise:</h3>";
        echo "<ul>";
        echo "<li><strong>Colaboradores ativos na Intranet:</strong> $colaboradores_ativos</li>";
        echo "<li><strong>Colaboradores não encontrados na Intranet:</strong> $colaboradores_nao_encontrados</li>";
        echo "<li><strong>Total que aparecerão como 'Sem PA':</strong> $colaboradores_sem_pa</li>";
        echo "</ul>";
        echo "</div>";
        
        // Verificar contagem total
        $count_total_query = "SELECT COUNT(DISTINCT cpf) as total FROM edu_relatorio_educacao";
        $stmt_count = $pdo_edu->prepare($count_total_query);
        $stmt_count->execute();
        $total_base = $stmt_count->fetch()['total'];
        
        // Calcular quantos devem aparecer (excluindo inativos)
        $colaboradores_que_devem_aparecer = $total_base;
        foreach ($todos_colaboradores as $colaborador) {
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
            $usuario_intranet_ativo = $mapa_usuarios_ativos[$cpf_normalizado] ?? null;
            $usuario_intranet_todos = $mapa_usuarios_todos[$cpf_normalizado] ?? null;

            // Se existe na Intranet mas está inativo, não deve aparecer
            if ($usuario_intranet_todos && !$usuario_intranet_ativo) {
                $colaboradores_que_devem_aparecer--;
            }
        }

        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>🎯 Resultado Esperado na Página:</h3>";
        echo "<ul>";
        echo "<li><strong>Total de colaboradores na base:</strong> $total_base</li>";
        echo "<li><strong>Usuários ativos na Intranet:</strong> " . count($usuarios_intranet_ativos) . "</li>";
        echo "<li><strong>Usuários inativos na Intranet:</strong> " . (count($usuarios_intranet_todos) - count($usuarios_intranet_ativos)) . "</li>";
        echo "<li><strong>Colaboradores que aparecerão:</strong> $colaboradores_que_devem_aparecer (base - inativos)</li>";
        echo "<li><strong>Distribuição esperada:</strong></li>";
        echo "<ul>";
        echo "<li>Com PA definido: Usuários ativos com agência</li>";
        echo "<li>Sem PA definido: Usuários ativos sem agência + Não encontrados na Intranet</li>";
        echo "<li>Filtrados (não aparecem): Usuários inativos na Intranet</li>";
        echo "</ul>";
        echo "</ul>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>❌ <strong>Erro ao buscar usuários da API da Intranet</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Instruções
echo "<h2>📋 Como Interpretar os Resultados</h2>\n";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Comportamento Esperado:</h3>";
echo "<ol>";
echo "<li><strong>Colaboradores Ativos com PA:</strong> Aparecem no PA correspondente</li>";
echo "<li><strong>Colaboradores Ativos sem PA:</strong> Aparecem em 'Sem PA Definido'</li>";
echo "<li><strong>Colaboradores Não Encontrados:</strong> Aparecem em 'Sem PA Definido' com badge 'Não encontrado na Intranet'</li>";
echo "</ol>";

echo "<h3>✅ Verificações:</h3>";
echo "<ul>";
echo "<li><strong>Total de colaboradores:</strong> Deve mostrar todos da base (não apenas ativos)</li>";
echo "<li><strong>Status nos cards:</strong> Ativos mostram status de bloqueio, não encontrados mostram badge cinza</li>";
echo "<li><strong>Agrupamento:</strong> Todos devem aparecer em algum PA ou 'Sem PA Definido'</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔗 Links para Teste:</h3>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>👥 Testar Página Principal</a>";
echo "<a href='test_usuarios_ativos.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🧪 Teste da API</a>";
echo "</p>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
?>
