<?php
require_once 'config/config.php';
require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'classes/IntranetAPI.php';

// Verificar se o usuário é administrador para acessar esta página de teste
checkEduPageAccess('admin');

$message = '';
$message_type = '';
$test_results = [];

// Processar testes
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $api = new IntranetAPI();
    
    if (isset($_POST['test_usuarios'])) {
        $start_time = microtime(true);
        $usuarios = $api->listarUsuarios(false); // Sem cache para teste
        $end_time = microtime(true);
        
        $test_results['usuarios'] = [
            'success' => $usuarios !== false,
            'data' => $usuarios,
            'count' => is_array($usuarios) ? count($usuarios) : 0,
            'time' => round(($end_time - $start_time) * 1000, 2) . 'ms'
        ];
    }
    
    if (isset($_POST['test_agencias'])) {
        $start_time = microtime(true);
        $agencias = $api->listarAgencias(false); // Sem cache para teste
        $end_time = microtime(true);
        
        $test_results['agencias'] = [
            'success' => $agencias !== false,
            'data' => $agencias,
            'count' => is_array($agencias) ? count($agencias) : 0,
            'time' => round(($end_time - $start_time) * 1000, 2) . 'ms'
        ];
    }
    
    if (isset($_POST['test_cache'])) {
        $api = new IntranetAPI();
        
        // Teste com cache
        $start_time = microtime(true);
        $usuarios_cache = $api->listarUsuarios(true);
        $end_time = microtime(true);
        
        $test_results['cache'] = [
            'success' => $usuarios_cache !== false,
            'count' => is_array($usuarios_cache) ? count($usuarios_cache) : 0,
            'time' => round(($end_time - $start_time) * 1000, 2) . 'ms'
        ];
    }
    
    if (isset($_POST['clear_cache'])) {
        $api = new IntranetAPI();
        $cleared = $api->limparCache();
        
        $message = $cleared ? 'Cache limpo com sucesso!' : 'Erro ao limpar cache.';
        $message_type = $cleared ? 'success' : 'danger';
    }
    
    if (isset($_POST['test_email'])) {
        $email = trim($_POST['email']);
        if (!empty($email)) {
            $api = new IntranetAPI();
            $start_time = microtime(true);
            $usuario = $api->buscarUsuarioPorEmail($email);
            $end_time = microtime(true);
            
            $test_results['email'] = [
                'success' => $usuario !== false,
                'data' => $usuario,
                'email' => $email,
                'time' => round(($end_time - $start_time) * 1000, 2) . 'ms'
            ];
        }
    }
    
    if (isset($_POST['test_agencia_numero'])) {
        $numero = trim($_POST['numero_agencia']);
        if (!empty($numero)) {
            $api = new IntranetAPI();
            $start_time = microtime(true);
            $agencia = $api->buscarAgenciaPorNumero($numero);
            $end_time = microtime(true);
            
            $test_results['agencia_numero'] = [
                'success' => $agencia !== false,
                'data' => $agencia,
                'numero' => $numero,
                'time' => round(($end_time - $start_time) * 1000, 2) . 'ms'
            ];
        }
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste API Intranet - <?php echo EDU_PROJECT_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #00AE9D;
            --sicoob-verde-claro: #C9D200;
            --sicoob-turquesa: #00AE9D;
            --sicoob-branco: #FFFFFF;
            --sicoob-cinza: #58595B;
        }

        body {
            background-color: #f8f9fa;
        }

        .navbar {
            background-color: var(--sicoob-verde-escuro) !important;
            box-shadow: 0 2px 8px rgba(0, 54, 65, 0.2);
        }

        .navbar-brand, .navbar-nav .nav-link {
            color: var(--sicoob-branco) !important;
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
        }

        .card-header {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            color: var(--sicoob-branco);
            border-radius: 12px 12px 0 0 !important;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa));
            border: none;
            border-radius: 8px;
            font-weight: 600;
        }

        .btn-success {
            background-color: var(--sicoob-verde-claro);
            border-color: var(--sicoob-verde-claro);
            color: var(--sicoob-verde-escuro);
            font-weight: 600;
        }

        .test-result {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .result-success {
            border-left: 4px solid #28a745;
        }

        .result-error {
            border-left: 4px solid #dc3545;
        }

        .json-display {
            background-color: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <div class="container mt-4">
        <!-- Cabeçalho -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-plug me-2"></i>Teste de Conexão - API Intranet</h2>
                <p class="text-muted">Teste as conexões com a API da Intranet Sicoob</p>
            </div>
        </div>

        <!-- Mensagens -->
        <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Configurações da API -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog me-2"></i>Configurações da API
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>URL da API:</strong> <?php echo EDU_API_URL; ?><br>
                        <strong>Usuário:</strong> <?php echo EDU_API_USER; ?><br>
                        <strong>Token:</strong> <?php echo substr(EDU_API_TOKEN, 0, 10) . '...'; ?>
                    </div>
                    <div class="col-md-6">
                        <strong>Cache Time:</strong> <?php echo EDU_API_CACHE_TIME; ?> segundos<br>
                        <strong>Cache Path:</strong> <?php echo EDU_API_CACHE_PATH; ?><br>
                        <strong>Debug Mode:</strong> <?php echo EDU_DEBUG_MODE ? 'Ativo' : 'Inativo'; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Testes Básicos -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-flask me-2"></i>Testes Básicos
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <form method="POST" class="d-inline">
                            <button type="submit" name="test_usuarios" class="btn btn-primary w-100">
                                <i class="fas fa-users me-2"></i>Testar Usuários
                            </button>
                        </form>
                    </div>
                    <div class="col-md-4">
                        <form method="POST" class="d-inline">
                            <button type="submit" name="test_agencias" class="btn btn-primary w-100">
                                <i class="fas fa-building me-2"></i>Testar Agências
                            </button>
                        </form>
                    </div>
                    <div class="col-md-4">
                        <form method="POST" class="d-inline">
                            <button type="submit" name="test_cache" class="btn btn-success w-100">
                                <i class="fas fa-memory me-2"></i>Testar Cache
                            </button>
                        </form>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <form method="POST">
                            <div class="input-group">
                                <input type="email" name="email" class="form-control" placeholder="Email do usuário" required>
                                <button type="submit" name="test_email" class="btn btn-outline-primary">
                                    <i class="fas fa-search me-1"></i>Buscar
                                </button>
                            </div>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <form method="POST">
                            <div class="input-group">
                                <input type="text" name="numero_agencia" class="form-control" placeholder="Número da agência" required>
                                <button type="submit" name="test_agencia_numero" class="btn btn-outline-primary">
                                    <i class="fas fa-search me-1"></i>Buscar
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <form method="POST" class="d-inline">
                            <button type="submit" name="clear_cache" class="btn btn-outline-danger">
                                <i class="fas fa-trash me-2"></i>Limpar Cache
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Resultados dos Testes -->
        <?php if (!empty($test_results)): ?>
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>Resultados dos Testes
                </h5>
            </div>
            <div class="card-body">
                <?php foreach ($test_results as $test_name => $result): ?>
                <div class="test-result <?php echo $result['success'] ? 'result-success' : 'result-error'; ?>">
                    <h6>
                        <i class="fas <?php echo $result['success'] ? 'fa-check-circle text-success' : 'fa-times-circle text-danger'; ?> me-2"></i>
                        <?php 
                        $test_titles = [
                            'usuarios' => 'Teste de Usuários',
                            'agencias' => 'Teste de Agências',
                            'cache' => 'Teste de Cache',
                            'email' => 'Busca por Email',
                            'agencia_numero' => 'Busca por Número de Agência'
                        ];
                        echo $test_titles[$test_name] ?? ucfirst($test_name);
                        ?>
                    </h6>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Status:</strong> <?php echo $result['success'] ? 'Sucesso' : 'Erro'; ?><br>
                            <?php if (isset($result['count'])): ?>
                            <strong>Registros:</strong> <?php echo number_format($result['count']); ?><br>
                            <?php endif; ?>
                            <strong>Tempo:</strong> <?php echo $result['time']; ?>
                            
                            <?php if (isset($result['email'])): ?>
                            <br><strong>Email buscado:</strong> <?php echo htmlspecialchars($result['email']); ?>
                            <?php endif; ?>
                            
                            <?php if (isset($result['numero'])): ?>
                            <br><strong>Número buscado:</strong> <?php echo htmlspecialchars($result['numero']); ?>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <?php if ($result['success'] && isset($result['data']) && is_array($result['data'])): ?>
                            <button class="btn btn-sm btn-outline-info" onclick="toggleJson('<?php echo $test_name; ?>')">
                                <i class="fas fa-code me-1"></i>Ver JSON
                            </button>
                            <div id="json_<?php echo $test_name; ?>" class="json-display mt-2" style="display: none;">
                                <?php echo htmlspecialchars(json_encode($result['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleJson(testName) {
            const element = document.getElementById('json_' + testName);
            if (element.style.display === 'none') {
                element.style.display = 'block';
            } else {
                element.style.display = 'none';
            }
        }
    </script>
</body>
</html>
