<?php
/**
 * Debug da Inconsistência nas Métricas
 * 
 * Analisar por que alguns colaboradores têm mais cursos concluídos do que atribuídos
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

echo "<h1>🔍 Debug da Inconsistência nas Métricas</h1>";

// CPF de exemplo fornecido
$cpf_exemplo = '14584378681'; // CPF normalizado

echo "<h2>1. 📊 Análise do Colaborador Específico</h2>";

echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🚨 Problema Identificado:</h3>";
echo "<p><strong>CPF:</strong> 145.843.786-81</p>";
echo "<p><strong>Situação:</strong> 42 cursos atribuídos, mas 44 cursos concluídos</p>";
echo "<p><strong>Inconsistência:</strong> Matematicamente impossível ter mais concluídos que atribuídos</p>";
echo "</div>";

// Buscar dados brutos do colaborador
$query_raw = "
    SELECT 
        cpf,
        usuario,
        trilha,
        codigo_trilha,
        recurso,
        codigo_recurso,
        aprovacao,
        nota_recurso,
        aproveitamento,
        carga_horaria_recurso,
        data_conclusao,
        validade_recurso,
        andamento_etapa,
        concluir_trilha_ate,
        data_admissao
    FROM edu_relatorio_educacao
    WHERE cpf = ?
    ORDER BY trilha, recurso
";

$stmt = $pdo_edu->prepare($query_raw);
$stmt->execute([$cpf_exemplo]);
$dados_brutos = $stmt->fetchAll();

echo "<h2>2. 📋 Dados Brutos do Colaborador</h2>";

if (empty($dados_brutos)) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px;'>";
    echo "<p>❌ <strong>Colaborador não encontrado!</strong></p>";
    echo "<p>Verifique se o CPF está correto: $cpf_exemplo</p>";
    echo "</div>";
} else {
    $colaborador_info = $dados_brutos[0];
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>👤 Informações do Colaborador:</h3>";
    echo "<p><strong>Nome:</strong> " . htmlspecialchars($colaborador_info['usuario']) . "</p>";
    echo "<p><strong>CPF:</strong> " . htmlspecialchars($colaborador_info['cpf']) . "</p>";
    echo "<p><strong>Data Admissão:</strong> " . htmlspecialchars($colaborador_info['data_admissao']) . "</p>";
    echo "<p><strong>Total de Registros:</strong> " . count($dados_brutos) . "</p>";
    echo "</div>";

    // Análise detalhada dos cursos
    echo "<h3>📚 Análise Detalhada dos Cursos:</h3>";
    
    $total_registros = count($dados_brutos);
    $cursos_com_aprovacao_sim = 0;
    $cursos_com_data_conclusao = 0;
    $cursos_duplicados = [];
    $cursos_unicos = [];
    
    foreach ($dados_brutos as $curso) {
        // Contar aprovações
        if ($curso['aprovacao'] === 'Sim') {
            $cursos_com_aprovacao_sim++;
        }
        
        // Contar com data de conclusão
        if (!empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00') {
            $cursos_com_data_conclusao++;
        }
        
        // Verificar duplicatas
        $chave_curso = $curso['codigo_trilha'] . '|' . $curso['codigo_recurso'];
        if (isset($cursos_unicos[$chave_curso])) {
            if (!isset($cursos_duplicados[$chave_curso])) {
                $cursos_duplicados[$chave_curso] = [];
            }
            $cursos_duplicados[$chave_curso][] = $curso;
        } else {
            $cursos_unicos[$chave_curso] = $curso;
        }
    }
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h4>📊 Estatísticas Básicas:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Métrica</th>";
    echo "<th style='padding: 8px;'>Valor</th>";
    echo "<th style='padding: 8px;'>Descrição</th>";
    echo "</tr>";
    echo "<tr><td style='padding: 8px;'>Total de Registros</td><td style='padding: 8px;'>$total_registros</td><td style='padding: 8px;'>Todos os registros na base</td></tr>";
    echo "<tr><td style='padding: 8px;'>Cursos Únicos</td><td style='padding: 8px;'>" . count($cursos_unicos) . "</td><td style='padding: 8px;'>Cursos sem duplicação</td></tr>";
    echo "<tr><td style='padding: 8px;'>Aprovação = 'Sim'</td><td style='padding: 8px;'>$cursos_com_aprovacao_sim</td><td style='padding: 8px;'>Cursos aprovados</td></tr>";
    echo "<tr><td style='padding: 8px;'>Com Data Conclusão</td><td style='padding: 8px;'>$cursos_com_data_conclusao</td><td style='padding: 8px;'>Cursos com data preenchida</td></tr>";
    echo "<tr><td style='padding: 8px;'>Cursos Duplicados</td><td style='padding: 8px;'>" . count($cursos_duplicados) . "</td><td style='padding: 8px;'>Cursos com múltiplos registros</td></tr>";
    echo "</table>";
    echo "</div>";
    
    // Mostrar duplicatas se existirem
    if (!empty($cursos_duplicados)) {
        echo "<h4>🔄 Cursos Duplicados Encontrados:</h4>";
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        foreach ($cursos_duplicados as $chave => $duplicatas) {
            $curso_original = $cursos_unicos[$chave];
            echo "<h5>Curso: " . htmlspecialchars($curso_original['recurso']) . "</h5>";
            echo "<p><strong>Trilha:</strong> " . htmlspecialchars($curso_original['trilha']) . "</p>";
            echo "<p><strong>Código:</strong> $chave</p>";
            echo "<p><strong>Registros Duplicados:</strong> " . count($duplicatas) . "</p>";
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 4px;'>Aprovação</th>";
            echo "<th style='padding: 4px;'>Data Conclusão</th>";
            echo "<th style='padding: 4px;'>Nota</th>";
            echo "<th style='padding: 4px;'>Andamento</th>";
            echo "</tr>";
            
            // Mostrar registro original
            echo "<tr style='background: #d4edda;'>";
            echo "<td style='padding: 4px;'>" . htmlspecialchars($curso_original['aprovacao']) . "</td>";
            echo "<td style='padding: 4px;'>" . htmlspecialchars($curso_original['data_conclusao']) . "</td>";
            echo "<td style='padding: 4px;'>" . htmlspecialchars($curso_original['nota_recurso']) . "</td>";
            echo "<td style='padding: 4px;'>" . htmlspecialchars($curso_original['andamento_etapa']) . "</td>";
            echo "</tr>";
            
            // Mostrar duplicatas
            foreach ($duplicatas as $dup) {
                echo "<tr style='background: #f8d7da;'>";
                echo "<td style='padding: 4px;'>" . htmlspecialchars($dup['aprovacao']) . "</td>";
                echo "<td style='padding: 4px;'>" . htmlspecialchars($dup['data_conclusao']) . "</td>";
                echo "<td style='padding: 4px;'>" . htmlspecialchars($dup['nota_recurso']) . "</td>";
                echo "<td style='padding: 4px;'>" . htmlspecialchars($dup['andamento_etapa']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        echo "</div>";
    }
}

echo "<h2>3. 🔍 Análise da Lógica de Cálculo</h2>";

// Verificar como o sistema está calculando as métricas
echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🧮 Investigação da Lógica Atual:</h3>";

// Simular o cálculo como está sendo feito no sistema
if (!empty($dados_brutos)) {
    echo "<h4>Simulação do Cálculo Atual:</h4>";
    
    // Método 1: Contar todos os registros
    $metodo1_total = count($dados_brutos);
    $metodo1_concluidos = 0;
    foreach ($dados_brutos as $curso) {
        if ($curso['aprovacao'] === 'Sim') {
            $metodo1_concluidos++;
        }
    }
    
    // Método 2: Contar cursos únicos
    $metodo2_total = count($cursos_unicos);
    $metodo2_concluidos = 0;
    foreach ($cursos_unicos as $curso) {
        if ($curso['aprovacao'] === 'Sim') {
            $metodo2_concluidos++;
        }
    }
    
    // Método 3: Contar por data de conclusão
    $metodo3_concluidos = 0;
    foreach ($dados_brutos as $curso) {
        if (!empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00') {
            $metodo3_concluidos++;
        }
    }
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Método</th>";
    echo "<th style='padding: 8px;'>Total Atribuídos</th>";
    echo "<th style='padding: 8px;'>Total Concluídos</th>";
    echo "<th style='padding: 8px;'>Consistência</th>";
    echo "</tr>";
    echo "<tr><td style='padding: 8px;'>Todos os Registros</td><td style='padding: 8px;'>$metodo1_total</td><td style='padding: 8px;'>$metodo1_concluidos</td><td style='padding: 8px;'>" . ($metodo1_concluidos <= $metodo1_total ? "✅" : "❌") . "</td></tr>";
    echo "<tr><td style='padding: 8px;'>Cursos Únicos</td><td style='padding: 8px;'>$metodo2_total</td><td style='padding: 8px;'>$metodo2_concluidos</td><td style='padding: 8px;'>" . ($metodo2_concluidos <= $metodo2_total ? "✅" : "❌") . "</td></tr>";
    echo "<tr><td style='padding: 8px;'>Por Data Conclusão</td><td style='padding: 8px;'>-</td><td style='padding: 8px;'>$metodo3_concluidos</td><td style='padding: 8px;'>-</td></tr>";
    echo "</table>";
}
echo "</div>";

echo "<h2>4. 🔧 Possíveis Causas do Problema</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Hipóteses para a Inconsistência:</h3>";

echo "<h4>1. Duplicação de Registros:</h4>";
echo "<ul>";
echo "<li><strong>Problema:</strong> Mesmo curso aparece múltiplas vezes na base</li>";
echo "<li><strong>Causa:</strong> Importações duplicadas ou atualizações que criam novos registros</li>";
echo "<li><strong>Efeito:</strong> Total atribuídos conta únicos, concluídos conta todos</li>";
echo "<li><strong>Solução:</strong> Usar DISTINCT ou GROUP BY na consulta</li>";
echo "</ul>";

echo "<h4>2. Lógica de Contagem Diferente:</h4>";
echo "<ul>";
echo "<li><strong>Problema:</strong> Critérios diferentes para 'atribuído' vs 'concluído'</li>";
echo "<li><strong>Causa:</strong> Uma consulta usa DISTINCT, outra não</li>";
echo "<li><strong>Efeito:</strong> Bases de cálculo inconsistentes</li>";
echo "<li><strong>Solução:</strong> Padronizar critérios de contagem</li>";
echo "</ul>";

echo "<h4>3. Dados Históricos:</h4>";
echo "<ul>";
echo "<li><strong>Problema:</strong> Cursos concluídos antes da atribuição atual</li>";
echo "<li><strong>Causa:</strong> Mudanças no sistema ou reatribuições</li>";
echo "<li><strong>Efeito:</strong> Conclusões 'órfãs' sem atribuição correspondente</li>";
echo "<li><strong>Solução:</strong> Filtrar por período ou status ativo</li>";
echo "</ul>";

echo "<h4>4. Problema na Query:</h4>";
echo "<ul>";
echo "<li><strong>Problema:</strong> JOINs ou WHERE clauses incorretos</li>";
echo "<li><strong>Causa:</strong> Consultas mal estruturadas</li>";
echo "<li><strong>Efeito:</strong> Contagens incorretas</li>";
echo "<li><strong>Solução:</strong> Revisar e corrigir consultas SQL</li>";
echo "</ul>";
echo "</div>";

echo "<h2>5. 🛠️ Investigação das Consultas Atuais</h2>";

// Vou buscar como está sendo feito o cálculo no sistema atual
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📝 Análise das Consultas do Sistema:</h3>";

echo "<h4>Consulta Atual (Hipotética):</h4>";
echo "<pre style='background: #ffffff; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
-- Possível consulta atual (PROBLEMÁTICA)
SELECT 
    cpf,
    COUNT(*) as total_cursos,                    -- Conta TODOS os registros
    COUNT(CASE WHEN aprovacao = 'Sim' THEN 1 END) as cursos_concluidos  -- Conta TODAS as aprovações
FROM edu_relatorio_educacao 
WHERE cpf = ?
GROUP BY cpf

-- Problema: Se há duplicatas, conta múltiplas vezes
");
echo "</pre>";

echo "<h4>Consulta Corrigida (Sugerida):</h4>";
echo "<pre style='background: #ffffff; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
-- Consulta corrigida (RECOMENDADA)
SELECT 
    cpf,
    COUNT(DISTINCT CONCAT(codigo_trilha, '|', codigo_recurso)) as total_cursos,
    COUNT(DISTINCT CASE WHEN aprovacao = 'Sim' 
          THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_concluidos
FROM edu_relatorio_educacao 
WHERE cpf = ?
GROUP BY cpf

-- Solução: Usa DISTINCT para evitar duplicatas
");
echo "</pre>";
echo "</div>";

echo "<h2>6. 🧪 Teste da Correção</h2>";

if (!empty($dados_brutos)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>✅ Aplicando Correção no Colaborador de Exemplo:</h3>";
    
    // Aplicar a lógica corrigida
    $cursos_unicos_corrigidos = [];
    $cursos_concluidos_corrigidos = 0;
    
    foreach ($dados_brutos as $curso) {
        $chave_unica = $curso['codigo_trilha'] . '|' . $curso['codigo_recurso'];
        
        if (!isset($cursos_unicos_corrigidos[$chave_unica])) {
            $cursos_unicos_corrigidos[$chave_unica] = $curso;
            
            // Contar concluídos apenas uma vez por curso único
            if ($curso['aprovacao'] === 'Sim') {
                $cursos_concluidos_corrigidos++;
            }
        }
    }
    
    $total_corrigido = count($cursos_unicos_corrigidos);
    
    echo "<h4>Resultado da Correção:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Métrica</th>";
    echo "<th style='padding: 8px;'>Antes (Problemático)</th>";
    echo "<th style='padding: 8px;'>Depois (Corrigido)</th>";
    echo "<th style='padding: 8px;'>Status</th>";
    echo "</tr>";
    echo "<tr><td style='padding: 8px;'>Total Atribuídos</td><td style='padding: 8px;'>42</td><td style='padding: 8px;'>$total_corrigido</td><td style='padding: 8px;'>✅ Corrigido</td></tr>";
    echo "<tr><td style='padding: 8px;'>Total Concluídos</td><td style='padding: 8px;'>44</td><td style='padding: 8px;'>$cursos_concluidos_corrigidos</td><td style='padding: 8px;'>✅ Corrigido</td></tr>";
    echo "<tr><td style='padding: 8px;'>Consistência</td><td style='padding: 8px;'>❌ Inconsistente</td><td style='padding: 8px;'>" . ($cursos_concluidos_corrigidos <= $total_corrigido ? "✅ Consistente" : "❌ Ainda inconsistente") . "</td><td style='padding: 8px;'>-</td></tr>";
    echo "</table>";
    echo "</div>";
}

echo "<h2>7. 🔍 Causa Raiz Identificada</h2>";

echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Problema Encontrado no Código:</h3>";

echo "<h4>Inconsistência na Lógica de Cálculo:</h4>";
echo "<pre style='background: #ffffff; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
// LINHA 120-122: Cálculo inicial (PROBLEMÁTICO)
COUNT(DISTINCT recurso) as total_cursos,  // Conta cursos ÚNICOS
SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as cursos_aprovados,  // Conta TODOS os registros
COUNT(CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00' THEN 1 END) as cursos_concluidos,  // Conta TODOS os registros

// LINHA 164-171: Recálculo posterior (CORRETO)
foreach (\$cursos_colaborador as \$curso) {
    if (!empty(\$curso['data_conclusao']) && \$curso['data_conclusao'] !== '0000-00-00') {
        \$concluidos++;  // Conta cursos ÚNICOS
    }
}
\$colaborador['cursos_concluidos'] = \$concluidos; // Sobrescreve o valor
");
echo "</pre>";

echo "<h4>Explicação do Problema:</h4>";
echo "<ol>";
echo "<li><strong>total_cursos:</strong> Usa COUNT(DISTINCT recurso) - conta cursos únicos ✅</li>";
echo "<li><strong>cursos_concluidos (inicial):</strong> Usa COUNT(...) - conta TODOS os registros ❌</li>";
echo "<li><strong>cursos_concluidos (recálculo):</strong> Loop em cursos únicos - conta corretamente ✅</li>";
echo "<li><strong>Resultado:</strong> Se há duplicatas, o valor inicial é maior que o recalculado</li>";
echo "</ol>";

echo "<h4>Cenário do Problema:</h4>";
echo "<ul>";
echo "<li>📊 <strong>Colaborador tem:</strong> 42 cursos únicos, mas 50 registros totais (duplicatas)</li>";
echo "<li>📊 <strong>Cálculo inicial:</strong> total_cursos = 42 (DISTINCT), cursos_concluidos = 44 (ALL)</li>";
echo "<li>📊 <strong>Recálculo:</strong> cursos_concluidos = 40 (baseado em cursos únicos)</li>";
echo "<li>📊 <strong>Exibição:</strong> 42 atribuídos, 40 concluídos (correto) OU 44 concluídos (se não recalculado)</li>";
echo "</ul>";
echo "</div>";

echo "<h2>8. 📋 Recomendações</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Ações Recomendadas:</h3>";

echo "<h4>1. Correção Imediata:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Padronizar Query:</strong> Usar DISTINCT em todas as contagens</li>";
echo "<li>✅ <strong>Remover Recálculo:</strong> Calcular corretamente na query inicial</li>";
echo "<li>✅ <strong>Chave Única:</strong> Usar CONCAT(codigo_trilha, '|', codigo_recurso)</li>";
echo "</ul>";

echo "<h4>2. Query Corrigida Sugerida:</h4>";
echo "<pre style='background: #ffffff; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
SELECT
    cpf,
    MAX(usuario) as usuario,
    COUNT(DISTINCT CONCAT(codigo_trilha, '|', codigo_recurso)) as total_cursos,
    COUNT(DISTINCT CASE WHEN aprovacao = 'Sim'
          THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_aprovados,
    COUNT(DISTINCT CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00'
          THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_concluidos
FROM edu_relatorio_educacao
WHERE cpf = ?
GROUP BY cpf
");
echo "</pre>";

echo "<h4>3. Melhorias Futuras:</h4>";
echo "<ul>";
echo "<li>🚀 <strong>Constraint Única:</strong> Adicionar constraint na base para evitar duplicatas</li>";
echo "<li>🚀 <strong>Validação:</strong> Implementar validações na importação</li>";
echo "<li>🚀 <strong>Monitoramento:</strong> Alertas para inconsistências</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Análise executada em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📊 Voltar para Análise</a>";
echo "<a href='detalhes_colaborador.php?cpf=$cpf_exemplo' style='background: #dc3545; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🔍 Ver Colaborador</a>";
echo "</p>";
?>
