<?php
/**
 * Script para limpar TODOS os caches e forçar dados frescos
 */

require_once 'config/config.php';

echo "<h1>🧹 Limpeza Completa de Cache</h1>\n";

// 1. Limpar cache de arquivos
$cache_files_removed = 0;
$cache_dir = EDU_API_CACHE_PATH;

if (is_dir($cache_dir)) {
    $files = glob($cache_dir . '*.json');
    foreach ($files as $file) {
        if (unlink($file)) {
            $cache_files_removed++;
        }
    }
}

echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>✅ Cache de arquivos:</strong> $cache_files_removed arquivos removidos</p>";
echo "</div>";

// 2. Limpar cache de sessão PHP
if (session_status() == PHP_SESSION_ACTIVE) {
    session_destroy();
    echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>✅ Sessão PHP:</strong> Destruída</p>";
    echo "</div>";
}

// 3. Limpar cache de opcodes (se disponível)
if (function_exists('opcache_reset')) {
    opcache_reset();
    echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>✅ Cache de opcodes:</strong> Limpo</p>";
    echo "</div>";
}

// 4. Verificar se há outros arquivos de cache
$other_cache_patterns = [
    '*.cache',
    '*.tmp',
    'cache_*',
    'temp_*'
];

$other_files_removed = 0;
foreach ($other_cache_patterns as $pattern) {
    $files = glob($cache_dir . $pattern);
    foreach ($files as $file) {
        if (unlink($file)) {
            $other_files_removed++;
        }
    }
}

if ($other_files_removed > 0) {
    echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>✅ Outros caches:</strong> $other_files_removed arquivos removidos</p>";
    echo "</div>";
}

// 5. Forçar headers para evitar cache do navegador
header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");

echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>✅ Headers de cache:</strong> Configurados para evitar cache do navegador</p>";
echo "</div>";

echo "<h2>🎯 Próximos Passos</h2>\n";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>✅ Cache Completamente Limpo!</h3>";
echo "<p><strong>Agora teste a página principal:</strong></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores&nocache=" . time() . "' ";
echo "style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>";
echo "🔄 Testar Página Principal</a>";
echo "</p>";
echo "<p><strong>O que deve acontecer:</strong></p>";
echo "<ul>";
echo "<li>✅ Usuários ativos da Intranet devem aparecer normalmente</li>";
echo "<li>✅ Usuários não encontrados na Intranet devem aparecer como 'Sem PA Definido'</li>";
echo "<li>❌ Usuários inativos da Intranet NÃO devem aparecer (incluindo ALICE BEATRIZ)</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>⚠️ Se o problema persistir:</h3>";
echo "<p>Pode ser necessário:</p>";
echo "<ul>";
echo "<li>Limpar cache do navegador (Ctrl+Shift+Del)</li>";
echo "<li>Abrir em aba anônima/privada</li>";
echo "<li>Verificar se há cache em outro nível (servidor web, proxy, etc.)</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>🔧 Informações Técnicas:</strong></p>";
echo "<ul>";
echo "<li><strong>Diretório de cache:</strong> " . EDU_API_CACHE_PATH . "</li>";
echo "<li><strong>Arquivos removidos:</strong> " . ($cache_files_removed + $other_files_removed) . "</li>";
echo "<li><strong>Timestamp:</strong> " . time() . "</li>";
echo "<li><strong>Data/Hora:</strong> " . date('d/m/Y H:i:s') . "</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Limpeza executada em: " . date('d/m/Y H:i:s') . "</em></p>";
?>
