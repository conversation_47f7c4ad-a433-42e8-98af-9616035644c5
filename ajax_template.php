<?php
require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'classes/EmailManager.php';

// Verificar permissões
checkPageAccess(['gestor', 'admin']);

header('Content-Type: application/json');

if (!isset($_GET['id'])) {
    echo json_encode(['error' => 'ID do template não fornecido']);
    exit;
}

$template_id = (int)$_GET['id'];
$emailManager = new EmailManager();

try {
    $template = $emailManager->buscarTemplate($template_id);
    
    if (!$template) {
        echo json_encode(['error' => 'Template não encontrado']);
        exit;
    }
    
    // Processar variáveis para exemplo
    $exemplo_colaborador = [
        'nome' => '<PERSON>',
        'cpf' => '12345678901',
        'email' => '<EMAIL>',
        'funcao' => '<PERSON>erente de Relacionamento',
        'codigo_unidade' => '3049-PA EXEMPLO',
        'cursos_a_vencer' => 3,
        'cursos_vencidos' => 1
    ];
    
    $conteudo_exemplo = $emailManager->processarVariaveis($template, $exemplo_colaborador);
    
    // Se for apenas para buscar dados do template (para edição)
    if (isset($_GET['edit']) && $_GET['edit'] == '1') {
        echo json_encode([
            'success' => true,
            'template' => $template
        ]);
    } else {
        // Para visualização com exemplo
        echo json_encode([
            'success' => true,
            'template' => $template,
            'exemplo' => $conteudo_exemplo
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode(['error' => $e->getMessage()]);
}
?>
