<?php
/**
 * Teste de Prazos de Conclusão
 * 
 * Este arquivo testa o cálculo dos prazos de conclusão dos cursos.
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'classes/IntranetAPI.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

echo "<h1>⏰ Teste de Prazos de Conclusão</h1>";

// Teste 1: Verificar configurações de prazos personalizados
echo "<h2>1. ⚙️ Verificação de Prazos Personalizados</h2>";

try {
    $stmt_prazos = $pdo_edu->query("SELECT * FROM edu_prazos_personalizados WHERE prazo_personalizado_ativo = 1");
    $prazos_config = $stmt_prazos->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($prazos_config)) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>Prazos Personalizados Encontrados:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Total de configurações ativas:</strong> " . count($prazos_config) . "</li>";
        echo "</ul>";
        
        echo "<p><strong>Configurações:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 5px;'>Trilha</th>";
        echo "<th style='padding: 5px;'>Curso</th>";
        echo "<th style='padding: 5px;'>Primeiro Prazo</th>";
        echo "<th style='padding: 5px;'>Renovação</th>";
        echo "</tr>";
        
        foreach ($prazos_config as $config) {
            echo "<tr>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($config['codigo_trilha']) . "</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($config['codigo_recurso']) . "</td>";
            echo "<td style='padding: 5px;'>" . $config['primeiro_prazo_dias'] . " dias</td>";
            echo "<td style='padding: 5px;'>" . $config['renovacao_prazo_dias'] . " dias</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>⚠️ <strong>Nenhum prazo personalizado ativo encontrado</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro ao verificar prazos:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 2: Função de cálculo de prazo personalizado
echo "<h2>2. 🧮 Teste da Função de Cálculo</h2>";

// Função para calcular prazo personalizado (copiada do arquivo principal)
function calcularPrazoPersonalizado($cpf, $codigo_trilha, $codigo_recurso, $data_admissao, $prazo_padrao, $pdo) {
    // Buscar configuração do prazo personalizado
    $query_config = "
        SELECT primeiro_prazo_dias, renovacao_prazo_dias
        FROM edu_prazos_personalizados
        WHERE codigo_trilha = ? AND codigo_recurso = ? AND prazo_personalizado_ativo = 1
    ";
    
    $stmt_config = $pdo->prepare($query_config);
    $stmt_config->execute([$codigo_trilha, $codigo_recurso]);
    $config = $stmt_config->fetch(PDO::FETCH_ASSOC);
    
    if (!$config) {
        return $prazo_padrao; // Fallback para prazo padrão
    }
    
    // Verificar se já houve conclusões anteriores
    $query_conclusoes = "
        SELECT data_conclusao
        FROM edu_relatorio_educacao
        WHERE cpf = ? AND codigo_trilha = ? AND codigo_recurso = ?
        AND data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00'
        ORDER BY data_conclusao DESC
        LIMIT 1
    ";
    
    $stmt_conclusoes = $pdo->prepare($query_conclusoes);
    $stmt_conclusoes->execute([$cpf, $codigo_trilha, $codigo_recurso]);
    $ultima_conclusao = $stmt_conclusoes->fetch(PDO::FETCH_ASSOC);
    
    if ($ultima_conclusao) {
        // Renovação: usar data da última conclusão + prazo de renovação
        $data_base = new DateTime($ultima_conclusao['data_conclusao']);
        $data_base->add(new DateInterval('P' . $config['renovacao_prazo_dias'] . 'D'));
    } else {
        // Primeira vez: usar data de admissão + primeiro prazo
        $data_base = new DateTime($data_admissao);
        $data_base->add(new DateInterval('P' . $config['primeiro_prazo_dias'] . 'D'));
    }
    
    return $data_base->format('Y-m-d');
}

try {
    // Buscar alguns colaboradores para teste
    $query_colaboradores = "
        SELECT DISTINCT cpf, usuario, data_admissao
        FROM edu_relatorio_educacao 
        WHERE cpf IS NOT NULL AND cpf != '' 
        AND data_admissao IS NOT NULL AND data_admissao != '0000-00-00'
        LIMIT 3
    ";
    
    $stmt = $pdo_edu->prepare($query_colaboradores);
    $stmt->execute();
    $colaboradores_teste = $stmt->fetchAll();
    
    if (!empty($colaboradores_teste)) {
        echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>Teste com Colaboradores Reais:</strong></p>";
        
        foreach ($colaboradores_teste as $colaborador) {
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<h6><strong>" . htmlspecialchars($colaborador['usuario']) . "</strong></h6>";
            echo "<p><strong>CPF:</strong> " . substr($colaborador['cpf'], 0, 3) . ".***.***-**</p>";
            echo "<p><strong>Data Admissão:</strong> " . date('d/m/Y', strtotime($colaborador['data_admissao'])) . "</p>";
            
            // Buscar cursos deste colaborador
            $query_cursos = "
                SELECT codigo_trilha, trilha, codigo_recurso, recurso, concluir_trilha_ate, data_conclusao
                FROM edu_relatorio_educacao
                WHERE cpf = ?
                LIMIT 3
            ";
            
            $stmt_cursos = $pdo_edu->prepare($query_cursos);
            $stmt_cursos->execute([$colaborador['cpf']]);
            $cursos = $stmt_cursos->fetchAll();
            
            echo "<p><strong>Cursos (primeiros 3):</strong></p>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 3px;'>Curso</th>";
            echo "<th style='padding: 3px;'>Prazo Padrão</th>";
            echo "<th style='padding: 3px;'>Prazo Calculado</th>";
            echo "<th style='padding: 3px;'>Tipo</th>";
            echo "</tr>";
            
            foreach ($cursos as $curso) {
                $prazo_calculado = calcularPrazoPersonalizado(
                    $colaborador['cpf'],
                    $curso['codigo_trilha'],
                    $curso['codigo_recurso'],
                    $colaborador['data_admissao'],
                    $curso['concluir_trilha_ate'],
                    $pdo_edu
                );
                
                $tipo_prazo = ($prazo_calculado === $curso['concluir_trilha_ate']) ? 'Padrão' : 'Personalizado';
                
                echo "<tr>";
                echo "<td style='padding: 3px;'>" . htmlspecialchars(substr($curso['recurso'], 0, 30)) . "...</td>";
                echo "<td style='padding: 3px;'>" . ($curso['concluir_trilha_ate'] ? date('d/m/Y', strtotime($curso['concluir_trilha_ate'])) : 'N/A') . "</td>";
                echo "<td style='padding: 3px;'>" . ($prazo_calculado ? date('d/m/Y', strtotime($prazo_calculado)) : 'N/A') . "</td>";
                echo "<td style='padding: 3px;'>$tipo_prazo</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "</div>";
        }
        echo "</div>";
        
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>⚠️ <strong>Nenhum colaborador com data de admissão encontrado para teste</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro no teste de cálculo:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 3: Verificar estrutura da tabela
echo "<h2>3. 🗃️ Verificação da Estrutura da Tabela</h2>";

try {
    $describe_query = "DESCRIBE edu_prazos_personalizados";
    $stmt_describe = $pdo_edu->prepare($describe_query);
    $stmt_describe->execute();
    $colunas = $stmt_describe->fetchAll();
    
    echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Estrutura da Tabela edu_prazos_personalizados:</strong></p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 3px;'>Campo</th>";
    echo "<th style='padding: 3px;'>Tipo</th>";
    echo "<th style='padding: 3px;'>Nulo</th>";
    echo "<th style='padding: 3px;'>Padrão</th>";
    echo "</tr>";
    
    foreach ($colunas as $coluna) {
        echo "<tr>";
        echo "<td style='padding: 3px;'>" . htmlspecialchars($coluna['Field']) . "</td>";
        echo "<td style='padding: 3px;'>" . htmlspecialchars($coluna['Type']) . "</td>";
        echo "<td style='padding: 3px;'>" . htmlspecialchars($coluna['Null']) . "</td>";
        echo "<td style='padding: 3px;'>" . htmlspecialchars($coluna['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro ao verificar estrutura:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Resumo final
echo "<h2>4. 📋 Resumo da Implementação</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Funcionalidade Implementada</h3>";

echo "<h4>✅ Cálculo de Prazos:</h4>";
echo "<ul>";
echo "<li><strong>Prazo Personalizado:</strong> Baseado em configurações específicas por trilha/curso</li>";
echo "<li><strong>Primeiro Prazo:</strong> Data admissão + dias configurados</li>";
echo "<li><strong>Renovação:</strong> Última conclusão + dias de renovação</li>";
echo "<li><strong>Fallback:</strong> Prazo padrão quando não há configuração personalizada</li>";
echo "</ul>";

echo "<h4>✅ Exibição nos Cards:</h4>";
echo "<ul>";
echo "<li><strong>Modal de Detalhes:</strong> Nova coluna 'Prazo' na tabela de cursos</li>";
echo "<li><strong>Data do Prazo:</strong> Formato dd/mm/aaaa</li>";
echo "<li><strong>Indicador:</strong> Badge mostrando se é personalizado</li>";
echo "<li><strong>Status:</strong> Vencido, a vencer, em dia</li>";
echo "</ul>";

echo "<h4>✅ Lógica Implementada:</h4>";
echo "<ul>";
echo "<li><strong>Busca Configuração:</strong> Por código_trilha + codigo_recurso</li>";
echo "<li><strong>Verifica Conclusões:</strong> Histórico de conclusões anteriores</li>";
echo "<li><strong>Calcula Data:</strong> Baseado em admissão ou última conclusão</li>";
echo "<li><strong>Determina Status:</strong> Baseado na diferença de dias</li>";
echo "</ul>";

echo "<h4>✅ Arquivos Modificados:</h4>";
echo "<ul>";
echo "<li><strong>analise_colaboradores.php:</strong> Funções de cálculo e configurações</li>";
echo "<li><strong>detalhes_colaborador.php:</strong> Exibição dos prazos no modal</li>";
echo "<li><strong>test_prazos.php:</strong> Arquivo de teste (este arquivo)</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>👥 Testar Cards com Prazos</a>";
echo "<a href='gerenciar_trilhas.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>⚙️ Gerenciar Prazos</a>";
echo "</p>";
?>
