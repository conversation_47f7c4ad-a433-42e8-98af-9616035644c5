<?php
// Configurar output buffer e headers de forma robusta
while (ob_get_level()) {
    ob_end_clean();
}
ob_start();

// Iniciar sessão se necessário
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Função para retornar JSON e sair
function returnJSON($data) {
    while (ob_get_level()) {
        ob_end_clean();
    }
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// Verificar autenticação
if (!isset($_SESSION['user_id'])) {
    returnJSON(['success' => false, 'error' => 'Usuário não autenticado']);
}

try {
    // Incluir apenas o banco de dados
    require_once 'config/database.php';
    $pdo_edu = $pdo;

    $search = $_GET['search'] ?? '';
    $limit = (int)($_GET['limit'] ?? 20);

    if ($limit <= 0 || $limit > 100) {
        $limit = 20;
    }

    // Verificar se a tabela existe
    $stmt = $pdo_edu->query("SHOW TABLES LIKE 'edu_relatorio_educacao'");
    if ($stmt->rowCount() === 0) {
        returnJSON(['success' => false, 'error' => 'Tabela edu_relatorio_educacao não encontrada']);
    }

    // Construir query
    $params = [];
    $whereConditions = ["email IS NOT NULL", "email != ''", "email LIKE '%@%'"];

    if (!empty($search)) {
        $searchTerm = "%$search%";
        $whereConditions[] = "(usuario LIKE ? OR cpf LIKE ? OR email LIKE ? OR funcao LIKE ?)";
        $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
    }

    $sql = "
        SELECT
            cpf,
            MAX(usuario) as nome,
            MAX(email) as email,
            MAX(funcao) as funcao,
            MAX(codigo_unidade) as codigo_unidade,
            COUNT(DISTINCT CASE WHEN concluir_trilha_ate BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
                               AND (aprovacao != 'Sim' OR aprovacao IS NULL)
                               THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_a_vencer,
            COUNT(DISTINCT CASE WHEN concluir_trilha_ate < CURDATE()
                               AND (aprovacao != 'Sim' OR aprovacao IS NULL)
                               THEN CONCAT(codigo_trilha, '|', codigo_recurso) END) as cursos_vencidos
        FROM edu_relatorio_educacao
        WHERE " . implode(" AND ", $whereConditions) . "
        GROUP BY cpf
        ORDER BY MAX(usuario)
        LIMIT " . $limit;

    $stmt = $pdo_edu->prepare($sql);
    $stmt->execute($params);
    $colaboradores = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Tentar aplicar filtro da Intranet de forma segura
    $mapa_usuarios_ativos = [];
    $mapa_usuarios_todos = [];
    $filtro_aplicado = false;
    $debug_info = [];

    // Tentar incluir a API da Intranet de forma mais segura
    try {
        // Capturar qualquer output que possa ser gerado
        ob_start();

        if (file_exists('config/config.php') && file_exists('classes/IntranetAPI.php')) {
            require_once 'config/config.php';
            require_once 'classes/IntranetAPI.php';

            $api = new IntranetAPI();

            // Buscar todos os usuários
            $usuarios_todos = $api->listarUsuarios(true, false);
            $usuarios_ativos = $api->listarUsuariosAtivos(true);

            // Limpar qualquer output gerado
            ob_end_clean();

            if ($usuarios_ativos !== false && $usuarios_todos !== false) {
                // Processar usuários ativos
                foreach ($usuarios_ativos as $usuario) {
                    if (!empty($usuario['cpf'])) {
                        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                        $mapa_usuarios_ativos[$cpf_normalizado] = true;
                    }
                }

                // Processar todos os usuários
                foreach ($usuarios_todos as $usuario) {
                    if (!empty($usuario['cpf'])) {
                        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                        $mapa_usuarios_todos[$cpf_normalizado] = true;
                    }
                }

                $filtro_aplicado = true;
                $debug_info['metodo'] = 'api_direta';
            }
        }

        // Se não conseguiu carregar diretamente, tentar o script separado
        if (!$filtro_aplicado && function_exists('exec') && file_exists('get_intranet_users.php')) {
            $output = [];
            $return_var = 0;
            exec('php get_intranet_users.php 2>/dev/null', $output, $return_var);

            if ($return_var === 0 && !empty($output)) {
                $intranet_data = json_decode(implode('', $output), true);
                if ($intranet_data && isset($intranet_data['ativos']) && isset($intranet_data['todos'])) {
                    $mapa_usuarios_ativos = $intranet_data['ativos'];
                    $mapa_usuarios_todos = $intranet_data['todos'];
                    $filtro_aplicado = true;
                    $debug_info['metodo'] = 'script_separado';
                }
            }
        }

    } catch (Exception $e) {
        // Limpar output em caso de erro
        if (ob_get_level()) {
            ob_end_clean();
        }
        $debug_info['erro'] = $e->getMessage();
    }

    // Formatar dados para retorno
    $resultado = [];
    foreach ($colaboradores as $colaborador) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
        
        // Aplicar filtro da Intranet se disponível
        if ($filtro_aplicado) {
            // Se existe na Intranet mas não está ativo, pular
            if (isset($mapa_usuarios_todos[$cpf_normalizado]) && !isset($mapa_usuarios_ativos[$cpf_normalizado])) {
                continue;
            }
        }
        
        // Determinar status na Intranet para exibição
        $status_intranet = 'Sem PA Definido';
        if ($filtro_aplicado) {
            if (isset($mapa_usuarios_todos[$cpf_normalizado])) {
                $status_intranet = isset($mapa_usuarios_ativos[$cpf_normalizado]) ? 'Ativo' : 'Inativo';
            }
        }

        $resultado[] = [
            'cpf' => $colaborador['cpf'],
            'nome' => $colaborador['nome'] ?: 'Nome não informado',
            'email' => $colaborador['email'] ?: 'E-mail não informado',
            'funcao' => $colaborador['funcao'] ?: 'Função não informada',
            'pa' => extrairPA($colaborador['codigo_unidade'] ?? ''),
            'cursos_a_vencer' => (int)($colaborador['cursos_a_vencer'] ?? 0),
            'cursos_vencidos' => (int)($colaborador['cursos_vencidos'] ?? 0),
            'cpf_formatado' => formatarCpf($colaborador['cpf']),
            'status_intranet' => $status_intranet
        ];
    }
    
    returnJSON([
        'success' => true,
        'colaboradores' => $resultado,
        'total' => count($resultado),
        'filtro_intranet' => $filtro_aplicado
    ]);
    
} catch (Exception $e) {
    returnJSON(['success' => false, 'error' => $e->getMessage()]);
}

function formatarCpf($cpf) {
    $cpf = preg_replace('/[^0-9]/', '', $cpf);
    if (strlen($cpf) == 11) {
        return substr($cpf, 0, 3) . '.' . substr($cpf, 3, 3) . '.' . substr($cpf, 6, 3) . '-' . substr($cpf, 9, 2);
    }
    return $cpf;
}

function extrairPA($codigo_unidade) {
    if (preg_match('/(\d+)-(.+)/', $codigo_unidade, $matches)) {
        return $matches[1] . ' - ' . $matches[2];
    }
    return $codigo_unidade ?: 'N/A';
}
?>
