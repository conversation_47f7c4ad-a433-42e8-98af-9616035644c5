<?php
/**
 * Teste da Regra de Data de Admissão para Prazos Personalizados
 * 
 * Este arquivo testa se a regra de data de admissão (01/01/2023) está funcionando corretamente.
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

echo "<h1>📅 Teste da Regra de Data de Admissão</h1>";

// Teste 1: Verificar regra implementada
echo "<h2>1. 📋 Regra Implementada</h2>";

echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Nova Regra de Negócio:</strong></p>";
echo "<ul>";
echo "<li>🗓️ <strong>Data de Corte:</strong> 01/01/2023</li>";
echo "<li>✅ <strong>Colaboradores Novos:</strong> Admitidos APÓS 01/01/2023 → Usam prazos personalizados</li>";
echo "<li>⏰ <strong>Colaboradores Antigos:</strong> Admitidos ATÉ 01/01/2023 → Usam prazos padrão</li>";
echo "<li>🎯 <strong>Objetivo:</strong> Aplicar novas regras apenas para colaboradores recentes</li>";
echo "</ul>";
echo "</div>";

// Teste 2: Verificar distribuição de colaboradores por data de admissão
echo "<h2>2. 📊 Distribuição de Colaboradores</h2>";

try {
    $query_distribuicao = "
        SELECT 
            CASE 
                WHEN data_admissao <= '2023-01-01' THEN 'Até 01/01/2023 (Prazo Padrão)'
                WHEN data_admissao > '2023-01-01' THEN 'Após 01/01/2023 (Prazo Personalizado)'
                ELSE 'Data Inválida'
            END as categoria,
            COUNT(DISTINCT cpf) as total_colaboradores,
            MIN(data_admissao) as primeira_admissao,
            MAX(data_admissao) as ultima_admissao
        FROM edu_relatorio_educacao 
        WHERE cpf IS NOT NULL AND cpf != '' 
        AND data_admissao IS NOT NULL AND data_admissao != '0000-00-00'
        GROUP BY categoria
        ORDER BY categoria
    ";
    
    $stmt = $pdo_edu->prepare($query_distribuicao);
    $stmt->execute();
    $distribuicao = $stmt->fetchAll();
    
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>✅ <strong>Distribuição por Data de Admissão:</strong></p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Categoria</th>";
    echo "<th style='padding: 8px;'>Colaboradores</th>";
    echo "<th style='padding: 8px;'>Primeira Admissão</th>";
    echo "<th style='padding: 8px;'>Última Admissão</th>";
    echo "<th style='padding: 8px;'>Tipo de Prazo</th>";
    echo "</tr>";
    
    $total_geral = 0;
    foreach ($distribuicao as $categoria) {
        $total_geral += $categoria['total_colaboradores'];
        $tipo_prazo = strpos($categoria['categoria'], 'Padrão') !== false ? 
            '<span style="color: #6c757d;">⏰ Padrão</span>' : 
            '<span style="color: #28a745;">✅ Personalizado</span>';
            
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($categoria['categoria']) . "</td>";
        echo "<td style='padding: 8px; text-align: center;'><strong>" . number_format($categoria['total_colaboradores']) . "</strong></td>";
        echo "<td style='padding: 8px; text-align: center;'>" . date('d/m/Y', strtotime($categoria['primeira_admissao'])) . "</td>";
        echo "<td style='padding: 8px; text-align: center;'>" . date('d/m/Y', strtotime($categoria['ultima_admissao'])) . "</td>";
        echo "<td style='padding: 8px; text-align: center;'>$tipo_prazo</td>";
        echo "</tr>";
    }
    
    echo "<tr style='background: #e9ecef; font-weight: bold;'>";
    echo "<td style='padding: 8px;'>TOTAL</td>";
    echo "<td style='padding: 8px; text-align: center;'>" . number_format($total_geral) . "</td>";
    echo "<td colspan='3' style='padding: 8px; text-align: center;'>-</td>";
    echo "</tr>";
    echo "</table>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro na consulta:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 3: Testar função de cálculo de prazo
echo "<h2>3. 🧪 Teste da Função de Cálculo</h2>";

// Função de teste (cópia da função principal com logs)
function testarCalculoPrazoComRegra($cpf, $codigo_trilha, $codigo_recurso, $data_admissao, $prazo_padrao, $pdo) {
    $log = [];
    
    // Validar parâmetros de entrada
    if (empty($cpf) || empty($codigo_trilha) || empty($codigo_recurso) || empty($data_admissao)) {
        $log[] = "❌ Parâmetros inválidos";
        return ['resultado' => $prazo_padrao, 'log' => $log, 'tipo' => 'erro'];
    }
    
    // Validar data de admissão
    if ($data_admissao === '0000-00-00' || !strtotime($data_admissao)) {
        $log[] = "❌ Data de admissão inválida: $data_admissao";
        return ['resultado' => $prazo_padrao, 'log' => $log, 'tipo' => 'erro'];
    }
    
    // REGRA: Prazos personalizados só valem para colaboradores admitidos após 01/01/2023
    $data_corte = '2023-01-01';
    $log[] = "📅 Data de admissão: " . date('d/m/Y', strtotime($data_admissao));
    $log[] = "📅 Data de corte: " . date('d/m/Y', strtotime($data_corte));
    
    if ($data_admissao <= $data_corte) {
        $log[] = "⏰ Colaborador admitido ANTES/EM $data_corte → Usar prazo padrão";
        return ['resultado' => $prazo_padrao, 'log' => $log, 'tipo' => 'padrao_regra'];
    }
    
    $log[] = "✅ Colaborador admitido APÓS $data_corte → Verificar prazo personalizado";
    
    // Buscar configuração do prazo personalizado
    $query_config = "
        SELECT primeiro_prazo_dias, renovacao_prazo_dias
        FROM edu_prazos_personalizados
        WHERE codigo_trilha = ? AND codigo_recurso = ? AND prazo_personalizado_ativo = 1
    ";
    
    $stmt_config = $pdo->prepare($query_config);
    $stmt_config->execute([$codigo_trilha, $codigo_recurso]);
    $config = $stmt_config->fetch(PDO::FETCH_ASSOC);
    
    if (!$config) {
        $log[] = "⏰ Sem configuração personalizada → Usar prazo padrão";
        return ['resultado' => $prazo_padrao, 'log' => $log, 'tipo' => 'padrao_sem_config'];
    }
    
    $log[] = "⚙️ Configuração encontrada: {$config['primeiro_prazo_dias']} dias (primeiro) / {$config['renovacao_prazo_dias']} dias (renovação)";
    
    // Simular cálculo personalizado (simplificado para teste)
    try {
        $data_base = new DateTime($data_admissao);
        $data_base->add(new DateInterval('P' . $config['primeiro_prazo_dias'] . 'D'));
        $resultado = $data_base->format('Y-m-d');
        
        $log[] = "✅ Prazo personalizado calculado: " . date('d/m/Y', strtotime($resultado));
        return ['resultado' => $resultado, 'log' => $log, 'tipo' => 'personalizado'];
        
    } catch (Exception $e) {
        $log[] = "❌ Erro no cálculo: " . $e->getMessage();
        return ['resultado' => $prazo_padrao, 'log' => $log, 'tipo' => 'erro_calculo'];
    }
}

try {
    // Buscar alguns colaboradores para teste (antes e depois da data de corte)
    $query_teste = "
        SELECT DISTINCT 
            cpf, usuario, data_admissao,
            CASE 
                WHEN data_admissao <= '2023-01-01' THEN 'ANTES'
                ELSE 'DEPOIS'
            END as categoria_teste
        FROM edu_relatorio_educacao 
        WHERE cpf IS NOT NULL AND cpf != '' 
        AND data_admissao IS NOT NULL AND data_admissao != '0000-00-00'
        ORDER BY data_admissao
        LIMIT 6
    ";
    
    $stmt_teste = $pdo_edu->prepare($query_teste);
    $stmt_teste->execute();
    $colaboradores_teste = $stmt_teste->fetchAll();
    
    if (!empty($colaboradores_teste)) {
        echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>🎯 Teste com Colaboradores Reais</h3>";
        
        foreach ($colaboradores_teste as $colaborador) {
            $resultado = testarCalculoPrazoComRegra(
                $colaborador['cpf'],
                'TESTE_TRILHA',
                'TESTE_CURSO',
                $colaborador['data_admissao'],
                '2024-12-31',
                $pdo_edu
            );
            
            $cor_categoria = $colaborador['categoria_teste'] === 'ANTES' ? '#dc3545' : '#28a745';
            $icone_tipo = [
                'personalizado' => '✅',
                'padrao_regra' => '⏰',
                'padrao_sem_config' => '⚙️',
                'erro' => '❌',
                'erro_calculo' => '❌'
            ][$resultado['tipo']] ?? '❓';
            
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<h6>$icone_tipo <strong>" . htmlspecialchars($colaborador['usuario']) . "</strong> 
                  <span style='color: $cor_categoria; font-weight: bold;'>({$colaborador['categoria_teste']} do corte)</span></h6>";
            echo "<p><strong>CPF:</strong> " . substr($colaborador['cpf'], 0, 3) . "***</p>";
            echo "<p><strong>Data Admissão:</strong> " . date('d/m/Y', strtotime($colaborador['data_admissao'])) . "</p>";
            echo "<p><strong>Resultado:</strong> " . ($resultado['resultado'] ? date('d/m/Y', strtotime($resultado['resultado'])) : 'N/A') . "</p>";
            echo "<p><strong>Tipo:</strong> " . ucfirst(str_replace('_', ' ', $resultado['tipo'])) . "</p>";
            echo "<details style='margin-top: 10px;'>";
            echo "<summary style='cursor: pointer; color: #007bff;'>Ver Log Detalhado</summary>";
            echo "<ul style='margin-top: 5px;'>";
            foreach ($resultado['log'] as $log_item) {
                echo "<li style='font-size: 0.9rem;'>$log_item</li>";
            }
            echo "</ul>";
            echo "</details>";
            echo "</div>";
        }
        echo "</div>";
        
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>⚠️ <strong>Nenhum colaborador encontrado para teste</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro no teste:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Resumo final
echo "<h2>4. 📋 Resumo da Implementação</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Regra de Data de Admissão Implementada</h3>";

echo "<h4>📅 Data de Corte: 01/01/2023</h4>";
echo "<ul>";
echo "<li><strong>Colaboradores Antigos (≤ 01/01/2023):</strong> Sempre usam prazo padrão</li>";
echo "<li><strong>Colaboradores Novos (> 01/01/2023):</strong> Podem usar prazos personalizados</li>";
echo "</ul>";

echo "<h4>🔄 Fluxo de Decisão:</h4>";
echo "<ol>";
echo "<li><strong>Verificar Data de Admissão:</strong> Comparar com 01/01/2023</li>";
echo "<li><strong>Se Anterior/Igual:</strong> Retornar prazo padrão (ignorar configurações)</li>";
echo "<li><strong>Se Posterior:</strong> Verificar se há configuração personalizada</li>";
echo "<li><strong>Com Configuração:</strong> Calcular prazo personalizado</li>";
echo "<li><strong>Sem Configuração:</strong> Usar prazo padrão</li>";
echo "</ol>";

echo "<h4>✅ Arquivos Modificados:</h4>";
echo "<ul>";
echo "<li><strong>analise_colaboradores.php:</strong> Função calcularPrazoPersonalizado()</li>";
echo "<li><strong>detalhes_colaborador.php:</strong> Função + lógica de exibição</li>";
echo "<li><strong>Interface:</strong> Indicadores visuais do motivo do prazo</li>";
echo "</ul>";

echo "<h4>🎨 Melhorias na Interface:</h4>";
echo "<ul>";
echo "<li><strong>Indicador Visual:</strong> 'Personalizado' vs 'Padrão'</li>";
echo "<li><strong>Tooltip Informativo:</strong> Motivo da aplicação do prazo</li>";
echo "<li><strong>Cores Diferenciadas:</strong> Verde para personalizado, cinza para padrão</li>";
echo "</ul>";

echo "<h4>🔍 Como Verificar:</h4>";
echo "<ul>";
echo "<li><strong>Modal de Detalhes:</strong> Coluna 'Prazo' mostra tipo e motivo</li>";
echo "<li><strong>Colaboradores Antigos:</strong> Sempre mostram 'Padrão'</li>";
echo "<li><strong>Colaboradores Novos:</strong> Mostram 'Personalizado' quando configurado</li>";
echo "<li><strong>Tooltip:</strong> Hover sobre motivo mostra explicação completa</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>👥 Testar Regra</a>";
echo "<a href='gerenciar_trilhas.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>⚙️ Gerenciar Prazos</a>";
echo "</p>";
?>
