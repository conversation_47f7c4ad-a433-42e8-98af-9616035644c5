<?php
// Relatório de Prazos e Vencimentos
echo '<table border="1" style="border-collapse: collapse;">';
echo '<tr style="background-color: #ffc107; color: black; font-weight: bold;">';
echo '<td colspan="8" style="padding: 10px; text-align: center; font-size: 16px;">';
echo 'RELATÓRIO DE PRAZOS E VENCIMENTOS';
echo '</td>';
echo '</tr>';

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td style="padding: 8px;">Gerado em:</td>';
echo '<td colspan="7" style="padding: 8px;">' . date('d/m/Y H:i:s') . '</td>';
echo '</tr>';

// Buscar todos os colaboradores e seus prazos
$colaboradores_query = "SELECT DISTINCT cpf FROM edu_relatorio_educacao";
$todos_colaboradores = $pdo_edu->query($colaboradores_query)->fetchAll();

$cronograma = [];
$hoje = new DateTime();

foreach ($todos_colaboradores as $colaborador) {
    $cursos_colaborador = buscarCursosColaborador($colaborador['cpf'], $pdo_edu, $prazos_config);
    
    foreach ($cursos_colaborador as $curso) {
        if (!empty($curso['prazo_calculado'])) {
            $prazo = new DateTime($curso['prazo_calculado']);
            $mes_ano = $prazo->format('Y-m');
            
            if (!isset($cronograma[$mes_ano])) {
                $cronograma[$mes_ano] = [
                    'mes_nome' => $prazo->format('m/Y'),
                    'vencidos' => 0,
                    'a_vencer' => 0,
                    'futuros' => 0,
                    'total' => 0
                ];
            }
            
            $cronograma[$mes_ano]['total']++;
            
            if ($curso['status_prazo'] === 'vencido') {
                $cronograma[$mes_ano]['vencidos']++;
            } elseif ($curso['status_prazo'] === 'a_vencer') {
                $cronograma[$mes_ano]['a_vencer']++;
            } else {
                $cronograma[$mes_ano]['futuros']++;
            }
        }
    }
}

// Ordenar por mês
ksort($cronograma);

// Cabeçalhos
echo '<tr style="background-color: #ffc107; color: black; font-weight: bold;">';
echo '<td style="padding: 8px;">Mês/Ano</td>';
echo '<td style="padding: 8px; text-align: center;">Total Prazos</td>';
echo '<td style="padding: 8px; text-align: center;">Vencidos</td>';
echo '<td style="padding: 8px; text-align: center;">A Vencer</td>';
echo '<td style="padding: 8px; text-align: center;">Futuros</td>';
echo '<td style="padding: 8px; text-align: center;">% Vencidos</td>';
echo '<td style="padding: 8px; text-align: center;">% A Vencer</td>';
echo '<td style="padding: 8px; text-align: center;">Status</td>';
echo '</tr>';

// Dados
foreach ($cronograma as $mes_dados) {
    $perc_vencidos = $mes_dados['total'] > 0 ? ($mes_dados['vencidos'] / $mes_dados['total']) * 100 : 0;
    $perc_a_vencer = $mes_dados['total'] > 0 ? ($mes_dados['a_vencer'] / $mes_dados['total']) * 100 : 0;
    
    $status = 'Normal';
    $cor_status = '#28a745';
    
    if ($perc_vencidos > 30) {
        $status = 'Crítico';
        $cor_status = '#dc3545';
    } elseif ($perc_vencidos > 15 || $perc_a_vencer > 50) {
        $status = 'Atenção';
        $cor_status = '#fd7e14';
    } elseif ($perc_vencidos > 5 || $perc_a_vencer > 30) {
        $status = 'Monitorar';
        $cor_status = '#ffc107';
    }
    
    echo '<tr>';
    echo '<td style="padding: 6px; font-weight: bold;">' . $mes_dados['mes_nome'] . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . $mes_dados['total'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; color: #dc3545; font-weight: bold;">' . $mes_dados['vencidos'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; color: #fd7e14; font-weight: bold;">' . $mes_dados['a_vencer'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; color: #28a745;">' . $mes_dados['futuros'] . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . number_format($perc_vencidos, 1) . '%</td>';
    echo '<td style="padding: 6px; text-align: center;">' . number_format($perc_a_vencer, 1) . '%</td>';
    echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: ' . $cor_status . ';">' . $status . '</td>';
    echo '</tr>';
}

echo '</table>';
?>
