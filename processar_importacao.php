<?php
// Configurar relatório de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 0); // Não mostrar erros na tela para não quebrar JSON

// Buffer de saída para capturar qualquer output indesejado
ob_start();

try {
    require_once 'config/config.php';
    require_once 'config/database.php';
    require_once 'edu_auth_check.php';
    require_once 'functions.php';

    // Verificar se usuário pode gerenciar importações (apenas gestores)
    if (!$edu_permissions->canManageImports()) {
        ob_clean();
        echo json_encode([
            'success' => false,
            'message' => 'Acesso negado. Apenas gestores podem gerenciar importações.'
        ]);
        exit;
    }

    // Limpar qualquer output anterior
    ob_clean();

    header('Content-Type: application/json');

    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        echo json_encode(['success' => false, 'message' => 'Método não permitido']);
        exit;
    }
    // Verificar se é o início do processamento ou continuação
    if (isset($_FILES['csv_file'])) {
        // Início do processamento - upload do arquivo
        $file = $_FILES['csv_file'];
        
        // Validações básicas
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('Erro no upload do arquivo.');
        }
        
        if ($file['size'] > EDU_MAX_FILE_SIZE) {
            throw new Exception('Arquivo muito grande. Tamanho máximo: ' . (EDU_MAX_FILE_SIZE / 1024 / 1024) . 'MB');
        }
        
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, EDU_ALLOWED_EXTENSIONS)) {
            throw new Exception('Tipo de arquivo não permitido. Use apenas: ' . implode(', ', EDU_ALLOWED_EXTENSIONS));
        }
        
        // Mover arquivo para diretório de uploads
        $filename = uniqid() . '_' . $file['name'];
        $filepath = EDU_UPLOAD_PATH . $filename;
        
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            throw new Exception('Erro ao salvar arquivo.');
        }
        
        // Registrar importação no banco
        $stmt = $pdo_edu->prepare("
            INSERT INTO edu_importacoes (nome_arquivo, tamanho_arquivo, total_registros, usuario_id, status) 
            VALUES (?, ?, 0, ?, 'processando')
        ");
        $stmt->execute([$file['name'], $file['size'], $_SESSION['user_id']]);
        $importacao_id = $pdo_edu->lastInsertId();
        
        // Contar total de linhas do arquivo
        $handle = fopen($filepath, 'r');
        if ($handle === false) {
            throw new Exception('Não foi possível abrir o arquivo CSV.');
        }
        
        // Detectar delimitador
        $delimiter = ',';
        $first_line = fgets($handle);
        rewind($handle);
        
        $first_line_converted = detectAndConvertEncoding($first_line);
        if (substr_count($first_line_converted, ';') > substr_count($first_line_converted, ',')) {
            $delimiter = ';';
        }
        
        // Contar linhas
        $total_rows = 0;
        fgetcsv($handle, 0, $delimiter); // Pular cabeçalho
        while (fgetcsv($handle, 0, $delimiter) !== false) {
            $total_rows++;
        }
        fclose($handle);
        
        // Atualizar total de registros
        $stmt = $pdo_edu->prepare("UPDATE edu_importacoes SET total_registros = ? WHERE id = ?");
        $stmt->execute([$total_rows, $importacao_id]);
        
        // Limpar tabela antes da importação
        $pdo_edu->exec("TRUNCATE TABLE edu_relatorio_educacao");
        
        // Criar arquivo de status para monitoramento
        $status_file = EDU_UPLOAD_PATH . 'status_' . $importacao_id . '.json';
        file_put_contents($status_file, json_encode([
            'importacao_id' => $importacao_id,
            'filepath' => $filepath,
            'delimiter' => $delimiter,
            'total' => $total_rows,
            'processed' => 0,
            'errors' => 0,
            'status' => 'processing',
            'current_batch' => 1,
            'batch_size' => EDU_BATCH_SIZE,
            'start_time' => time()
        ]));
        
        echo json_encode([
            'success' => true,
            'message' => 'Arquivo enviado com sucesso. Iniciando processamento...',
            'importacao_id' => $importacao_id,
            'total_registros' => $total_rows
        ]);
        
    } elseif (isset($_POST['importacao_id'])) {
        // Continuação do processamento - processar próximo lote
        $importacao_id = intval($_POST['importacao_id']);
        $status_file = EDU_UPLOAD_PATH . 'status_' . $importacao_id . '.json';
        
        if (!file_exists($status_file)) {
            throw new Exception('Arquivo de status não encontrado.');
        }
        
        $status = json_decode(file_get_contents($status_file), true);
        
        if ($status['status'] === 'completed' || $status['status'] === 'error') {
            echo json_encode($status);
            exit;
        }
        
        // Processar próximo lote
        $result = processNextBatch($status, $pdo_edu);
        
        // Atualizar arquivo de status
        file_put_contents($status_file, json_encode($result));
        
        echo json_encode($result);
        
    } else {
        throw new Exception('Parâmetros inválidos.');
    }
    
} catch (Exception $e) {
    ob_clean();
    echo json_encode([
        'success' => false,
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    ob_clean();
    echo json_encode([
        'success' => false,
        'status' => 'error',
        'message' => 'Erro fatal: ' . $e->getMessage()
    ]);
}

function processNextBatch($status, $pdo_edu) {
    try {
        $filepath = $status['filepath'];
        $delimiter = $status['delimiter'];
        $batch_size = $status['batch_size'];
        $processed = $status['processed'];
        $total = $status['total'];
        $current_batch = $status['current_batch'];
        $importacao_id = $status['importacao_id'];
        
        $handle = fopen($filepath, 'r');
        if ($handle === false) {
            throw new Exception('Não foi possível abrir o arquivo CSV.');
        }
        
        // Pular cabeçalho
        fgetcsv($handle, 0, $delimiter);
        
        // Pular linhas já processadas
        for ($i = 0; $i < $processed; $i++) {
            fgetcsv($handle, 0, $delimiter);
        }
        
        // Preparar statement de inserção
        $insert_stmt = $pdo_edu->prepare("
            INSERT INTO edu_relatorio_educacao (
                codigo_unidade, hierarquia_unidade, usuario, identificador, situacao_usuario,
                cpf, email, data_admissao, funcao, superior_imediato, tipo_trilha, etapa,
                codigo_trilha, trilha, aprovado_trilha, aproveitamento, situacao_trilha,
                iniciar_trilha_em, concluir_trilha_ate, data_aprovacao_trilha, carga_horaria_trilha,
                prazo_etapa_trilha_jornada, andamento_etapa, total_horas_essenciais_etapa,
                horas_essenciais_feitas, horas_complementares_feitas, codigo_recurso, recurso,
                nota_recurso, aprovacao, carga_horaria_recurso, data_conclusao, validade_recurso,
                responsavel_associacao, usuario_importacao
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $batch_processed = 0;
        $batch_errors = 0;
        $error_details = [];
        
        $pdo_edu->beginTransaction();
        
        // Processar lote
        while ($batch_processed < $batch_size && ($row = fgetcsv($handle, 0, $delimiter)) !== false) {
            try {
                // Aplicar tratamento de codificação
                $row = processCSVLine($row);
                
                // Pular linhas vazias
                if (empty(array_filter($row))) {
                    continue;
                }
                
                // Mapear dados das colunas
                $data = [
                    trim($row[0] ?? ''),  // codigo_unidade
                    trim($row[1] ?? ''),  // hierarquia_unidade
                    trim($row[2] ?? ''),  // usuario
                    trim($row[3] ?? ''),  // identificador
                    trim($row[4] ?? ''),  // situacao_usuario
                    formatCpf($row[5] ?? ''),  // cpf
                    trim($row[6] ?? ''),  // email
                    convertDate($row[7] ?? ''),  // data_admissao
                    trim($row[8] ?? ''),  // funcao
                    trim($row[9] ?? ''),  // superior_imediato
                    trim($row[10] ?? ''), // tipo_trilha
                    trim($row[11] ?? ''), // etapa
                    trim($row[12] ?? ''), // codigo_trilha
                    trim($row[13] ?? ''), // trilha
                    trim($row[14] ?? ''), // aprovado_trilha
                    cleanNumericValue($row[15] ?? ''), // aproveitamento
                    trim($row[16] ?? ''), // situacao_trilha
                    convertDate($row[17] ?? ''), // iniciar_trilha_em
                    convertDate($row[18] ?? ''), // concluir_trilha_ate
                    convertDate($row[19] ?? ''), // data_aprovacao_trilha
                    cleanTimeValue($row[20] ?? ''), // carga_horaria_trilha
                    trim($row[21] ?? ''), // prazo_etapa_trilha_jornada
                    trim($row[22] ?? ''), // andamento_etapa
                    cleanNumericValue($row[23] ?? ''), // total_horas_essenciais_etapa
                    cleanNumericValue($row[24] ?? ''), // horas_essenciais_feitas
                    cleanNumericValue($row[25] ?? ''), // horas_complementares_feitas
                    trim($row[26] ?? ''), // codigo_recurso
                    trim($row[27] ?? ''), // recurso
                    cleanNumericValue($row[28] ?? ''), // nota_recurso
                    trim($row[29] ?? ''), // aprovacao
                    cleanTimeValue($row[30] ?? ''), // carga_horaria_recurso
                    convertDate($row[31] ?? ''), // data_conclusao
                    convertDate($row[32] ?? ''), // validade_recurso
                    trim($row[33] ?? ''), // responsavel_associacao
                    $_SESSION['user_id'] // usuario_importacao
                ];
                
                // Validação básica - CPF é obrigatório
                if (empty($data[5])) {
                    throw new Exception("CPF é obrigatório");
                }
                
                $insert_stmt->execute($data);
                $batch_processed++;
                
            } catch (Exception $e) {
                $batch_errors++;
                $error_details[] = "Linha " . ($processed + $batch_processed + 1) . ": " . $e->getMessage();
            }
        }
        
        $pdo_edu->commit();
        fclose($handle);
        
        // Atualizar contadores
        $new_processed = $processed + $batch_processed;
        $new_errors = $status['errors'] + $batch_errors;
        
        // Calcular progresso
        $progress_percent = ($new_processed / $total) * 100;
        $is_completed = $new_processed >= $total;
        
        // Calcular tempo estimado
        $elapsed_time = time() - $status['start_time'];
        $estimated_total_time = $elapsed_time * ($total / max(1, $new_processed));
        $estimated_remaining = max(0, $estimated_total_time - $elapsed_time);
        
        $result = [
            'importacao_id' => $importacao_id,
            'filepath' => $filepath,
            'delimiter' => $delimiter,
            'total' => $total,
            'processed' => $new_processed,
            'errors' => $new_errors,
            'current_batch' => $current_batch + 1,
            'batch_size' => $batch_size,
            'start_time' => $status['start_time'],
            'progress_percent' => round($progress_percent, 1),
            'estimated_remaining' => $estimated_remaining,
            'status' => $is_completed ? 'completed' : 'processing'
        ];
        
        if ($is_completed) {
            // Atualizar status da importação no banco
            $final_status = ($new_errors == 0) ? 'concluido' : 'erro';
            $detalhes_erro = empty($error_details) ? null : implode("\n", array_slice($error_details, 0, 10));
            
            $stmt = $pdo_edu->prepare("
                UPDATE edu_importacoes 
                SET registros_importados = ?, registros_erro = ?, status = ?, detalhes_erro = ?, data_conclusao = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$new_processed, $new_errors, $final_status, $detalhes_erro, $importacao_id]);
            
            // Remover arquivo temporário
            if (file_exists($filepath)) {
                unlink($filepath);
            }
            
            $result['message'] = "Importação concluída! $new_processed registros importados" . 
                                ($new_errors > 0 ? " com $new_errors erros" : "");
        }
        
        return $result;
        
    } catch (Exception $e) {
        if (isset($pdo_edu) && $pdo_edu->inTransaction()) {
            $pdo_edu->rollBack();
        }
        
        return [
            'status' => 'error',
            'message' => 'Erro durante o processamento: ' . $e->getMessage()
        ];
    }
}
?>
