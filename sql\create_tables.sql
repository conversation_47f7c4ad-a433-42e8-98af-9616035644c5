-- Script de criação das tabelas para o Sistema de Educação Corporativa
-- Banco: sicoob_access_control

USE sicoob_access_control;

-- Tabela principal para armazenar dados do relatório de educação corporativa
CREATE TABLE IF NOT EXISTS edu_relatorio_educacao (
    id INT AUTO_INCREMENT PRIMARY KEY,
    codigo_unidade VARCHAR(50),
    hierarquia_unidade TEXT,
    usuario VARCHAR(255),
    identificador VARCHAR(100),
    situacao_usuario VARCHAR(50),
    cpf VARCHAR(11) NOT NULL,
    email VARCHAR(255),
    data_admissao DATE,
    funcao VARCHAR(255),
    superior_imediato VARCHAR(255),
    tipo_trilha VARCHAR(100),
    etapa VARCHAR(255),
    codigo_trilha VARCHAR(50),
    trilha VARCHAR(255),
    aprovado_trilha VARCHAR(10),
    aproveitamento DECIMAL(5,2),
    situacao_trilha VARCHAR(50),
    iniciar_trilha_em DATE,
    concluir_trilha_ate DATE,
    data_aprovacao_trilha DATE,
    carga_horaria_trilha VARCHAR(10),
    prazo_etapa_trilha_jornada VARCHAR(100),
    andamento_etapa VARCHAR(50),
    total_horas_essenciais_etapa INT,
    horas_essenciais_feitas INT,
    horas_complementares_feitas INT,
    codigo_recurso VARCHAR(50),
    recurso VARCHAR(255),
    nota_recurso DECIMAL(5,2),
    aprovacao VARCHAR(10),
    carga_horaria_recurso VARCHAR(10),
    data_conclusao DATE,
    validade_recurso DATE,
    responsavel_associacao VARCHAR(255),
    data_importacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    usuario_importacao INT,
    
    INDEX idx_cpf (cpf),
    INDEX idx_codigo_unidade (codigo_unidade),
    INDEX idx_usuario (usuario),
    INDEX idx_trilha (codigo_trilha),
    INDEX idx_data_importacao (data_importacao),
    INDEX idx_usuario_importacao (usuario_importacao),
    
    FOREIGN KEY (usuario_importacao) REFERENCES usuarios(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de controle de importações
CREATE TABLE IF NOT EXISTS edu_importacoes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome_arquivo VARCHAR(255) NOT NULL,
    tamanho_arquivo INT NOT NULL,
    total_registros INT NOT NULL,
    registros_importados INT DEFAULT 0,
    registros_erro INT DEFAULT 0,
    status ENUM('processando', 'concluido', 'erro') DEFAULT 'processando',
    detalhes_erro TEXT,
    usuario_id INT NOT NULL,
    data_importacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_conclusao TIMESTAMP NULL,
    
    INDEX idx_usuario (usuario_id),
    INDEX idx_status (status),
    INDEX idx_data (data_importacao),
    
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
