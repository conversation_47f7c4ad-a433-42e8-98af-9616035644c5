<?php
// Dashboard Executivo
// Este arquivo gera um relatório consolidado com principais métricas e indicadores

// Buscar estatísticas gerais
$stats_query = "
    SELECT 
        COUNT(DISTINCT cpf) as total_colaboradores,
        COUNT(DISTINCT trilha) as total_trilhas,
        COUNT(DISTINCT recurso) as total_cursos,
        COUNT(*) as total_atribuicoes,
        SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as cursos_aprovados,
        AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento,
        COUNT(CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00' THEN 1 END) as total_concluidos
    FROM edu_relatorio_educacao
";
$estatisticas = $pdo_edu->query($stats_query)->fetch();

// Buscar estatísticas por trilha
$trilhas_query = "
    SELECT 
        trilha,
        COUNT(DISTINCT cpf) as colaboradores,
        COUNT(DISTINCT recurso) as cursos,
        SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as aprovados,
        COUNT(*) as total_atribuicoes,
        AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento,
        (SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) / COUNT(*)) * 100 as percentual_aprovacao
    FROM edu_relatorio_educacao
    WHERE trilha IS NOT NULL AND trilha != ''
    GROUP BY trilha
    ORDER BY percentual_aprovacao DESC
";
$trilhas_stats = $pdo_edu->query($trilhas_query)->fetchAll();

// Buscar todos os colaboradores para calcular métricas detalhadas
$colaboradores_query = "
    SELECT DISTINCT cpf, usuario, funcao, codigo_unidade
    FROM edu_relatorio_educacao
    ORDER BY usuario
";
$todos_colaboradores = $pdo_edu->query($colaboradores_query)->fetchAll();

// Calcular métricas por status
$colaboradores_vencidos = 0;
$colaboradores_a_vencer = 0;
$colaboradores_em_andamento = 0;
$colaboradores_em_dia = 0;
$total_cursos_vencidos = 0;
$total_cursos_a_vencer = 0;

foreach ($todos_colaboradores as $colaborador) {
    $cursos_colaborador = buscarCursosColaborador($colaborador['cpf'], $pdo_edu, $prazos_config);
    
    $tem_vencido = false;
    $tem_a_vencer = false;
    $tem_em_andamento = false;
    
    foreach ($cursos_colaborador as $curso) {
        if ($curso['status_prazo'] === 'vencido') {
            $tem_vencido = true;
            $total_cursos_vencidos++;
        } elseif ($curso['status_prazo'] === 'a_vencer') {
            $tem_a_vencer = true;
            $total_cursos_a_vencer++;
        } elseif (!empty($curso['andamento_etapa']) && $curso['aprovacao'] !== 'Sim') {
            $tem_em_andamento = true;
        }
    }
    
    if ($tem_vencido) {
        $colaboradores_vencidos++;
    } elseif ($tem_a_vencer) {
        $colaboradores_a_vencer++;
    } elseif ($tem_em_andamento) {
        $colaboradores_em_andamento++;
    } else {
        $colaboradores_em_dia++;
    }
}

// Buscar distribuição por PA
$pas_stats = [];
foreach ($todos_colaboradores as $colaborador) {
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;
    
    $pa_key = 'Sem PA';
    if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
        $agencia_id = $usuario_intranet['agencia'];
        if (isset($mapa_agencias[$agencia_id])) {
            $agencia_data = $mapa_agencias[$agencia_id];
            $pa_key = $agencia_data['numero'] . ' - ' . $agencia_data['nome'];
        } else {
            $pa_key = 'PA ' . $agencia_id;
        }
    }
    
    if (!isset($pas_stats[$pa_key])) {
        $pas_stats[$pa_key] = 0;
    }
    $pas_stats[$pa_key]++;
}

// Ordenar PAs por quantidade de colaboradores
arsort($pas_stats);

// Cabeçalho do relatório
echo '<table border="1" style="border-collapse: collapse;">';
echo '<tr style="background-color: #003641; color: white; font-weight: bold;">';
echo '<td colspan="8" style="padding: 15px; text-align: center; font-size: 18px;">';
echo 'DASHBOARD EXECUTIVO - EDUCAÇÃO CORPORATIVA';
echo '</td>';
echo '</tr>';

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td style="padding: 8px;">Gerado em:</td>';
echo '<td colspan="7" style="padding: 8px;">' . date('d/m/Y H:i:s') . '</td>';
echo '</tr>';

echo '<tr><td colspan="8" style="padding: 10px;"></td></tr>'; // Espaçamento

// SEÇÃO 1: MÉTRICAS PRINCIPAIS
echo '<tr style="background-color: #00AE9D; color: white; font-weight: bold;">';
echo '<td colspan="8" style="padding: 12px; text-align: center; font-size: 16px;">📊 MÉTRICAS PRINCIPAIS</td>';
echo '</tr>';

echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 10px; font-weight: bold;">Total de Colaboradores</td>';
echo '<td style="padding: 10px; font-weight: bold;">Total de Trilhas</td>';
echo '<td style="padding: 10px; font-weight: bold;">Total de Cursos</td>';
echo '<td style="padding: 10px; font-weight: bold;">Total de Atribuições</td>';
echo '<td style="padding: 10px; font-weight: bold;">Cursos Aprovados</td>';
echo '<td style="padding: 10px; font-weight: bold;">Taxa de Aprovação</td>';
echo '<td style="padding: 10px; font-weight: bold;">Média Aproveitamento</td>';
echo '<td style="padding: 10px; font-weight: bold;">Taxa de Conclusão</td>';
echo '</tr>';

$taxa_aprovacao = $estatisticas['total_atribuicoes'] > 0 ? 
    ($estatisticas['cursos_aprovados'] / $estatisticas['total_atribuicoes']) * 100 : 0;
$taxa_conclusao = $estatisticas['total_atribuicoes'] > 0 ? 
    ($estatisticas['total_concluidos'] / $estatisticas['total_atribuicoes']) * 100 : 0;

echo '<tr>';
echo '<td style="padding: 10px; text-align: center; font-size: 16px; font-weight: bold; color: #003641;">' . number_format($estatisticas['total_colaboradores']) . '</td>';
echo '<td style="padding: 10px; text-align: center; font-size: 16px; font-weight: bold; color: #003641;">' . number_format($estatisticas['total_trilhas']) . '</td>';
echo '<td style="padding: 10px; text-align: center; font-size: 16px; font-weight: bold; color: #003641;">' . number_format($estatisticas['total_cursos']) . '</td>';
echo '<td style="padding: 10px; text-align: center; font-size: 16px; font-weight: bold; color: #003641;">' . number_format($estatisticas['total_atribuicoes']) . '</td>';
echo '<td style="padding: 10px; text-align: center; font-size: 16px; font-weight: bold; color: #28a745;">' . number_format($estatisticas['cursos_aprovados']) . '</td>';
echo '<td style="padding: 10px; text-align: center; font-size: 16px; font-weight: bold; color: #28a745;">' . number_format($taxa_aprovacao, 1) . '%</td>';
echo '<td style="padding: 10px; text-align: center; font-size: 16px; font-weight: bold; color: #007bff;">' . number_format($estatisticas['media_aproveitamento'], 1) . '%</td>';
echo '<td style="padding: 10px; text-align: center; font-size: 16px; font-weight: bold; color: #007bff;">' . number_format($taxa_conclusao, 1) . '%</td>';
echo '</tr>';

echo '<tr><td colspan="8" style="padding: 10px;"></td></tr>'; // Espaçamento

// SEÇÃO 2: STATUS DOS COLABORADORES
echo '<tr style="background-color: #00AE9D; color: white; font-weight: bold;">';
echo '<td colspan="8" style="padding: 12px; text-align: center; font-size: 16px;">👥 STATUS DOS COLABORADORES</td>';
echo '</tr>';

echo '<tr style="background-color: #e9ecef;">';
echo '<td colspan="2" style="padding: 10px; font-weight: bold; text-align: center;">Em Dia</td>';
echo '<td colspan="2" style="padding: 10px; font-weight: bold; text-align: center;">Em Andamento</td>';
echo '<td colspan="2" style="padding: 10px; font-weight: bold; text-align: center;">A Vencer</td>';
echo '<td colspan="2" style="padding: 10px; font-weight: bold; text-align: center;">Vencidos</td>';
echo '</tr>';

echo '<tr>';
echo '<td colspan="2" style="padding: 15px; text-align: center; font-size: 20px; font-weight: bold; color: #28a745;">' . $colaboradores_em_dia . '</td>';
echo '<td colspan="2" style="padding: 15px; text-align: center; font-size: 20px; font-weight: bold; color: #007bff;">' . $colaboradores_em_andamento . '</td>';
echo '<td colspan="2" style="padding: 15px; text-align: center; font-size: 20px; font-weight: bold; color: #fd7e14;">' . $colaboradores_a_vencer . '</td>';
echo '<td colspan="2" style="padding: 15px; text-align: center; font-size: 20px; font-weight: bold; color: #dc3545;">' . $colaboradores_vencidos . '</td>';
echo '</tr>';

echo '<tr>';
$perc_em_dia = ($colaboradores_em_dia / $estatisticas['total_colaboradores']) * 100;
$perc_andamento = ($colaboradores_em_andamento / $estatisticas['total_colaboradores']) * 100;
$perc_a_vencer = ($colaboradores_a_vencer / $estatisticas['total_colaboradores']) * 100;
$perc_vencidos = ($colaboradores_vencidos / $estatisticas['total_colaboradores']) * 100;

echo '<td colspan="2" style="padding: 8px; text-align: center; font-size: 14px; color: #28a745;">(' . number_format($perc_em_dia, 1) . '%)</td>';
echo '<td colspan="2" style="padding: 8px; text-align: center; font-size: 14px; color: #007bff;">(' . number_format($perc_andamento, 1) . '%)</td>';
echo '<td colspan="2" style="padding: 8px; text-align: center; font-size: 14px; color: #fd7e14;">(' . number_format($perc_a_vencer, 1) . '%)</td>';
echo '<td colspan="2" style="padding: 8px; text-align: center; font-size: 14px; color: #dc3545;">(' . number_format($perc_vencidos, 1) . '%)</td>';
echo '</tr>';

echo '<tr><td colspan="8" style="padding: 10px;"></td></tr>'; // Espaçamento

// SEÇÃO 3: PERFORMANCE POR TRILHA
echo '<tr style="background-color: #00AE9D; color: white; font-weight: bold;">';
echo '<td colspan="8" style="padding: 12px; text-align: center; font-size: 16px;">🎯 PERFORMANCE POR TRILHA</td>';
echo '</tr>';

echo '<tr style="background-color: #e9ecef; font-weight: bold;">';
echo '<td style="padding: 8px;">Trilha</td>';
echo '<td style="padding: 8px; text-align: center;">Colaboradores</td>';
echo '<td style="padding: 8px; text-align: center;">Cursos</td>';
echo '<td style="padding: 8px; text-align: center;">Atribuições</td>';
echo '<td style="padding: 8px; text-align: center;">Aprovados</td>';
echo '<td style="padding: 8px; text-align: center;">% Aprovação</td>';
echo '<td style="padding: 8px; text-align: center;">Média Aproveitamento</td>';
echo '<td style="padding: 8px; text-align: center;">Status</td>';
echo '</tr>';

foreach ($trilhas_stats as $trilha) {
    $cor_status = '#28a745'; // Verde
    $status_texto = 'Excelente';
    
    if ($trilha['percentual_aprovacao'] < 40) {
        $cor_status = '#dc3545'; // Vermelho
        $status_texto = 'Crítico';
    } elseif ($trilha['percentual_aprovacao'] < 60) {
        $cor_status = '#fd7e14'; // Laranja
        $status_texto = 'Regular';
    } elseif ($trilha['percentual_aprovacao'] < 80) {
        $cor_status = '#007bff'; // Azul
        $status_texto = 'Bom';
    }
    
    echo '<tr>';
    echo '<td style="padding: 6px;">' . htmlspecialchars($trilha['trilha']) . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . $trilha['colaboradores'] . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . $trilha['cursos'] . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . $trilha['total_atribuicoes'] . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . $trilha['aprovados'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; font-weight: bold;">' . number_format($trilha['percentual_aprovacao'], 1) . '%</td>';
    echo '<td style="padding: 6px; text-align: center;">' . ($trilha['media_aproveitamento'] ? number_format($trilha['media_aproveitamento'], 1) . '%' : 'N/A') . '</td>';
    echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: ' . $cor_status . ';">' . $status_texto . '</td>';
    echo '</tr>';
}

echo '<tr><td colspan="8" style="padding: 10px;"></td></tr>'; // Espaçamento

// SEÇÃO 4: DISTRIBUIÇÃO POR PA (TOP 10)
echo '<tr style="background-color: #00AE9D; color: white; font-weight: bold;">';
echo '<td colspan="8" style="padding: 12px; text-align: center; font-size: 16px;">🏢 DISTRIBUIÇÃO POR PA/AGÊNCIA (TOP 10)</td>';
echo '</tr>';

echo '<tr style="background-color: #e9ecef; font-weight: bold;">';
echo '<td colspan="4" style="padding: 8px; text-align: center;">PA/Agência</td>';
echo '<td colspan="2" style="padding: 8px; text-align: center;">Colaboradores</td>';
echo '<td colspan="2" style="padding: 8px; text-align: center;">% do Total</td>';
echo '</tr>';

$contador_pa = 0;
foreach ($pas_stats as $pa => $quantidade) {
    if ($contador_pa >= 10) break;
    $contador_pa++;
    
    $percentual = ($quantidade / $estatisticas['total_colaboradores']) * 100;
    
    echo '<tr>';
    echo '<td colspan="4" style="padding: 6px;">' . htmlspecialchars($pa) . '</td>';
    echo '<td colspan="2" style="padding: 6px; text-align: center; font-weight: bold;">' . $quantidade . '</td>';
    echo '<td colspan="2" style="padding: 6px; text-align: center;">' . number_format($percentual, 1) . '%</td>';
    echo '</tr>';
}

echo '<tr><td colspan="8" style="padding: 10px;"></td></tr>'; // Espaçamento

// SEÇÃO 5: ALERTAS E RECOMENDAÇÕES
echo '<tr style="background-color: #dc3545; color: white; font-weight: bold;">';
echo '<td colspan="8" style="padding: 12px; text-align: center; font-size: 16px;">⚠️ ALERTAS E RECOMENDAÇÕES</td>';
echo '</tr>';

$alertas = [];

if ($colaboradores_vencidos > 0) {
    $alertas[] = "🔴 {$colaboradores_vencidos} colaboradores com cursos vencidos ({$total_cursos_vencidos} cursos) - AÇÃO IMEDIATA NECESSÁRIA";
}

if ($colaboradores_a_vencer > 0) {
    $alertas[] = "🟡 {$colaboradores_a_vencer} colaboradores com cursos a vencer ({$total_cursos_a_vencer} cursos) - MONITORAR PROXIMAMENTE";
}

if ($taxa_aprovacao < 70) {
    $alertas[] = "📉 Taxa de aprovação baixa (" . number_format($taxa_aprovacao, 1) . "%) - REVISAR ESTRATÉGIAS DE CAPACITAÇÃO";
}

// Trilhas com performance crítica
foreach ($trilhas_stats as $trilha) {
    if ($trilha['percentual_aprovacao'] < 40) {
        $alertas[] = "🎯 Trilha '{$trilha['trilha']}' com performance crítica (" . number_format($trilha['percentual_aprovacao'], 1) . "% aprovação)";
    }
}

if (empty($alertas)) {
    $alertas[] = "✅ Nenhum alerta crítico identificado - Sistema funcionando dentro dos parâmetros esperados";
}

foreach ($alertas as $alerta) {
    echo '<tr>';
    echo '<td colspan="8" style="padding: 8px; background-color: #fff3cd; border-left: 4px solid #ffc107;">' . $alerta . '</td>';
    echo '</tr>';
}

echo '</table>';
?>
