<?php
/**
 * Classe para integração com a API da Intranet Sicoob
 * 
 * Esta classe gerencia as conexões com a API da Intranet para buscar
 * dados de usuários e agências, incluindo sistema de cache para otimização.
 */
class IntranetAPI {
    
    private $apiUrl;
    private $apiUser;
    private $apiToken;
    private $cacheTime;
    private $cachePath;
    private $logPath;
    
    public function __construct() {
        $this->apiUrl = EDU_API_URL;
        $this->apiUser = EDU_API_USER;
        $this->apiToken = EDU_API_TOKEN;
        $this->cacheTime = EDU_API_CACHE_TIME;
        $this->cachePath = EDU_API_CACHE_PATH;
        $this->logPath = EDU_LOG_PATH;
    }
    
    /**
     * Executa uma chamada para a API da Intranet
     * 
     * @param string $module Módulo da API (ex: Usuarios, Agencias)
     * @param string $action Ação da API (ex: listarUsuarios, listarAgencias)
     * @param array $extraFields Campos adicionais para a requisição
     * @return array|false Dados retornados pela API ou false em caso de erro
     */
    private function callAPI($module, $action, $extraFields = []) {
        try {
            // Preparar campos da API
            $apiFields = array_merge([
                'api_user' => $this->apiUser,
                'api_token' => $this->apiToken,
                'api_module' => $module,
                'api_action' => $action
            ], $extraFields);
            
            // Log da requisição
            $this->logAPICall($module, $action, $apiFields);
            
            // Configurar cURL
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => $this->apiUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => http_build_query($apiFields),
                CURLOPT_TIMEOUT => 30,
                CURLOPT_CONNECTTIMEOUT => 10,
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/x-www-form-urlencoded',
                    'User-Agent: SicoobEducacaoCorporativa/1.0'
                ]
            ));
            
            // Executar requisição
            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $curlError = curl_error($curl);
            curl_close($curl);
            
            // Verificar erros de cURL
            if ($curlError) {
                throw new Exception("Erro cURL: " . $curlError);
            }
            
            // Verificar código HTTP
            if ($httpCode !== 200) {
                throw new Exception("Erro HTTP: " . $httpCode);
            }
            
            // Decodificar resposta JSON
            $data = json_decode($response, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("Erro ao decodificar JSON: " . json_last_error_msg());
            }
            
            // Log da resposta
            $this->logAPIResponse($module, $action, $data, true);
            
            return $data;
            
        } catch (Exception $e) {
            // Log do erro
            $this->logAPIResponse($module, $action, ['error' => $e->getMessage()], false);
            
            if (EDU_DEBUG_MODE) {
                error_log("Erro na API Intranet ($module/$action): " . $e->getMessage());
            }
            
            return false;
        }
    }
    
    /**
     * Busca dados do cache se disponível e válido
     * 
     * @param string $cacheKey Chave do cache
     * @return array|false Dados do cache ou false se não encontrado/expirado
     */
    private function getFromCache($cacheKey) {
        $cacheFile = $this->cachePath . $cacheKey . '.json';
        
        if (!file_exists($cacheFile)) {
            return false;
        }
        
        $cacheData = json_decode(file_get_contents($cacheFile), true);
        
        if (!$cacheData || !isset($cacheData['timestamp']) || !isset($cacheData['data'])) {
            return false;
        }
        
        // Verificar se o cache ainda é válido
        if ((time() - $cacheData['timestamp']) > $this->cacheTime) {
            unlink($cacheFile); // Remove cache expirado
            return false;
        }
        
        return $cacheData['data'];
    }
    
    /**
     * Salva dados no cache
     * 
     * @param string $cacheKey Chave do cache
     * @param array $data Dados para salvar
     * @return bool Sucesso da operação
     */
    private function saveToCache($cacheKey, $data) {
        try {
            $cacheFile = $this->cachePath . $cacheKey . '.json';
            $cacheData = [
                'timestamp' => time(),
                'data' => $data
            ];
            
            return file_put_contents($cacheFile, json_encode($cacheData)) !== false;
            
        } catch (Exception $e) {
            if (EDU_DEBUG_MODE) {
                error_log("Erro ao salvar cache: " . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Lista todos os usuários da Intranet
     *
     * @param bool $useCache Se deve usar cache (padrão: true)
     * @param bool $apenasAtivos Se deve retornar apenas usuários ativos (padrão: false)
     * @return array|false Lista de usuários ou false em caso de erro
     */
    public function listarUsuarios($useCache = true, $apenasAtivos = false) {
        $cacheKey = $apenasAtivos ? 'usuarios_intranet_ativos' : 'usuarios_intranet';

        // Tentar buscar do cache primeiro
        if ($useCache) {
            $cachedData = $this->getFromCache($cacheKey);
            if ($cachedData !== false) {
                return $cachedData;
            }
        }

        // Buscar da API
        $data = $this->callAPI('Usuarios', 'listarUsuarios');

        // Processar dados dos usuários para adicionar foto_url e filtrar ativos
        if ($data !== false && is_array($data)) {
            $usuarios_processados = [];

            foreach ($data as $usuario) {
                // Filtrar apenas usuários ativos se solicitado
                if ($apenasAtivos && (!isset($usuario['status']) || $usuario['status'] != 1)) {
                    continue;
                }

                // Adicionar URL da foto se existir
                if (!empty($usuario['foto'])) {
                    $usuario['foto_url'] = 'https://intranet.sicoobcredilivre.com.br/sys/conteudo/usuarios/' . $usuario['foto'];
                }

                // Normalizar campos para compatibilidade
                $usuario['nome_completo'] = $usuario['nome'] ?? '';
                $usuario['setor_nome'] = $usuario['nomeSetor'] ?? '';
                $usuario['funcao_nome'] = $usuario['nomeFuncao'] ?? '';

                // Adicionar informações de status de bloqueio formatadas
                $usuario['status_bloqueio'] = $this->obterStatusBloqueio($usuario);

                $usuarios_processados[] = $usuario;
            }

            $data = $usuarios_processados;
        }

        if ($data !== false && $useCache) {
            $this->saveToCache($cacheKey, $data);
        }

        return $data;
    }
    
    /**
     * Lista apenas os usuários ativos da Intranet
     *
     * @param bool $useCache Se deve usar cache (padrão: true)
     * @return array|false Lista de usuários ativos ou false em caso de erro
     */
    public function listarUsuariosAtivos($useCache = true) {
        return $this->listarUsuarios($useCache, true);
    }

    /**
     * Lista todas as agências da Intranet
     *
     * @param bool $useCache Se deve usar cache (padrão: true)
     * @return array|false Lista de agências ou false em caso de erro
     */
    public function listarAgencias($useCache = true) {
        $cacheKey = 'agencias_intranet';
        
        // Tentar buscar do cache primeiro
        if ($useCache) {
            $cachedData = $this->getFromCache($cacheKey);
            if ($cachedData !== false) {
                return $cachedData;
            }
        }
        
        // Buscar da API
        $data = $this->callAPI('Agencias', 'listarAgencias');
        
        if ($data !== false && $useCache) {
            $this->saveToCache($cacheKey, $data);
        }
        
        return $data;
    }
    
    /**
     * Busca um usuário específico por email
     * 
     * @param string $email Email do usuário
     * @return array|false Dados do usuário ou false se não encontrado
     */
    public function buscarUsuarioPorEmail($email) {
        $usuarios = $this->listarUsuarios();
        
        if ($usuarios === false) {
            return false;
        }
        
        foreach ($usuarios as $usuario) {
            if (isset($usuario['email']) && strtolower($usuario['email']) === strtolower($email)) {
                return $usuario;
            }
        }
        
        return false;
    }
    
    /**
     * Busca uma agência específica por número
     *
     * @param string $numero Número da agência
     * @return array|false Dados da agência ou false se não encontrada
     */
    public function buscarAgenciaPorNumero($numero) {
        $agencias = $this->listarAgencias();

        if ($agencias === false) {
            return false;
        }

        foreach ($agencias as $agencia) {
            if (isset($agencia['numero']) && $agencia['numero'] == $numero) {
                return $agencia;
            }
        }

        return false;
    }

    /**
     * Busca um usuário específico por CPF
     *
     * @param string $cpf CPF do usuário (com ou sem formatação)
     * @param bool $apenasAtivos Se deve buscar apenas entre usuários ativos (padrão: false)
     * @return array|false Dados do usuário ou false se não encontrado
     */
    public function buscarUsuarioPorCpf($cpf, $apenasAtivos = false) {
        $usuarios = $this->listarUsuarios(true, $apenasAtivos);

        if ($usuarios === false) {
            return false;
        }

        // Normalizar CPF de busca
        $cpf_busca = str_pad(preg_replace('/[^0-9]/', '', $cpf), 11, '0', STR_PAD_LEFT);

        foreach ($usuarios as $usuario) {
            if (isset($usuario['cpf'])) {
                // Normalizar CPF do usuário
                $cpf_usuario = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                if ($cpf_usuario === $cpf_busca) {
                    return $usuario;
                }
            }
        }

        return false;
    }

    /**
     * Obtém o status de bloqueio formatado de um usuário
     *
     * @param array $usuario Dados do usuário da API
     * @return array Array com informações do status de bloqueio
     */
    private function obterStatusBloqueio($usuario) {
        $bloqueado = $usuario['bloqueado'] ?? 0;

        switch ($bloqueado) {
            case 0:
                return [
                    'codigo' => 0,
                    'texto' => 'Sem Bloqueio',
                    'classe' => 'success',
                    'icone' => 'fa-check-circle',
                    'descricao' => 'Usuário sem restrições de acesso'
                ];
            case 1:
                return [
                    'codigo' => 1,
                    'texto' => 'Bloqueado',
                    'classe' => 'danger',
                    'icone' => 'fa-ban',
                    'descricao' => 'Usuário com acesso bloqueado'
                ];
            case 3:
                return [
                    'codigo' => 3,
                    'texto' => 'Bloqueio Agendado',
                    'classe' => 'warning',
                    'icone' => 'fa-clock',
                    'descricao' => 'Usuário com bloqueio programado'
                ];
            default:
                return [
                    'codigo' => $bloqueado,
                    'texto' => 'Status Desconhecido',
                    'classe' => 'secondary',
                    'icone' => 'fa-question-circle',
                    'descricao' => 'Status de bloqueio não reconhecido'
                ];
        }
    }

    /**
     * Cria um mapa de usuários indexado por CPF para busca rápida
     *
     * @return array Mapa de usuários [cpf => dados_usuario]
     */
    public function criarMapaUsuariosPorCpf() {
        $usuarios = $this->listarUsuarios();
        $mapa = [];

        if ($usuarios !== false) {
            foreach ($usuarios as $usuario) {
                if (!empty($usuario['cpf'])) {
                    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                    $mapa[$cpf_normalizado] = $usuario;
                }
            }
        }

        return $mapa;
    }
    
    /**
     * Limpa todo o cache da API
     * 
     * @return bool Sucesso da operação
     */
    public function limparCache() {
        try {
            $files = glob($this->cachePath . '*.json');
            foreach ($files as $file) {
                unlink($file);
            }
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Registra log de chamada da API
     */
    private function logAPICall($module, $action, $fields) {
        if (!EDU_DEBUG_MODE) return;
        
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'type' => 'API_CALL',
            'module' => $module,
            'action' => $action,
            'fields' => array_keys($fields) // Não logar valores sensíveis
        ];
        
        $this->writeLog('api_calls', $logData);
    }
    
    /**
     * Registra log de resposta da API
     */
    private function logAPIResponse($module, $action, $data, $success) {
        if (!EDU_DEBUG_MODE) return;
        
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'type' => 'API_RESPONSE',
            'module' => $module,
            'action' => $action,
            'success' => $success,
            'data_size' => is_array($data) ? count($data) : 0
        ];
        
        if (!$success) {
            $logData['error'] = $data;
        }
        
        $this->writeLog('api_responses', $logData);
    }
    
    /**
     * Escreve log em arquivo
     */
    private function writeLog($type, $data) {
        try {
            $logFile = $this->logPath . $type . '_' . date('Y-m-d') . '.log';
            $logLine = json_encode($data) . "\n";
            file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
        } catch (Exception $e) {
            // Falha silenciosa no log
        }
    }
}
?>
