# Sistema de Prazos Personalizados

## 🎯 **Visão Geral**

O Sistema de Prazos Personalizados permite configurar prazos específicos para cursos individuais, sobrescrevendo os prazos padrão do relatório importado.

## 🏗️ **Estrutura do Sistema**

### **Tabelas Criadas:**

1. **`edu_prazos_personalizados`** - Configurações de prazos
2. **`edu_log_prazos`** - Log de alterações

### **Arquivos Principais:**

- `gerenciar_trilhas.php` - Interface de gerenciamento
- `prazo_calculator.php` - Funções de cálculo
- `sql/create_prazos_personalizados.sql` - Scripts de criação

## 📋 **Como Funciona**

### **Lógica de Prazos:**

1. **Prazo Padrão** (sem personalização):
   - Usa a coluna `concluir_trilha_ate` do relatório importado

2. **Prazo Personalizado** (com personalização ativa):
   - **Primeiro Prazo**: Para colaboradores que nunca fizeram o curso
     - Base: `data_admissao` + X dias
   - **Renovação**: Para colaboradores que já concluíram o curso
     - Base: última `data_conclusao` + Y dias

### **Exemplo Prático:**

```
Curso: "Segurança da Informação"
- Primeiro Prazo: 90 dias
- Renovação: 365 dias

Colaborador A (nunca fez):
- Admissão: 01/01/2024
- Prazo final: 01/04/2024 (01/01 + 90 dias)

Colaborador B (já concluiu em 15/06/2024):
- Última conclusão: 15/06/2024
- Prazo final: 15/06/2025 (15/06 + 365 dias)
```

## 🎛️ **Interface de Gerenciamento**

### **Página: `gerenciar_trilhas.php`**

#### **Funcionalidades:**
- ✅ Lista trilhas agrupadas com seus cursos
- ✅ Switch para ativar/desativar prazo personalizado
- ✅ Campos para configurar prazos (dias)
- ✅ Feedback visual do status
- ✅ Log de alterações

#### **Permissões:**
- **Acesso**: Apenas gestores e administradores
- **Verificação**: `checkPageAccess('gestor')`

### **Elementos da Interface:**

1. **Switch de Ativação**:
   - Liga/desliga prazo personalizado por curso
   - Atualização automática via formulário

2. **Configuração de Prazos**:
   - Campo "Primeiro Prazo" (dias)
   - Campo "Renovação" (dias)
   - Botão "Salvar Prazos"

3. **Feedback Visual**:
   - Badge "Ativo" (verde) / "Padrão" (cinza)
   - Destaque visual para cursos com prazo personalizado
   - Data da última configuração

## 🔧 **Funções de Cálculo**

### **`calcularPrazoPersonalizado()`**

```php
calcularPrazoPersonalizado(
    $cpf,                    // CPF do colaborador
    $codigo_trilha,          // Código da trilha
    $codigo_recurso,         // Código do curso
    $data_admissao,          // Data de admissão
    $concluir_trilha_ate,    // Prazo padrão
    $pdo                     // Conexão BD
)
```

**Retorna**: Data final calculada ou prazo padrão

### **`atualizarTodosPrazosPersonalizados()`**

Atualiza todos os registros que possuem prazos personalizados ativos.

### **`obterEstatisticasPrazos()`**

Retorna estatísticas sobre o uso de prazos personalizados.

## 📊 **Banco de Dados**

### **Tabela: `edu_prazos_personalizados`**

| Campo | Tipo | Descrição |
|-------|------|-----------|
| `id` | INT | Chave primária |
| `codigo_trilha` | VARCHAR(50) | Código da trilha |
| `trilha` | VARCHAR(255) | Nome da trilha |
| `codigo_recurso` | VARCHAR(50) | Código do curso |
| `recurso` | VARCHAR(255) | Nome do curso |
| `prazo_personalizado_ativo` | BOOLEAN | Se está ativo |
| `primeiro_prazo_dias` | INT | Dias para primeiro prazo |
| `renovacao_prazo_dias` | INT | Dias para renovação |
| `usuario_criacao` | INT | Quem criou |
| `data_criacao` | TIMESTAMP | Quando criou |
| `usuario_atualizacao` | INT | Quem atualizou |
| `data_atualizacao` | TIMESTAMP | Quando atualizou |

### **Tabela: `edu_log_prazos`**

| Campo | Tipo | Descrição |
|-------|------|-----------|
| `id` | INT | Chave primária |
| `prazo_id` | INT | ID do prazo alterado |
| `acao` | ENUM | Tipo de ação |
| `valores_anteriores` | JSON | Valores antes |
| `valores_novos` | JSON | Valores depois |
| `usuario_id` | INT | Quem fez a alteração |
| `data_acao` | TIMESTAMP | Quando foi feito |

## 🔄 **Fluxo de Trabalho**

### **1. Configuração Inicial:**
1. Importar relatório com dados
2. Acessar "Gerenciar Trilhas"
3. Ativar prazo personalizado para cursos desejados
4. Configurar "Primeiro Prazo" e "Renovação"

### **2. Cálculo Automático:**
1. Sistema verifica se curso tem prazo personalizado
2. Consulta histórico do colaborador
3. Aplica regra apropriada (primeiro prazo ou renovação)
4. Calcula data final baseada na configuração

### **3. Monitoramento:**
1. Log de todas as alterações
2. Estatísticas de uso
3. Feedback visual na interface

## 📈 **Benefícios**

### **Para Gestores:**
- ✅ Controle granular sobre prazos
- ✅ Flexibilidade por curso específico
- ✅ Histórico de alterações
- ✅ Interface intuitiva

### **Para o Sistema:**
- ✅ Cálculos automáticos
- ✅ Integração com dados existentes
- ✅ Logs de auditoria
- ✅ Performance otimizada

### **Para Colaboradores:**
- ✅ Prazos mais realistas
- ✅ Diferenciação entre primeiro acesso e renovação
- ✅ Baseado em datas reais (admissão/conclusão)

## 🛠️ **Manutenção**

### **Backup Recomendado:**
```sql
-- Backup das configurações
SELECT * FROM edu_prazos_personalizados;

-- Backup dos logs
SELECT * FROM edu_log_prazos;
```

### **Limpeza de Logs:**
```sql
-- Remover logs antigos (mais de 1 ano)
DELETE FROM edu_log_prazos 
WHERE data_acao < DATE_SUB(NOW(), INTERVAL 1 YEAR);
```

## 🎯 **Casos de Uso**

### **Exemplo 1: Curso de Segurança**
- **Primeiro Prazo**: 30 dias (urgente para novos funcionários)
- **Renovação**: 365 dias (anual para quem já fez)

### **Exemplo 2: Treinamento Técnico**
- **Primeiro Prazo**: 90 dias (tempo para adaptação)
- **Renovação**: 180 dias (atualização semestral)

### **Exemplo 3: Compliance**
- **Primeiro Prazo**: 15 dias (obrigatório imediato)
- **Renovação**: 90 dias (revisão trimestral)

## 🔐 **Segurança**

- ✅ Controle de acesso por permissão
- ✅ Log de todas as alterações
- ✅ Validação de dados de entrada
- ✅ Transações de banco para consistência
- ✅ Tratamento de erros robusto

---

**Sistema implementado e pronto para uso!** 🚀
