<?php
// Funções auxiliares para processamento de dados

// Função para tratar CPF - adicionar zeros à esquerda se necessário
function formatCpf($cpf) {
    if (empty($cpf)) {
        return '';
    }
    
    // Limpar caracteres não numéricos
    $cleaned = preg_replace('/[^0-9]/', '', $cpf);
    
    if (empty($cleaned)) {
        return '';
    }
    
    // Completar com zeros à esquerda até 11 dígitos para CPF
    return str_pad($cleaned, 11, '0', STR_PAD_LEFT);
}

// Função para converter data do formato brasileiro para MySQL
function convertDate($date) {
    if (empty($date)) {
        return null;
    }
    
    // Tentar diferentes formatos de data
    $formats = ['d/m/Y', 'd-m-Y', 'Y-m-d'];
    
    foreach ($formats as $format) {
        $dateObj = DateTime::createFromFormat($format, $date);
        if ($dateObj !== false) {
            return $dateObj->format('Y-m-d');
        }
    }
    
    return null;
}

// Função para limpar valores numéricos
function cleanNumericValue($value) {
    if (empty($value)) {
        return null;
    }
    
    // Remover caracteres não numéricos exceto vírgula e ponto
    $cleaned = preg_replace('/[^0-9,.]/', '', $value);
    
    // Converter vírgula para ponto (formato brasileiro)
    $cleaned = str_replace(',', '.', $cleaned);
    
    return is_numeric($cleaned) ? floatval($cleaned) : null;
}

// Função para tratar campos de carga horária no formato hh:mm
function cleanTimeValue($value) {
    if (empty($value)) {
        return null;
    }
    
    $value = trim($value);
    
    // Se já está no formato hh:mm, manter
    if (preg_match('/^\d{1,2}:\d{2}$/', $value)) {
        return $value;
    }
    
    // Se é um número como "1025", converter para "10:25"
    if (preg_match('/^\d{3,4}$/', $value)) {
        $hours = substr($value, 0, -2);
        $minutes = substr($value, -2);
        return $hours . ':' . $minutes;
    }
    
    // Se é um número decimal, converter minutos totais para hh:mm
    if (is_numeric($value)) {
        $totalMinutes = intval($value);
        $hours = intval($totalMinutes / 60);
        $minutes = $totalMinutes % 60;
        return sprintf('%d:%02d', $hours, $minutes);
    }
    
    return $value;
}

// Função para detectar e converter codificação
function detectAndConvertEncoding($text) {
    if (empty($text)) {
        return $text;
    }
    
    // Lista de codificações para testar
    $encodings = ['UTF-8', 'ISO-8859-1', 'Windows-1252', 'CP1252'];
    
    // Detectar codificação
    $detected = mb_detect_encoding($text, $encodings, true);
    
    // Se não for UTF-8, converter
    if ($detected && $detected !== 'UTF-8') {
        return mb_convert_encoding($text, 'UTF-8', $detected);
    }
    
    return $text;
}

// Função para processar linha do CSV com tratamento de codificação
function processCSVLine($line) {
    if (is_array($line)) {
        return array_map('detectAndConvertEncoding', $line);
    }
    return detectAndConvertEncoding($line);
}
?>
