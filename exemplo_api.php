<?php
/**
 * Exemplo de uso da API da Intranet
 * 
 * Este arquivo demonstra como usar a classe IntranetAPI
 * para buscar dados de usuários e agências.
 */

require_once 'config/config.php';
require_once 'classes/IntranetAPI.php';

// Criar instância da API
$api = new IntranetAPI();

echo "<h1>Exemplo de Uso da API da Intranet</h1>\n";

// Exemplo 1: Listar todos os usuários
echo "<h2>1. Listar Usuários</h2>\n";
$usuarios = $api->listarUsuarios();

if ($usuarios !== false) {
    echo "<p>Total de usuários encontrados: " . count($usuarios) . "</p>\n";
    
    // Mostrar os primeiros 3 usuários como exemplo
    echo "<h3>Primeiros 3 usuários:</h3>\n";
    for ($i = 0; $i < min(3, count($usuarios)); $i++) {
        $usuario = $usuarios[$i];
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px;'>\n";
        echo "<strong>Nome:</strong> " . htmlspecialchars($usuario['nome'] ?? 'N/A') . "<br>\n";
        echo "<strong>Email:</strong> " . htmlspecialchars($usuario['email'] ?? 'N/A') . "<br>\n";
        echo "<strong>Agência:</strong> " . htmlspecialchars($usuario['agencia'] ?? 'N/A') . "<br>\n";
        echo "</div>\n";
    }
} else {
    echo "<p style='color: red;'>Erro ao buscar usuários da API.</p>\n";
}

// Exemplo 2: Listar todas as agências
echo "<h2>2. Listar Agências</h2>\n";
$agencias = $api->listarAgencias();

if ($agencias !== false) {
    echo "<p>Total de agências encontradas: " . count($agencias) . "</p>\n";
    
    // Mostrar as primeiras 3 agências como exemplo
    echo "<h3>Primeiras 3 agências:</h3>\n";
    for ($i = 0; $i < min(3, count($agencias)); $i++) {
        $agencia = $agencias[$i];
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px;'>\n";
        echo "<strong>Número:</strong> " . htmlspecialchars($agencia['numero'] ?? 'N/A') . "<br>\n";
        echo "<strong>Nome:</strong> " . htmlspecialchars($agencia['nome'] ?? 'N/A') . "<br>\n";
        echo "<strong>ID:</strong> " . htmlspecialchars($agencia['id'] ?? 'N/A') . "<br>\n";
        echo "</div>\n";
    }
} else {
    echo "<p style='color: red;'>Erro ao buscar agências da API.</p>\n";
}

// Exemplo 3: Buscar usuário por email
echo "<h2>3. Buscar Usuário por Email</h2>\n";
$email_exemplo = '<EMAIL>'; // Substitua por um email real para teste
$usuario = $api->buscarUsuarioPorEmail($email_exemplo);

if ($usuario !== false) {
    echo "<p>Usuário encontrado para o email: $email_exemplo</p>\n";
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px;'>\n";
    echo "<strong>Nome:</strong> " . htmlspecialchars($usuario['nome'] ?? 'N/A') . "<br>\n";
    echo "<strong>Email:</strong> " . htmlspecialchars($usuario['email'] ?? 'N/A') . "<br>\n";
    echo "<strong>Agência:</strong> " . htmlspecialchars($usuario['agencia'] ?? 'N/A') . "<br>\n";
    echo "</div>\n";
} else {
    echo "<p style='color: orange;'>Usuário não encontrado para o email: $email_exemplo</p>\n";
}

// Exemplo 4: Buscar agência por número
echo "<h2>4. Buscar Agência por Número</h2>\n";
$numero_exemplo = '001'; // Substitua por um número real para teste
$agencia = $api->buscarAgenciaPorNumero($numero_exemplo);

if ($agencia !== false) {
    echo "<p>Agência encontrada para o número: $numero_exemplo</p>\n";
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px;'>\n";
    echo "<strong>Número:</strong> " . htmlspecialchars($agencia['numero'] ?? 'N/A') . "<br>\n";
    echo "<strong>Nome:</strong> " . htmlspecialchars($agencia['nome'] ?? 'N/A') . "<br>\n";
    echo "<strong>ID:</strong> " . htmlspecialchars($agencia['id'] ?? 'N/A') . "<br>\n";
    echo "</div>\n";
} else {
    echo "<p style='color: orange;'>Agência não encontrada para o número: $numero_exemplo</p>\n";
}

// Exemplo 5: Teste de performance com cache
echo "<h2>5. Teste de Performance (Cache)</h2>\n";

// Primeira chamada (sem cache)
$start_time = microtime(true);
$usuarios_sem_cache = $api->listarUsuarios(false);
$time_sem_cache = microtime(true) - $start_time;

// Segunda chamada (com cache)
$start_time = microtime(true);
$usuarios_com_cache = $api->listarUsuarios(true);
$time_com_cache = microtime(true) - $start_time;

echo "<p><strong>Tempo sem cache:</strong> " . round($time_sem_cache * 1000, 2) . "ms</p>\n";
echo "<p><strong>Tempo com cache:</strong> " . round($time_com_cache * 1000, 2) . "ms</p>\n";
echo "<p><strong>Melhoria:</strong> " . round((($time_sem_cache - $time_com_cache) / $time_sem_cache) * 100, 1) . "%</p>\n";

// Exemplo 6: Informações de configuração
echo "<h2>6. Configurações da API</h2>\n";
echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>\n";
echo "<strong>URL da API:</strong> " . EDU_API_URL . "<br>\n";
echo "<strong>Usuário da API:</strong> " . EDU_API_USER . "<br>\n";
echo "<strong>Token da API:</strong> " . substr(EDU_API_TOKEN, 0, 10) . "...<br>\n";
echo "<strong>Tempo de Cache:</strong> " . EDU_API_CACHE_TIME . " segundos<br>\n";
echo "<strong>Diretório de Cache:</strong> " . EDU_API_CACHE_PATH . "<br>\n";
echo "<strong>Debug Mode:</strong> " . (EDU_DEBUG_MODE ? 'Ativo' : 'Inativo') . "<br>\n";
echo "</div>\n";

echo "<h2>7. Como Usar em Seus Projetos</h2>\n";
echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>\n";
echo htmlspecialchars('<?php
// Incluir a classe
require_once \'classes/IntranetAPI.php\';

// Criar instância
$api = new IntranetAPI();

// Buscar todos os usuários
$usuarios = $api->listarUsuarios();

// Buscar todas as agências
$agencias = $api->listarAgencias();

// Buscar usuário específico
$usuario = $api->buscarUsuarioPorEmail(\'<EMAIL>\');

// Buscar agência específica
$agencia = $api->buscarAgenciaPorNumero(\'001\');

// Limpar cache se necessário
$api->limparCache();
?>');
echo "</pre>\n";

echo "<p><em>Exemplo executado em: " . date('d/m/Y H:i:s') . "</em></p>\n";
?>
