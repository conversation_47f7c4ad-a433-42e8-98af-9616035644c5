# Exibição de Carga Horária dos Cursos

## 🎯 **Funcionalidade Implementada**

Adicionada a exibição da carga horária dos cursos na página de gerenciamento de trilhas, proporcionando informações mais completas sobre cada curso.

## 📊 **Informações Exibidas**

### **Para Cada Curso:**
1. **Nome do Curso** - Título principal
2. **Código do Recurso** - Identificador único
3. **Carga Horária** - Duração do curso (formato hh:mm)
4. **Status do Prazo** - Personalizado ou Padrão

### **Layout Organizado:**
- **Coluna 1 (5/12)**: Nome e informações básicas
- **Coluna 2 (2/12)**: Badge de carga horária destacado
- **Coluna 3 (3/12)**: Controle de prazo personalizado
- **Coluna 4 (2/12)**: Status visual

## 🎨 **Design e Apresentação**

### **Carga Horária Destacada:**
```html
<div class="badge carga-horaria-badge">
    <i class="fas fa-hourglass-half me-1"></i>
    10:25
</div>
<div class="small text-muted">Carga Horária</div>
```

### **Informações Inline:**
```html
<div class="curso-info-grid">
    <div class="info-item text-muted">
        <i class="fas fa-code"></i>
        <span>REC001</span>
    </div>
    <div class="info-item carga-horaria-info">
        <i class="fas fa-clock"></i>
        <span>10:25</span>
    </div>
</div>
```

### **Estilos CSS Aplicados:**
```css
.carga-horaria-badge {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #1565c0;
    border: 1px solid #90caf9;
    font-weight: 600;
}

.carga-horaria-info {
    color: var(--sicoob-turquesa);
    font-weight: 600;
}

.curso-info-grid {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}
```

## 🔍 **Tratamento de Dados**

### **Consulta SQL Atualizada:**
```sql
SELECT DISTINCT 
    codigo_trilha, 
    trilha, 
    codigo_recurso, 
    recurso,
    carga_horaria_recurso
FROM edu_relatorio_educacao 
WHERE codigo_trilha IS NOT NULL 
AND codigo_recurso IS NOT NULL 
ORDER BY trilha, recurso
```

### **Estrutura de Dados:**
```php
$trilhas[$trilha_nome]['cursos'][] = [
    'codigo_recurso' => $curso['codigo_recurso'],
    'nome' => $curso['recurso'],
    'carga_horaria' => $curso['carga_horaria_recurso'],
    'config' => $config
];
```

## 📋 **Casos de Exibição**

### **1. Curso com Carga Horária Informada:**
```
📚 Segurança da Informação
🔢 REC001  🕐 08:30

[Badge: 08:30] [Switch: Prazo Personalizado] [Badge: Ativo]
```

### **2. Curso sem Carga Horária:**
```
📚 Curso Básico
🔢 REC002

[Badge: Não informado] [Switch: Prazo Personalizado] [Badge: Padrão]
```

### **3. Diferentes Formatos de Carga Horária:**
- **Formato padrão**: `10:25` (10 horas e 25 minutos)
- **Cursos longos**: `40:00` (40 horas)
- **Cursos curtos**: `02:30` (2 horas e 30 minutos)

## 🎯 **Benefícios da Implementação**

### **Para Gestores:**
- ✅ **Visão completa** dos cursos e suas durações
- ✅ **Planejamento facilitado** de cronogramas de treinamento
- ✅ **Comparação rápida** entre cursos de diferentes trilhas
- ✅ **Informação centralizada** em uma única tela

### **Para Administradores:**
- ✅ **Gestão eficiente** de recursos de treinamento
- ✅ **Identificação rápida** de cursos por duração
- ✅ **Configuração informada** de prazos personalizados
- ✅ **Visão estratégica** do portfólio de cursos

### **Para o Sistema:**
- ✅ **Interface mais informativa** e profissional
- ✅ **Dados relevantes** sempre visíveis
- ✅ **Layout organizado** e responsivo
- ✅ **Experiência de usuário** aprimorada

## 📱 **Responsividade**

### **Desktop:**
- Layout em 4 colunas bem distribuídas
- Badges destacados e legíveis
- Informações organizadas horizontalmente

### **Tablet:**
- Colunas se ajustam automaticamente
- Badges mantêm visibilidade
- Layout flexível preservado

### **Mobile:**
- Informações empilhadas verticalmente
- Badges redimensionados apropriadamente
- Navegação touch-friendly mantida

## 🔧 **Detalhes Técnicos**

### **Consulta Otimizada:**
- `DISTINCT` para evitar duplicatas
- `ORDER BY` para organização consistente
- Inclusão da coluna `carga_horaria_recurso`

### **Tratamento de Dados:**
- Verificação de valores vazios/nulos
- Exibição condicional de badges
- Formatação preservada do banco

### **CSS Responsivo:**
- Flexbox para layout adaptável
- Classes utilitárias do Bootstrap
- Estilos customizados para destaque

## 📊 **Exemplos Visuais**

### **Curso Completo:**
```
┌─────────────────────────────────────────────────────────────┐
│ 📚 Segurança da Informação                                  │
│ 🔢 REC001  🕐 08:30                                         │
│                                                             │
│ [08:30] [⚙️ Prazo Personalizado: ✓] [✅ Ativo]              │
│ Carga   Switch                      Status                  │
│ Horária                                                     │
└─────────────────────────────────────────────────────────────┘
```

### **Curso sem Carga Horária:**
```
┌─────────────────────────────────────────────────────────────┐
│ 📚 Curso Básico                                             │
│ 🔢 REC002                                                   │
│                                                             │
│ [❓ Não informado] [⚙️ Prazo Personalizado: ✗] [⏰ Padrão]  │
│ Carga Horária      Switch                      Status       │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 **Resultado Final**

### **Informações Agora Disponíveis:**
- ✅ Nome completo do curso
- ✅ Código de identificação
- ✅ **Carga horária destacada**
- ✅ Status de prazo personalizado
- ✅ Controles de configuração

### **Interface Aprimorada:**
- ✅ Layout mais informativo
- ✅ Design profissional e organizado
- ✅ Badges visuais destacados
- ✅ Responsividade mantida
- ✅ Experiência de usuário superior

**A exibição da carga horária torna o sistema mais completo e informativo para gestão de trilhas e cursos!** 🎉
