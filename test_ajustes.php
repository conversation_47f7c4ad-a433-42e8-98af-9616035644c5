<?php
/**
 * Teste dos Ajustes - Abas, Métricas e Filtros
 * 
 * Este arquivo testa os ajustes realizados na página de análise.
 */

require_once 'config/config.php';
require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'classes/IntranetAPI.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

echo "<h1>🧪 Teste dos Ajustes Realizados</h1>";

// Teste 1: Verificar consulta de colaboradores com novas métricas
echo "<h2>1. 👥 Teste das Novas Métricas de Colaboradores</h2>";

try {
    $query_colaboradores_teste = "
        SELECT 
            cpf,
            usuario,
            COUNT(DISTINCT trilha) as total_trilhas,
            COUNT(DISTINCT recurso) as total_cursos,
            SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as cursos_aprovados,
            COUNT(CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00' THEN 1 END) as cursos_concluidos,
            COUNT(CASE WHEN validade_recurso IS NOT NULL AND validade_recurso != '0000-00-00' AND validade_recurso <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND validade_recurso >= CURDATE() THEN 1 END) as cursos_a_vencer,
            COUNT(CASE WHEN validade_recurso IS NOT NULL AND validade_recurso != '0000-00-00' AND validade_recurso < CURDATE() THEN 1 END) as cursos_vencidos
        FROM edu_relatorio_educacao
        GROUP BY cpf, usuario
        LIMIT 3
    ";
    
    $stmt = $pdo_edu->prepare($query_colaboradores_teste);
    $stmt->execute();
    $colaboradores_teste = $stmt->fetchAll();
    
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>✅ <strong>Novas Métricas de Colaboradores:</strong></p>";
    
    if (!empty($colaboradores_teste)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Colaborador</th>";
        echo "<th style='padding: 8px;'>Atribuídos</th>";
        echo "<th style='padding: 8px;'>Concluídos</th>";
        echo "<th style='padding: 8px;'>A Vencer</th>";
        echo "<th style='padding: 8px;'>Vencidos</th>";
        echo "</tr>";
        
        foreach ($colaboradores_teste as $colaborador) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($colaborador['usuario']) . "</td>";
            echo "<td style='padding: 8px; text-align: center;'>" . $colaborador['total_cursos'] . "</td>";
            echo "<td style='padding: 8px; text-align: center;'>" . $colaborador['cursos_concluidos'] . "</td>";
            echo "<td style='padding: 8px; text-align: center;'>" . $colaborador['cursos_a_vencer'] . "</td>";
            echo "<td style='padding: 8px; text-align: center;'>" . $colaborador['cursos_vencidos'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Nenhum colaborador encontrado para teste.</p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro nas métricas de colaboradores:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 2: Verificar consulta de cursos
echo "<h2>2. 🎓 Teste da Consulta de Cursos</h2>";

try {
    $query_cursos_teste = "
        SELECT 
            codigo_recurso,
            recurso,
            trilha,
            COUNT(DISTINCT cpf) as total_colaboradores,
            SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as total_aprovados,
            COUNT(CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00' THEN 1 END) as total_concluidos
        FROM edu_relatorio_educacao
        GROUP BY codigo_recurso, recurso, trilha
        LIMIT 5
    ";
    
    $stmt = $pdo_edu->prepare($query_cursos_teste);
    $stmt->execute();
    $cursos_teste = $stmt->fetchAll();
    
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>✅ <strong>Consulta de Cursos Funcionando:</strong></p>";
    
    if (!empty($cursos_teste)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Curso</th>";
        echo "<th style='padding: 8px;'>Trilha</th>";
        echo "<th style='padding: 8px;'>Colaboradores</th>";
        echo "<th style='padding: 8px;'>Aprovados</th>";
        echo "<th style='padding: 8px;'>Concluídos</th>";
        echo "</tr>";
        
        foreach ($cursos_teste as $curso) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($curso['recurso']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($curso['trilha']) . "</td>";
            echo "<td style='padding: 8px; text-align: center;'>" . $curso['total_colaboradores'] . "</td>";
            echo "<td style='padding: 8px; text-align: center;'>" . $curso['total_aprovados'] . "</td>";
            echo "<td style='padding: 8px; text-align: center;'>" . $curso['total_concluidos'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>Total de cursos únicos encontrados:</strong> " . count($cursos_teste) . "</p>";
    } else {
        echo "<p>❌ Nenhum curso encontrado - isso pode indicar um problema na consulta.</p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro na consulta de cursos:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 3: Verificar filtros disponíveis
echo "<h2>3. 🔍 Teste dos Filtros Disponíveis</h2>";

try {
    // Trilhas disponíveis
    $trilhas_query = "SELECT DISTINCT trilha FROM edu_relatorio_educacao WHERE trilha IS NOT NULL AND trilha != '' ORDER BY trilha LIMIT 5";
    $trilhas_disponiveis = $pdo_edu->query($trilhas_query)->fetchAll(PDO::FETCH_COLUMN);
    
    // Cursos disponíveis
    $cursos_query = "SELECT DISTINCT recurso FROM edu_relatorio_educacao WHERE recurso IS NOT NULL AND recurso != '' ORDER BY recurso LIMIT 5";
    $cursos_disponiveis = $pdo_edu->query($cursos_query)->fetchAll(PDO::FETCH_COLUMN);
    
    // Status de aprovação
    $aprovacoes_query = "SELECT DISTINCT aprovacao FROM edu_relatorio_educacao WHERE aprovacao IS NOT NULL AND aprovacao != ''";
    $aprovacoes_disponiveis = $pdo_edu->query($aprovacoes_query)->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>✅ <strong>Filtros Disponíveis:</strong></p>";
    
    echo "<div style='display: flex; gap: 20px; flex-wrap: wrap;'>";
    
    echo "<div style='flex: 1; min-width: 200px;'>";
    echo "<h6>Trilhas (primeiras 5):</h6>";
    echo "<ul>";
    foreach ($trilhas_disponiveis as $trilha) {
        echo "<li>" . htmlspecialchars($trilha) . "</li>";
    }
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='flex: 1; min-width: 200px;'>";
    echo "<h6>Cursos (primeiros 5):</h6>";
    echo "<ul>";
    foreach ($cursos_disponiveis as $curso) {
        echo "<li>" . htmlspecialchars(substr($curso, 0, 50)) . (strlen($curso) > 50 ? '...' : '') . "</li>";
    }
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='flex: 1; min-width: 200px;'>";
    echo "<h6>Status de Aprovação:</h6>";
    echo "<ul>";
    foreach ($aprovacoes_disponiveis as $aprovacao) {
        echo "<li>" . htmlspecialchars($aprovacao) . "</li>";
    }
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro nos filtros:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 4: Verificar cálculo de datas (a vencer e vencidos)
echo "<h2>4. 📅 Teste de Cálculo de Datas</h2>";

try {
    $query_datas = "
        SELECT 
            COUNT(CASE WHEN validade_recurso IS NOT NULL AND validade_recurso != '0000-00-00' AND validade_recurso <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND validade_recurso >= CURDATE() THEN 1 END) as total_a_vencer,
            COUNT(CASE WHEN validade_recurso IS NOT NULL AND validade_recurso != '0000-00-00' AND validade_recurso < CURDATE() THEN 1 END) as total_vencidos,
            COUNT(CASE WHEN validade_recurso IS NOT NULL AND validade_recurso != '0000-00-00' THEN 1 END) as total_com_validade,
            CURDATE() as data_atual,
            DATE_ADD(CURDATE(), INTERVAL 30 DAY) as data_limite_vencimento
        FROM edu_relatorio_educacao
    ";
    
    $stmt = $pdo_edu->prepare($query_datas);
    $stmt->execute();
    $datas_teste = $stmt->fetch();
    
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>✅ <strong>Cálculo de Datas:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Data atual:</strong> " . $datas_teste['data_atual'] . "</li>";
    echo "<li><strong>Data limite (30 dias):</strong> " . $datas_teste['data_limite_vencimento'] . "</li>";
    echo "<li><strong>Total com validade:</strong> " . number_format($datas_teste['total_com_validade']) . "</li>";
    echo "<li><strong>Total a vencer (30 dias):</strong> " . number_format($datas_teste['total_a_vencer']) . "</li>";
    echo "<li><strong>Total vencidos:</strong> " . number_format($datas_teste['total_vencidos']) . "</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro no cálculo de datas:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 5: Verificar URLs das abas
echo "<h2>5. 🔗 Teste das URLs das Abas</h2>";

$urls_teste = [
    'Aba Colaboradores' => 'analise_colaboradores.php?aba=colaboradores',
    'Aba Cursos' => 'analise_colaboradores.php?aba=cursos',
    'Colaboradores com Filtro de Trilha' => 'analise_colaboradores.php?aba=colaboradores&trilha=teste',
    'Cursos com Filtro de Aprovação' => 'analise_colaboradores.php?aba=cursos&aprovacao=Sim'
];

echo "<div style='background: #e2e3e5; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>URLs de Teste (clique para testar):</strong></p>";
foreach ($urls_teste as $nome => $url) {
    echo "<p>• <strong>$nome:</strong> <a href='$url' target='_blank' style='color: #007bff;'>$url</a></p>";
}
echo "</div>";

// Resumo final
echo "<h2>6. 📋 Resumo dos Ajustes Realizados</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Ajustes Implementados</h3>";

echo "<h4>✅ Abas Harmonizadas:</h4>";
echo "<ul>";
echo "<li>Estilo das abas ajustado para combinar com o design da página</li>";
echo "<li>Gradientes e cores Sicoob aplicados</li>";
echo "<li>Transições suaves e efeitos hover</li>";
echo "<li>Estrutura HTML simplificada</li>";
echo "</ul>";

echo "<h4>✅ Novas Métricas nos Cards:</h4>";
echo "<ul>";
echo "<li><strong>Total Atribuídos:</strong> Todos os cursos do colaborador</li>";
echo "<li><strong>Concluídos:</strong> Cursos com data de conclusão</li>";
echo "<li><strong>A Vencer:</strong> Cursos que vencem em 30 dias</li>";
echo "<li><strong>Vencidos:</strong> Cursos com validade expirada</li>";
echo "</ul>";

echo "<h4>✅ Filtros Simplificados:</h4>";
echo "<ul>";
echo "<li>Removido campo 'Situação da Trilha'</li>";
echo "<li>Layout reorganizado em 3 colunas</li>";
echo "<li>Filtros específicos por aba mantidos</li>";
echo "</ul>";

echo "<h4>✅ Correção da Aba de Cursos:</h4>";
echo "<ul>";
echo "<li>Consulta separada para cursos implementada</li>";
echo "<li>Parâmetros específicos para filtros de cursos</li>";
echo "<li>Contagem correta de cursos únicos</li>";
echo "<li>Métricas específicas para análise de cursos</li>";
echo "</ul>";

echo "<h4>✅ Status Atualizado:</h4>";
echo "<ul>";
echo "<li><strong>Vencidos:</strong> Prioridade máxima (vermelho)</li>";
echo "<li><strong>A Vencer:</strong> Atenção necessária (amarelo)</li>";
echo "<li><strong>Em Dia:</strong> Situação regular (verde)</li>";
echo "<li><strong>Pendente:</strong> Sem atividade (cinza)</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>👥 Testar Aba Colaboradores</a>";
echo "<a href='analise_colaboradores.php?aba=cursos' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🎓 Testar Aba Cursos</a>";
echo "</p>";
?>
