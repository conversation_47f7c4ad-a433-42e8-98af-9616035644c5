<?php
/**
 * Verificação de autenticação específica para o projeto de Educação Corporativa
 * Não interfere com as permissões globais do sistema
 */

// Verificar se já foi incluído
if (defined('EDU_AUTH_CHECKED')) {
    return;
}
define('EDU_AUTH_CHECKED', true);

// Incluir autenticação global primeiro
require_once __DIR__ . '/../../auth_check.php';

// Incluir classe de permissões específica
require_once __DIR__ . '/classes/EduPermissions.php';

// Verificar se usuário está logado (usando sistema global)
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit;
}

// Criar instância das permissões específicas do projeto
$edu_permissions = new EduPermissions($pdo, $_SESSION['user_id']);

// Verificar se usuário tem acesso ao projeto
if (!$edu_permissions->hasAccess()) {
    // Usuário não tem acesso ao projeto de educação corporativa
    header('Location: ../../access_denied_edu.php?projeto=educacao-corporativa');
    exit;
}

// Definir variáveis para compatibilidade com código existente
$permissions = $edu_permissions;

// Definir informações do usuário
$user = [
    'id' => $_SESSION['user_id'],
    'nome_completo' => $_SESSION['nome_completo'] ?? 'Usuário',
    'username' => $_SESSION['username'] ?? '',
    'email' => $_SESSION['email'] ?? '',
    'nivel_acesso' => strtolower($edu_permissions->getNivelAcesso())
];

/**
 * Função para verificar acesso a páginas específicas
 * @param array $allowed_levels - Níveis permitidos: ['admin', 'gestor', 'comum']
 */
function checkEduPageAccess($allowed_levels = ['comum']) {
    global $edu_permissions;
    
    $has_access = false;
    
    foreach ($allowed_levels as $level) {
        switch ($level) {
            case 'admin':
                if ($edu_permissions->isAdmin()) {
                    $has_access = true;
                }
                break;
            case 'gestor':
                if ($edu_permissions->isGestor()) {
                    $has_access = true;
                }
                break;
            case 'comum':
                if ($edu_permissions->hasAccess()) {
                    $has_access = true;
                }
                break;
        }
        
        if ($has_access) break;
    }
    
    if (!$has_access) {
        $required_level = implode(' ou ', $allowed_levels);
        header("Location: ../../access_denied_edu.php?projeto=educacao-corporativa&nivel=$required_level");
        exit;
    }
}

/**
 * Função para verificar se usuário pode gerenciar permissões
 */
function canManageEduPermissions() {
    global $edu_permissions;
    return $edu_permissions->canManagePermissions();
}

/**
 * Função para obter informações de debug sobre permissões
 */
function getEduPermissionsDebug() {
    global $edu_permissions;
    
    return [
        'usuario_id' => $edu_permissions->getUsuarioId(),
        'nivel_acesso' => $edu_permissions->getNivelAcesso(),
        'nivel_texto' => $edu_permissions->getNivelTexto(),
        'is_admin' => $edu_permissions->isAdmin(),
        'is_gestor' => $edu_permissions->isGestor(),
        'is_comum' => $edu_permissions->isComum(),
        'is_global_admin' => $edu_permissions->isGlobalAdmin(),
        'has_access' => $edu_permissions->hasAccess(),
        'can_manage_permissions' => $edu_permissions->canManagePermissions(),
        'can_manage_trilhas' => $edu_permissions->canManageTrilhas(),
        'can_access_advanced_reports' => $edu_permissions->canAccessAdvancedReports()
    ];
}

// Log de acesso ao projeto (opcional)
if (defined('EDU_LOG_ACCESS') && EDU_LOG_ACCESS) {
    $stmt = $pdo->prepare("
        INSERT INTO logs (usuario_id, acao, detalhes, data_hora)
        VALUES (?, 'Acesso - Educação Corporativa', ?, NOW())
    ");
    $page = basename($_SERVER['PHP_SELF']);
    $detalhes = "Usuário acessou página: $page (Nível: {$edu_permissions->getNivelTexto()})";
    $stmt->execute([$_SESSION['user_id'], $detalhes]);
}
?>
