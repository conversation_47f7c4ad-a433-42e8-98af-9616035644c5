<?php
/**
 * Teste das Novas Regras de Prazo para Cursos
 * 
 * Este arquivo testa se as novas regras de negócio para prazos estão funcionando corretamente.
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

echo "<h1>⏰ Teste das Novas Regras de Prazo</h1>";

// Teste 1: Verificar regras implementadas
echo "<h2>1. 📋 Novas Regras de Negócio</h2>";

echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Regras para Cursos COM Prazo Personalizado:</strong></p>";
echo "<ul>";
echo "<li>✅ <strong>Campo 'Renovação' preenchido:</strong> Curso tem renovação</li>";
echo "<li>✅ <strong>Campo 'Renovação' vazio:</strong> Curso NÃO tem renovação (uma vez concluído, não precisa refazer)</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Regras para Cursos SEM Prazo Personalizado:</strong></p>";
echo "<ul>";
echo "<li>⚠️ <strong>Por padrão:</strong> NÃO possuem renovação</li>";
echo "<li>✅ <strong>Com data de conclusão:</strong> Desconsiderar prazo (já foi concluído)</li>";
echo "<li>⏰ <strong>Sem data de conclusão:</strong> Usar prazo padrão do relatório</li>";
echo "</ul>";
echo "</div>";

// Teste 2: Verificar configurações de prazos personalizados
echo "<h2>2. ⚙️ Verificação das Configurações</h2>";

try {
    $query_config = "
        SELECT 
            codigo_trilha, codigo_recurso, trilha, recurso,
            primeiro_prazo_dias, renovacao_prazo_dias, prazo_personalizado_ativo,
            CASE 
                WHEN renovacao_prazo_dias IS NULL OR renovacao_prazo_dias = '' OR renovacao_prazo_dias = 0 
                THEN 'SEM RENOVAÇÃO' 
                ELSE 'COM RENOVAÇÃO' 
            END as tipo_renovacao
        FROM edu_prazos_personalizados 
        WHERE prazo_personalizado_ativo = 1
        ORDER BY tipo_renovacao, trilha, recurso
        LIMIT 10
    ";
    
    $stmt = $pdo_edu->prepare($query_config);
    $stmt->execute();
    $configuracoes = $stmt->fetchAll();
    
    if (!empty($configuracoes)) {
        echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>📊 Configurações de Prazos Personalizados</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Trilha</th>";
        echo "<th style='padding: 8px;'>Curso</th>";
        echo "<th style='padding: 8px;'>Primeiro Prazo</th>";
        echo "<th style='padding: 8px;'>Renovação</th>";
        echo "<th style='padding: 8px;'>Tipo</th>";
        echo "</tr>";
        
        foreach ($configuracoes as $config) {
            $cor_tipo = $config['tipo_renovacao'] === 'COM RENOVAÇÃO' ? '#28a745' : '#dc3545';
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($config['trilha'], 0, 30)) . "...</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars(substr($config['recurso'], 0, 30)) . "...</td>";
            echo "<td style='padding: 8px; text-align: center;'>" . $config['primeiro_prazo_dias'] . " dias</td>";
            echo "<td style='padding: 8px; text-align: center;'>" . ($config['renovacao_prazo_dias'] ?: 'N/A') . "</td>";
            echo "<td style='padding: 8px; text-align: center; color: $cor_tipo; font-weight: bold;'>" . $config['tipo_renovacao'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>⚠️ <strong>Nenhuma configuração de prazo personalizado encontrada</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro na consulta:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 3: Simular cenários de aplicação das regras
echo "<h2>3. 🧪 Simulação de Cenários</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Cenários de Teste</h3>";

// Cenário 1: Curso com prazo personalizado COM renovação
echo "<div style='border: 1px solid #28a745; padding: 10px; margin: 10px 0; border-radius: 5px; background: #d4edda;'>";
echo "<h6>✅ Cenário 1: Curso COM Prazo Personalizado + COM Renovação</h6>";
echo "<ul>";
echo "<li><strong>Configuração:</strong> Primeiro Prazo = 365 dias, Renovação = 180 dias</li>";
echo "<li><strong>Colaborador:</strong> Admitido em 2024, sem conclusão anterior</li>";
echo "<li><strong>Resultado Esperado:</strong> Prazo = Data Admissão + 365 dias</li>";
echo "<li><strong>Após Conclusão:</strong> Novo prazo = Data Conclusão + 180 dias</li>";
echo "</ul>";
echo "</div>";

// Cenário 2: Curso com prazo personalizado SEM renovação
echo "<div style='border: 1px solid #dc3545; padding: 10px; margin: 10px 0; border-radius: 5px; background: #f8d7da;'>";
echo "<h6>❌ Cenário 2: Curso COM Prazo Personalizado + SEM Renovação</h6>";
echo "<ul>";
echo "<li><strong>Configuração:</strong> Primeiro Prazo = 365 dias, Renovação = vazio/0</li>";
echo "<li><strong>Colaborador:</strong> Admitido em 2024, sem conclusão anterior</li>";
echo "<li><strong>Resultado Esperado:</strong> Prazo = Data Admissão + 365 dias</li>";
echo "<li><strong>Após Conclusão:</strong> Sem prazo (curso concluído definitivamente)</li>";
echo "</ul>";
echo "</div>";

// Cenário 3: Curso sem prazo personalizado, não concluído
echo "<div style='border: 1px solid #ffc107; padding: 10px; margin: 10px 0; border-radius: 5px; background: #fff3cd;'>";
echo "<h6>⏰ Cenário 3: Curso SEM Prazo Personalizado + NÃO Concluído</h6>";
echo "<ul>";
echo "<li><strong>Configuração:</strong> Sem prazo personalizado</li>";
echo "<li><strong>Colaborador:</strong> Sem data de conclusão</li>";
echo "<li><strong>Resultado Esperado:</strong> Prazo padrão do relatório</li>";
echo "<li><strong>Renovação:</strong> Não se aplica (sem renovação por padrão)</li>";
echo "</ul>";
echo "</div>";

// Cenário 4: Curso sem prazo personalizado, já concluído
echo "<div style='border: 1px solid #17a2b8; padding: 10px; margin: 10px 0; border-radius: 5px; background: #d1ecf1;'>";
echo "<h6>🎯 Cenário 4: Curso SEM Prazo Personalizado + JÁ Concluído</h6>";
echo "<ul>";
echo "<li><strong>Configuração:</strong> Sem prazo personalizado</li>";
echo "<li><strong>Colaborador:</strong> Com data de conclusão</li>";
echo "<li><strong>Resultado Esperado:</strong> Sem prazo (curso concluído definitivamente)</li>";
echo "<li><strong>Status:</strong> 'Concluído' (sem necessidade de refazer)</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

// Teste 4: Verificar dados reais
echo "<h2>4. 📊 Verificação com Dados Reais</h2>";

try {
    // Buscar alguns cursos para teste
    $query_teste = "
        SELECT DISTINCT 
            cpf, usuario, trilha, recurso, codigo_trilha, codigo_recurso,
            data_conclusao, data_admissao, concluir_trilha_ate
        FROM edu_relatorio_educacao 
        WHERE cpf IS NOT NULL AND cpf != ''
        ORDER BY data_conclusao DESC, usuario
        LIMIT 8
    ";
    
    $stmt_teste = $pdo_edu->prepare($query_teste);
    $stmt_teste->execute();
    $cursos_teste = $stmt_teste->fetchAll();
    
    if (!empty($cursos_teste)) {
        // Buscar configurações de prazos
        $stmt_prazos = $pdo_edu->query("SELECT * FROM edu_prazos_personalizados WHERE prazo_personalizado_ativo = 1");
        $prazos_config = [];
        foreach ($stmt_prazos->fetchAll(PDO::FETCH_ASSOC) as $config) {
            $key = $config['codigo_trilha'] . '|' . $config['codigo_recurso'];
            $prazos_config[$key] = $config;
        }
        
        echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>🔍 Análise de Cursos Reais</h3>";
        
        foreach ($cursos_teste as $curso) {
            $key = $curso['codigo_trilha'] . '|' . $curso['codigo_recurso'];
            $tem_prazo_personalizado = isset($prazos_config[$key]);
            $tem_conclusao = !empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00';
            
            // Determinar cenário
            if ($tem_prazo_personalizado) {
                $config = $prazos_config[$key];
                $tem_renovacao = !empty($config['renovacao_prazo_dias']) && $config['renovacao_prazo_dias'] > 0;
                
                if ($tem_conclusao && !$tem_renovacao) {
                    $cenario = "Personalizado SEM renovação + Concluído";
                    $resultado = "Sem prazo (concluído definitivamente)";
                    $cor = "#dc3545";
                } elseif ($tem_conclusao && $tem_renovacao) {
                    $cenario = "Personalizado COM renovação + Concluído";
                    $resultado = "Prazo de renovação aplicado";
                    $cor = "#28a745";
                } else {
                    $cenario = "Personalizado + Não concluído";
                    $resultado = "Primeiro prazo aplicado";
                    $cor = "#007bff";
                }
            } else {
                if ($tem_conclusao) {
                    $cenario = "Sem personalização + Concluído";
                    $resultado = "Sem prazo (concluído definitivamente)";
                    $cor = "#17a2b8";
                } else {
                    $cenario = "Sem personalização + Não concluído";
                    $resultado = "Prazo padrão do relatório";
                    $cor = "#ffc107";
                }
            }
            
            echo "<div style='border: 1px solid $cor; padding: 10px; margin: 10px 0; border-radius: 5px; background: rgba(" . 
                 ($cor === "#dc3545" ? "220,53,69" : 
                  ($cor === "#28a745" ? "40,167,69" : 
                   ($cor === "#007bff" ? "0,123,255" : 
                    ($cor === "#17a2b8" ? "23,162,184" : "255,193,7")))) . ",0.1);'>";
            echo "<h6 style='color: $cor;'>" . htmlspecialchars($curso['usuario']) . " - " . htmlspecialchars(substr($curso['recurso'], 0, 40)) . "...</h6>";
            echo "<table style='width: 100%; font-size: 0.9rem;'>";
            echo "<tr><td><strong>Cenário:</strong></td><td style='color: $cor; font-weight: bold;'>$cenario</td></tr>";
            echo "<tr><td><strong>Data Conclusão:</strong></td><td>" . ($tem_conclusao ? date('d/m/Y', strtotime($curso['data_conclusao'])) : 'N/A') . "</td></tr>";
            echo "<tr><td><strong>Prazo Personalizado:</strong></td><td>" . ($tem_prazo_personalizado ? 'Sim' : 'Não') . "</td></tr>";
            echo "<tr><td><strong>Resultado:</strong></td><td style='font-weight: bold;'>$resultado</td></tr>";
            echo "</table>";
            echo "</div>";
        }
        echo "</div>";
        
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>⚠️ <strong>Nenhum curso encontrado para teste</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro no teste:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Resumo final
echo "<h2>5. 📋 Resumo das Novas Regras</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Regras de Negócio Implementadas</h3>";

echo "<h4>✅ Cursos COM Prazo Personalizado:</h4>";
echo "<ul>";
echo "<li><strong>Campo 'Renovação' preenchido:</strong> Curso tem renovação normal</li>";
echo "<li><strong>Campo 'Renovação' vazio/0:</strong> Curso sem renovação (uma vez concluído, não precisa refazer)</li>";
echo "<li><strong>Primeira vez:</strong> Data admissão + primeiro prazo</li>";
echo "<li><strong>Após conclusão com renovação:</strong> Data conclusão + prazo renovação</li>";
echo "<li><strong>Após conclusão sem renovação:</strong> Sem prazo (concluído definitivamente)</li>";
echo "</ul>";

echo "<h4>⏰ Cursos SEM Prazo Personalizado:</h4>";
echo "<ul>";
echo "<li><strong>Por padrão:</strong> NÃO possuem renovação</li>";
echo "<li><strong>Com data de conclusão:</strong> Sem prazo (concluído definitivamente)</li>";
echo "<li><strong>Sem data de conclusão:</strong> Prazo padrão do relatório</li>";
echo "<li><strong>Nunca renovam:</strong> Uma vez concluídos, não precisam ser refeitos</li>";
echo "</ul>";

echo "<h4>🔧 Implementação Técnica:</h4>";
echo "<ul>";
echo "<li><strong>Função calcularPrazoPersonalizado():</strong> Retorna null para cursos sem renovação concluídos</li>";
echo "<li><strong>Lógica de exibição:</strong> Trata null como 'Concluído' sem prazo</li>";
echo "<li><strong>Status especial:</strong> 'concluido_sem_renovacao' para cursos finalizados</li>";
echo "<li><strong>Interface:</strong> Badge 'Concluído' verde para cursos sem prazo</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>👥 Testar Regras</a>";
echo "<a href='gerenciar_trilhas.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>⚙️ Configurar Prazos</a>";
echo "</p>";
?>
