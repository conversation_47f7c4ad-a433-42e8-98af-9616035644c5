<?php
/**
 * Teste da Correção de "Cursos em Andamento"
 * 
 * Verificar se a correção do cálculo de cursos em andamento foi implementada corretamente.
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

echo "<h1>🔧 Teste da Correção de 'Cursos em Andamento'</h1>";

// Teste 1: Verificar problema identificado
echo "<h2>1. ❌ Problema Identificado</h2>";

echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🚨 Inconsistência Encontrada:</h3>";
echo "<p><strong>Card principal 'Cursos em Andamento' estava zerado, mas colaboradores tinham badge 'Em Andamento'</strong></p>";

echo "<h4>Sintomas do Problema:</h4>";
echo "<ul>";
echo "<li>❌ <strong>Card Principal:</strong> 'Cursos em Andamento' mostrava 0</li>";
echo "<li>❌ <strong>Cards Colaboradores:</strong> Vários com badge 'Em Andamento'</li>";
echo "<li>❌ <strong>Inconsistência:</strong> Lógicas diferentes para mesmo conceito</li>";
echo "<li>❌ <strong>Confusão:</strong> Usuários vendo dados contraditórios</li>";
echo "</ul>";

echo "<h4>Causa Raiz:</h4>";
echo "<ul>";
echo "<li>❌ <strong>Lógica Divergente:</strong> Card principal e badges usavam critérios diferentes</li>";
echo "<li>❌ <strong>Função Incompleta:</strong> calcularStatusColaborador não considerava 'Em Andamento'</li>";
echo "<li>❌ <strong>Critério Restritivo:</strong> Card principal excluía vencidos e a vencer</li>";
echo "<li>❌ <strong>Falta de Padronização:</strong> Cada local calculava de forma diferente</li>";
echo "</ul>";
echo "</div>";

// Teste 2: Verificar correção implementada
echo "<h2>2. ✅ Correção Implementada</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔧 Solução Aplicada:</h3>";

echo "<h4>1. Função calcularStatusColaborador Atualizada:</h4>";
echo "<pre style='background: #ffffff; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
// ANTES (Não considerava Em Andamento)
function calcularStatusColaborador(\$colaborador) {
    if (\$cursos_vencidos > 0) {
        return ['status' => 'vencido', 'texto' => 'Cursos Vencidos', 'classe' => 'danger'];
    } elseif (\$cursos_a_vencer > 0) {
        return ['status' => 'a_vencer', 'texto' => 'A Vencer', 'classe' => 'warning'];
    } elseif (\$cursos_concluidos > 0) {
        return ['status' => 'em_dia', 'texto' => 'Em Dia', 'classe' => 'success'];
    } else {
        return ['status' => 'pendente', 'texto' => 'Pendente', 'classe' => 'secondary'];
    }
}

// DEPOIS (Considera Em Andamento)
function calcularStatusColaborador(\$colaborador) {
    // Verificar se há cursos em andamento
    \$cursos_colaborador = buscarCursosColaborador(\$colaborador['cpf'], \$pdo_edu, \$prazos_config);
    \$tem_em_andamento = false;
    
    foreach (\$cursos_colaborador as \$curso) {
        if (!empty(\$curso['andamento_etapa']) && 
            \$curso['aprovacao'] !== 'Sim' && 
            \$curso['status_prazo'] !== 'vencido' && 
            \$curso['status_prazo'] !== 'a_vencer') {
            \$tem_em_andamento = true;
            break;
        }
    }

    if (\$cursos_vencidos > 0) {
        return ['status' => 'vencido', 'texto' => 'Cursos Vencidos', 'classe' => 'danger'];
    } elseif (\$cursos_a_vencer > 0) {
        return ['status' => 'a_vencer', 'texto' => 'A Vencer', 'classe' => 'warning'];
    } elseif (\$tem_em_andamento) {
        return ['status' => 'em_andamento', 'texto' => 'Em Andamento', 'classe' => 'info'];
    } elseif (\$cursos_concluidos > 0) {
        return ['status' => 'em_dia', 'texto' => 'Em Dia', 'classe' => 'success'];
    } else {
        return ['status' => 'pendente', 'texto' => 'Pendente', 'classe' => 'secondary'];
    }
}
");
echo "</pre>";

echo "<h4>2. Cálculo do Card Principal Simplificado:</h4>";
echo "<pre style='background: #ffffff; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
// ANTES (Lógica restritiva)
foreach (\$cursos_colaborador as \$curso) {
    // Curso em andamento: tem andamento_etapa mas não está aprovado, vencido ou a vencer
    if (!empty(\$curso['andamento_etapa']) && 
        \$curso['aprovacao'] !== 'Sim' && 
        \$curso['status_prazo'] !== 'vencido' && 
        \$curso['status_prazo'] !== 'a_vencer') {
        \$em_andamento++;
    }
}

// DEPOIS (Lógica simplificada e consistente)
foreach (\$cursos_colaborador as \$curso) {
    // Curso em andamento: tem andamento_etapa mas não está aprovado
    if (!empty(\$curso['andamento_etapa']) && \$curso['aprovacao'] !== 'Sim') {
        \$em_andamento++;
    }
}
");
echo "</pre>";

echo "<h4>3. Hierarquia de Prioridade dos Status:</h4>";
echo "<ol>";
echo "<li><strong>Vencidos:</strong> Prioridade máxima (classe: danger)</li>";
echo "<li><strong>A Vencer:</strong> Segunda prioridade (classe: warning)</li>";
echo "<li><strong>Em Andamento:</strong> Terceira prioridade (classe: info)</li>";
echo "<li><strong>Em Dia:</strong> Quarta prioridade (classe: success)</li>";
echo "<li><strong>Pendente:</strong> Última prioridade (classe: secondary)</li>";
echo "</ol>";
echo "</div>";

// Teste 3: Verificar lógica unificada
echo "<h2>3. ✅ Lógica Unificada</h2>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔄 Critério Único para 'Em Andamento':</h3>";

echo "<h4>Definição Unificada:</h4>";
echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>Curso em Andamento:</strong> Curso que possui andamento_etapa preenchido mas ainda não foi aprovado.";
echo "</div>";

echo "<h4>Critérios Técnicos:</h4>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 8px;'>Campo</th>";
echo "<th style='padding: 8px;'>Condição</th>";
echo "<th style='padding: 8px;'>Observação</th>";
echo "</tr>";
echo "<tr><td style='padding: 8px;'>andamento_etapa</td><td style='padding: 8px;'>NOT NULL e != ''</td><td style='padding: 8px;'>Deve ter progresso registrado</td></tr>";
echo "<tr><td style='padding: 8px;'>aprovacao</td><td style='padding: 8px;'>!= 'Sim'</td><td style='padding: 8px;'>Ainda não foi aprovado</td></tr>";
echo "</table>";

echo "<h4>Exemplos Práticos:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Em Andamento:</strong> andamento_etapa = '50%' e aprovacao = 'Não'</li>";
echo "<li>✅ <strong>Em Andamento:</strong> andamento_etapa = 'Módulo 2' e aprovacao = NULL</li>";
echo "<li>❌ <strong>Não é Em Andamento:</strong> andamento_etapa = NULL (sem progresso)</li>";
echo "<li>❌ <strong>Não é Em Andamento:</strong> aprovacao = 'Sim' (já aprovado)</li>";
echo "</ul>";

echo "<h4>Benefícios da Unificação:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Consistência:</strong> Mesma lógica em todo o sistema</li>";
echo "<li>✅ <strong>Simplicidade:</strong> Critério claro e objetivo</li>";
echo "<li>✅ <strong>Manutenibilidade:</strong> Fácil de entender e modificar</li>";
echo "<li>✅ <strong>Confiabilidade:</strong> Dados sempre alinhados</li>";
echo "</ul>";
echo "</div>";

// Teste 4: Como testar a correção
echo "<h2>4. 🧪 Como Testar a Correção</h2>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📋 Passos para Verificação:</h3>";

echo "<h4>Teste 1: Card Principal</h4>";
echo "<ol>";
echo "<li><strong>Acesse:</strong> analise_colaboradores.php</li>";
echo "<li><strong>Observe:</strong> Card 'Cursos em Andamento' no topo</li>";
echo "<li><strong>Verifique:</strong> Não deve mais estar zerado</li>";
echo "<li><strong>Anote:</strong> O valor exibido</li>";
echo "</ol>";

echo "<h4>Teste 2: Badges dos Colaboradores</h4>";
echo "<ol>";
echo "<li><strong>Procure:</strong> Colaboradores com badge 'Em Andamento'</li>";
echo "<li><strong>Conte:</strong> Quantos colaboradores têm esse badge</li>";
echo "<li><strong>Compare:</strong> Com o valor do card principal</li>";
echo "<li><strong>Confirme:</strong> Valores devem ser consistentes</li>";
echo "</ol>";

echo "<h4>Teste 3: Filtro por Status</h4>";
echo "<ol>";
echo "<li><strong>Aplique:</strong> Filtro Status = 'Em Andamento'</li>";
echo "<li><strong>Observe:</strong> Apenas colaboradores com esse status</li>";
echo "<li><strong>Verifique:</strong> Card principal atualiza corretamente</li>";
echo "<li><strong>Confirme:</strong> Números batem</li>";
echo "</ol>";

echo "<h4>Teste 4: Detalhes do Colaborador</h4>";
echo "<ol>";
echo "<li><strong>Clique:</strong> 'Ver Detalhes' de um colaborador 'Em Andamento'</li>";
echo "<li><strong>Observe:</strong> Lista de cursos no modal</li>";
echo "<li><strong>Identifique:</strong> Quais cursos têm andamento_etapa</li>";
echo "<li><strong>Confirme:</strong> Lógica está correta</li>";
echo "</ol>";
echo "</div>";

// Teste 5: Verificar dados do banco
echo "<h2>5. 📊 Verificação no Banco de Dados</h2>";

try {
    // Verificar cursos com andamento_etapa
    $query_andamento = "
        SELECT 
            COUNT(DISTINCT cpf) as colaboradores_com_andamento,
            COUNT(*) as cursos_com_andamento
        FROM edu_relatorio_educacao 
        WHERE andamento_etapa IS NOT NULL 
        AND andamento_etapa != '' 
        AND aprovacao != 'Sim'
    ";
    
    $stmt = $pdo_edu->prepare($query_andamento);
    $stmt->execute();
    $dados_andamento = $stmt->fetch();
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>🔍 Dados do Banco:</h3>";
    
    echo "<h4>Cursos em Andamento:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Métrica</th>";
    echo "<th style='padding: 8px;'>Valor</th>";
    echo "<th style='padding: 8px;'>Descrição</th>";
    echo "</tr>";
    echo "<tr>";
    echo "<td style='padding: 8px;'>Colaboradores com Andamento</td>";
    echo "<td style='padding: 8px;'>" . number_format($dados_andamento['colaboradores_com_andamento']) . "</td>";
    echo "<td style='padding: 8px;'>CPFs únicos com cursos em andamento</td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td style='padding: 8px;'>Cursos com Andamento</td>";
    echo "<td style='padding: 8px;'>" . number_format($dados_andamento['cursos_com_andamento']) . "</td>";
    echo "<td style='padding: 8px;'>Total de registros com andamento_etapa</td>";
    echo "</tr>";
    echo "</table>";
    
    // Verificar alguns exemplos
    $query_exemplos = "
        SELECT 
            cpf,
            usuario,
            recurso,
            andamento_etapa,
            aprovacao
        FROM edu_relatorio_educacao 
        WHERE andamento_etapa IS NOT NULL 
        AND andamento_etapa != '' 
        AND aprovacao != 'Sim'
        LIMIT 5
    ";
    
    $stmt_exemplos = $pdo_edu->prepare($query_exemplos);
    $stmt_exemplos->execute();
    $exemplos = $stmt_exemplos->fetchAll();
    
    if (!empty($exemplos)) {
        echo "<h4>Exemplos de Cursos em Andamento:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>CPF</th>";
        echo "<th style='padding: 8px;'>Colaborador</th>";
        echo "<th style='padding: 8px;'>Curso</th>";
        echo "<th style='padding: 8px;'>Andamento</th>";
        echo "<th style='padding: 8px;'>Aprovação</th>";
        echo "</tr>";
        
        foreach ($exemplos as $exemplo) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($exemplo['cpf']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($exemplo['usuario']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($exemplo['recurso']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($exemplo['andamento_etapa']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($exemplo['aprovacao'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>✅ Estes colaboradores devem aparecer com badge 'Em Andamento'</strong></p>";
    } else {
        echo "<p><strong>ℹ️ Nenhum curso em andamento encontrado no banco</strong></p>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro na consulta:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Resumo final
echo "<h2>6. 📋 Resumo da Correção</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Correção Implementada com Sucesso</h3>";

echo "<h4>✅ Problemas Resolvidos:</h4>";
echo "<ul>";
echo "<li><strong>Inconsistência:</strong> Card principal e badges agora usam mesma lógica</li>";
echo "<li><strong>Função Atualizada:</strong> calcularStatusColaborador considera 'Em Andamento'</li>";
echo "<li><strong>Cálculo Simplificado:</strong> Critério mais abrangente no card principal</li>";
echo "<li><strong>Hierarquia Clara:</strong> Ordem de prioridade bem definida</li>";
echo "</ul>";

echo "<h4>✅ Melhorias Implementadas:</h4>";
echo "<ul>";
echo "<li><strong>Lógica Unificada:</strong> Mesmo critério em todo o sistema</li>";
echo "<li><strong>Código Limpo:</strong> Funções mais claras e organizadas</li>";
echo "<li><strong>Manutenibilidade:</strong> Fácil de entender e modificar</li>";
echo "<li><strong>Confiabilidade:</strong> Dados sempre consistentes</li>";
echo "</ul>";

echo "<h4>🚀 Benefícios:</h4>";
echo "<ul>";
echo "<li><strong>Precisão:</strong> Métricas refletem realidade do sistema</li>";
echo "<li><strong>Consistência:</strong> Dados alinhados em toda interface</li>";
echo "<li><strong>Usabilidade:</strong> Informações claras e confiáveis</li>";
echo "<li><strong>Monitoramento:</strong> Acompanhamento correto do progresso</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📊 Verificar Correção</a>";
echo "<a href='analise_colaboradores.php?status_curso=em_andamento' style='background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🔄 Filtrar Em Andamento</a>";
echo "</p>";
?>
