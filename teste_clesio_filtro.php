<?php
/**
 * Teste específico para verificar se CLESIO está sendo filtrado corretamente
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/IntranetAPI.php';

echo "<h1>🔍 Teste Específico: CLESIO GOMES DE JESUS</h1>\n";

// Criar instância da API
$api = new IntranetAPI();

// Buscar dados da API SEM CACHE
echo "<h2>1. 📊 Buscar Dados da API</h2>\n";
$usuarios_intranet_todos = $api->listarUsuarios(false, false);
$usuarios_intranet_ativos = $api->listarUsuariosAtivos(false);

echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>API Status:</strong></p>";
echo "<ul>";
echo "<li><strong>Total usuários:</strong> " . count($usuarios_intranet_todos) . "</li>";
echo "<li><strong>Usuários ativos:</strong> " . count($usuarios_intranet_ativos) . "</li>";
echo "</ul>";
echo "</div>";

// Criar mapas
$mapa_usuarios_ativos = [];
$mapa_usuarios_todos = [];

foreach ($usuarios_intranet_ativos as $usuario) {
    if (!empty($usuario['cpf'])) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
        $mapa_usuarios_ativos[$cpf_normalizado] = $usuario;
    }
}

foreach ($usuarios_intranet_todos as $usuario) {
    if (!empty($usuario['cpf'])) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
        $mapa_usuarios_todos[$cpf_normalizado] = $usuario;
    }
}

echo "<h2>2. 🔍 Buscar CLESIO no Banco de Dados</h2>\n";

// Buscar CLESIO especificamente
$clesio_query = "SELECT cpf, usuario FROM edu_relatorio_educacao WHERE usuario LIKE '%CLESIO%' GROUP BY cpf LIMIT 5";
$stmt_clesio = $pdo_edu->prepare($clesio_query);
$stmt_clesio->execute();
$clesio_results = $stmt_clesio->fetchAll();

if (empty($clesio_results)) {
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>⚠️ Nenhum colaborador com nome CLESIO encontrado no banco</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Colaboradores encontrados com nome CLESIO:</strong> " . count($clesio_results) . "</p>";
    echo "</div>";
    
    foreach ($clesio_results as $clesio_data) {
        $clesio_cpf = str_pad(preg_replace('/[^0-9]/', '', $clesio_data['cpf']), 11, '0', STR_PAD_LEFT);
        $clesio_ativo = $mapa_usuarios_ativos[$clesio_cpf] ?? null;
        $clesio_todos = $mapa_usuarios_todos[$clesio_cpf] ?? null;
        
        echo "<h3>👤 " . htmlspecialchars($clesio_data['usuario']) . "</h3>";
        
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='padding: 10px;'>Verificação</th>";
        echo "<th style='padding: 10px;'>Resultado</th>";
        echo "<th style='padding: 10px;'>Detalhes</th>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td style='padding: 10px;'><strong>CPF normalizado</strong></td>";
        echo "<td style='padding: 10px;'>" . $clesio_cpf . "</td>";
        echo "<td style='padding: 10px;'>" . substr($clesio_data['cpf'], 0, 3) . "***</td>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td style='padding: 10px;'><strong>Existe no mapa TODOS</strong></td>";
        echo "<td style='padding: 10px;'>" . ($clesio_todos ? '✅ SIM' : '❌ NÃO') . "</td>";
        echo "<td style='padding: 10px;'>" . ($clesio_todos ? 'Status: ' . ($clesio_todos['status'] ?? 'indefinido') : 'N/A') . "</td>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td style='padding: 10px;'><strong>Existe no mapa ATIVOS</strong></td>";
        echo "<td style='padding: 10px;'>" . ($clesio_ativo ? '✅ SIM' : '❌ NÃO') . "</td>";
        echo "<td style='padding: 10px;'>" . ($clesio_ativo ? 'Status: ' . ($clesio_ativo['status'] ?? 'indefinido') : 'N/A') . "</td>";
        echo "</tr>";
        
        // Testar a condição de filtro
        $condicao_filtro = ($clesio_todos && !$clesio_ativo);
        
        echo "<tr style='background: " . ($condicao_filtro ? '#f8d7da' : '#d4edda') . ";'>";
        echo "<td style='padding: 10px;'><strong>Condição: (todos && !ativo)</strong></td>";
        echo "<td style='padding: 10px;'>" . ($condicao_filtro ? '✅ VERDADEIRA' : '❌ FALSA') . "</td>";
        echo "<td style='padding: 10px;'>" . ($condicao_filtro ? 'Deve ser filtrado' : 'Deve ser processado') . "</td>";
        echo "</tr>";
        
        echo "<tr style='background: " . ($condicao_filtro ? '#f8d7da' : '#d4edda') . ";'>";
        echo "<td style='padding: 10px;'><strong>Ação na página</strong></td>";
        echo "<td style='padding: 10px;'>" . ($condicao_filtro ? '🚫 FILTRAR' : '✅ EXIBIR') . "</td>";
        echo "<td style='padding: 10px;'>" . ($condicao_filtro ? 'NÃO deve aparecer' : 'Deve aparecer') . "</td>";
        echo "</tr>";
        
        echo "<tr style='background: " . ($condicao_filtro ? '#f8d7da' : '#d4edda') . ";'>";
        echo "<td style='padding: 10px;'><strong>Resultado esperado</strong></td>";
        echo "<td style='padding: 10px;'>" . ($condicao_filtro ? '❌ NÃO APARECE' : '✅ APARECE') . "</td>";
        echo "<td style='padding: 10px;'>" . ($condicao_filtro ? 'Inativo - filtrado' : ($clesio_ativo ? 'Ativo - exibido' : 'Não encontrado - Sem PA')) . "</td>";
        echo "</tr>";
        
        echo "</table>";
        echo "</div>";
        
        if ($clesio_todos) {
            echo "<div style='background: #e2e3e5; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p><strong>📋 Dados completos na API (TODOS):</strong></p>";
            echo "<pre style='font-size: 11px; background: white; padding: 10px; border-radius: 3px;'>";
            echo htmlspecialchars(json_encode($clesio_todos, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            echo "</pre>";
            echo "</div>";
        }
    }
}

echo "<h2>3. 🧪 Teste do Filtro por Nome</h2>\n";

// Simular filtro por nome como na página principal
$filtro_nome = 'clesio';

echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Simulando filtro por nome:</strong> '$filtro_nome'</p>";
echo "</div>";

// Query similar à da página principal
$where_conditions = [];
$params = [];

if (!empty($filtro_nome)) {
    $where_conditions[] = "usuario LIKE ?";
    $params[] = '%' . $filtro_nome . '%';
}

$colaboradores_query = "
    SELECT
        cpf,
        MAX(usuario) as usuario,
        COUNT(DISTINCT trilha) as total_trilhas,
        COUNT(DISTINCT CONCAT(codigo_trilha, '|', codigo_recurso)) as total_cursos
    FROM edu_relatorio_educacao
    " . (!empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "") . "
    GROUP BY cpf
    ORDER BY MAX(usuario)
";

$stmt_colaboradores = $pdo_edu->prepare($colaboradores_query);
$stmt_colaboradores->execute($params);
$colaboradores_filtrados = $stmt_colaboradores->fetchAll();

echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Colaboradores encontrados no banco com filtro '$filtro_nome':</strong> " . count($colaboradores_filtrados) . "</p>";
echo "</div>";

// Aplicar filtro de inativos
$colaboradores_apos_filtro = [];
$total_filtrados = 0;

foreach ($colaboradores_filtrados as $colaborador) {
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    
    $usuario_intranet_ativo = $mapa_usuarios_ativos[$cpf_normalizado] ?? null;
    $usuario_intranet_todos = $mapa_usuarios_todos[$cpf_normalizado] ?? null;
    
    // Aplicar filtro de inativos
    if ($usuario_intranet_todos && !$usuario_intranet_ativo) {
        $total_filtrados++;
        continue; // Filtrar inativo
    }
    
    $colaboradores_apos_filtro[] = $colaborador;
}

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📊 Resultado do Filtro:</h3>";
echo "<ul>";
echo "<li><strong>Colaboradores encontrados no banco:</strong> " . count($colaboradores_filtrados) . "</li>";
echo "<li><strong>Colaboradores filtrados (inativos):</strong> $total_filtrados</li>";
echo "<li><strong>Colaboradores que devem aparecer:</strong> " . count($colaboradores_apos_filtro) . "</li>";
echo "</ul>";
echo "</div>";

if (count($colaboradores_apos_filtro) > 0) {
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>✅ Colaboradores que devem aparecer na página:</strong></p>";
    echo "<ul>";
    foreach ($colaboradores_apos_filtro as $colaborador) {
        echo "<li>" . htmlspecialchars($colaborador['usuario']) . " (CPF: " . substr($colaborador['cpf'], 0, 3) . "***)</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if ($total_filtrados > 0) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>❌ Colaboradores filtrados (não devem aparecer):</strong></p>";
    echo "<ul>";
    foreach ($colaboradores_filtrados as $colaborador) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
        $usuario_intranet_ativo = $mapa_usuarios_ativos[$cpf_normalizado] ?? null;
        $usuario_intranet_todos = $mapa_usuarios_todos[$cpf_normalizado] ?? null;
        
        if ($usuario_intranet_todos && !$usuario_intranet_ativo) {
            echo "<li>" . htmlspecialchars($colaborador['usuario']) . " (CPF: " . substr($colaborador['cpf'], 0, 3) . "***) - INATIVO</li>";
        }
    }
    echo "</ul>";
    echo "</div>";
}

echo "<h2>4. 🎯 Conclusão</h2>\n";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Resultado Esperado:</h3>";
echo "<p>Quando você filtrar por 'clesio' na página principal, deve aparecer:</p>";
echo "<ul>";
echo "<li><strong>Total de colaboradores:</strong> " . count($colaboradores_apos_filtro) . " (não " . count($colaboradores_filtrados) . ")</li>";
echo "<li><strong>CLESIO GOMES DE JESUS:</strong> " . ($total_filtrados > 0 ? 'NÃO deve aparecer (inativo)' : 'Deve aparecer normalmente') . "</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔧 Teste na Página Principal:</h3>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores&nome=clesio&nocache=" . time() . "' ";
echo "style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>";
echo "🔍 Testar Filtro por 'clesio'</a>";
echo "</p>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
?>
