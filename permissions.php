<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/config.php';
require_once '../../config/database.php';
require_once 'classes/EduPermissions.php';

// Verificar sessão
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit;
}

// Criar instância das permissões
$edu_permissions = new EduPermissions($pdo, $_SESSION['user_id']);

// Verificar se pode gerenciar permissões
if (!$edu_permissions->canManagePermissions()) {
    header('Location: ../../access_denied_edu.php?projeto=educacao-corporativa&nivel=admin');
    exit;
}

// Processar ações POST
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        if ($action == 'update_permission') {
            $usuario_id = $_POST['usuario_id'];
            $nivel_acesso = $_POST['nivel_acesso'];
            
            // Validar
            $niveis_validos = ['ADMIN', 'GESTOR', 'COMUM'];
            if (!in_array($nivel_acesso, $niveis_validos)) {
                throw new Exception('Nível de acesso inválido');
            }
            
            // Buscar nome do usuário
            $stmt = $pdo->prepare("SELECT nome_completo FROM usuarios WHERE id = ?");
            $stmt->execute([$usuario_id]);
            $nome_usuario = $stmt->fetchColumn();
            
            if (!$nome_usuario) {
                throw new Exception('Usuário não encontrado');
            }
            
            // Verificar se já existe permissão ativa
            $stmt = $pdo->prepare("SELECT id FROM edu_permissoes_usuarios WHERE usuario_id = ? AND ativo = TRUE");
            $stmt->execute([$usuario_id]);
            $permissao_existente = $stmt->fetchColumn();

            if ($permissao_existente) {
                // Atualizar permissão existente
                $stmt = $pdo->prepare("
                    UPDATE edu_permissoes_usuarios
                    SET nivel_acesso = ?,
                        usuario_atualizacao = ?,
                        data_atualizacao = CURRENT_TIMESTAMP
                    WHERE usuario_id = ? AND ativo = TRUE
                ");
                $stmt->execute([$nivel_acesso, $_SESSION['user_id'], $usuario_id]);
            } else {
                // Inserir nova permissão
                $stmt = $pdo->prepare("
                    INSERT INTO edu_permissoes_usuarios (usuario_id, nivel_acesso, usuario_criacao, usuario_atualizacao)
                    VALUES (?, ?, ?, ?)
                ");
                $stmt->execute([$usuario_id, $nivel_acesso, $_SESSION['user_id'], $_SESSION['user_id']]);
            }
            
            $message = 'Permissão atualizada com sucesso!';
        }
        
        if ($action == 'remove_permission') {
            $usuario_id = $_POST['usuario_id'];

            // Buscar nome do usuário
            $stmt = $pdo->prepare("SELECT nome_completo FROM usuarios WHERE id = ?");
            $stmt->execute([$usuario_id]);
            $nome_usuario = $stmt->fetchColumn();

            if (!$nome_usuario) {
                throw new Exception('Usuário não encontrado');
            }

            // Remover permissão (deletar registro)
            $stmt = $pdo->prepare("
                DELETE FROM edu_permissoes_usuarios
                WHERE usuario_id = ? AND ativo = TRUE
            ");
            $stmt->execute([$usuario_id]);

            // Log da remoção
            $stmt = $pdo->prepare("
                INSERT INTO logs (usuario_id, acao, detalhes, data_hora)
                VALUES (?, 'Educação Corporativa - Remoção de permissão', ?, NOW())
            ");
            $detalhes_log = "Usuário '$nome_usuario' (ID: $usuario_id) teve permissão de acesso removida por " . ($_SESSION['nome_completo'] ?? 'Sistema');
            $stmt->execute([$_SESSION['user_id'], $detalhes_log]);

            $message = 'Acesso removido com sucesso!';
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Filtros
$filtro_nome = $_GET['nome'] ?? '';
$filtro_setor = $_GET['setor'] ?? '';
$filtro_nivel = $_GET['nivel'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Construir query com filtros - TODOS os usuários
$where_conditions = ["u.ativo = TRUE"];
$params = [];

if ($filtro_nome) {
    $where_conditions[] = "(u.nome_completo LIKE ? OR u.username LIKE ?)";
    $params[] = "%$filtro_nome%";
    $params[] = "%$filtro_nome%";
}

if ($filtro_setor) {
    $where_conditions[] = "s.id = ?";
    $params[] = $filtro_setor;
}

if ($filtro_nivel) {
    if ($filtro_nivel === 'sem_permissao') {
        $where_conditions[] = "epu.nivel_acesso IS NULL";
    } else {
        $where_conditions[] = "epu.nivel_acesso = ? AND epu.ativo = TRUE";
        $params[] = strtoupper($filtro_nivel);
    }
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

// Contar total de registros
$count_query = "
    SELECT COUNT(DISTINCT u.id)
    FROM usuarios u
    LEFT JOIN usuario_setor us ON u.id = us.usuario_id
    LEFT JOIN setores s ON us.setor_id = s.id
    LEFT JOIN edu_permissoes_usuarios epu ON u.id = epu.usuario_id AND epu.ativo = TRUE
    $where_clause
";
$stmt = $pdo->prepare($count_query);
$stmt->execute($params);
$total_records = $stmt->fetchColumn();
$total_pages = ceil($total_records / $per_page);

// Buscar TODOS os usuários (com e sem permissão)
$query = "
    SELECT u.id, u.username, u.nome_completo, u.email,
           GROUP_CONCAT(DISTINCT s.nome ORDER BY s.nome SEPARATOR ', ') as setores,
           epu.nivel_acesso, epu.data_criacao as permissao_criada,
           uc.nome_completo as criado_por, ua.nome_completo as atualizado_por
    FROM usuarios u
    LEFT JOIN usuario_setor us ON u.id = us.usuario_id
    LEFT JOIN setores s ON us.setor_id = s.id
    LEFT JOIN edu_permissoes_usuarios epu ON u.id = epu.usuario_id AND epu.ativo = TRUE
    LEFT JOIN usuarios uc ON epu.usuario_criacao = uc.id
    LEFT JOIN usuarios ua ON epu.usuario_atualizacao = ua.id
    $where_clause
    GROUP BY u.id, u.username, u.nome_completo, u.email, epu.nivel_acesso, epu.data_criacao, uc.nome_completo, ua.nome_completo
    ORDER BY u.nome_completo
    LIMIT $per_page OFFSET $offset
";

$stmt = $pdo->prepare($query);
$stmt->execute($params);
$usuarios = $stmt->fetchAll();

// Buscar setores para filtro
$stmt = $pdo->prepare("SELECT id, nome FROM setores WHERE ativo = TRUE ORDER BY nome");
$stmt->execute();
$setores_disponiveis = $stmt->fetchAll();

// Níveis de acesso
$niveis_acesso = [
    ['id' => 'ADMIN', 'nome' => 'Administrador'],
    ['id' => 'GESTOR', 'nome' => 'Gestor'],
    ['id' => 'COMUM', 'nome' => 'Usuário']
];
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Permissões - <?php echo EDU_PROJECT_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --sicoob-verde-escuro: #003641;
            --sicoob-turquesa: #00AE9D;
        }
        
        .card { 
            border: none; 
            border-radius: 12px; 
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); 
        }
        
        .card-header { 
            background: linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa)); 
            color: white; 
            border-radius: 12px 12px 0 0 !important;
        }
        
        .btn-primary { 
            background-color: var(--sicoob-verde-escuro); 
            border-color: var(--sicoob-verde-escuro); 
        }
        
        .btn-primary:hover { 
            background-color: var(--sicoob-turquesa); 
            border-color: var(--sicoob-turquesa); 
        }
        
        .text-primary { 
            color: var(--sicoob-verde-escuro) !important; 
        }
        
        .permission-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }

        .badge-comum { background-color: #6c757d; }
        .badge-gestor { background-color: #fd7e14; }
        .badge-admin { background-color: #dc3545; }
        .badge-administrador { background-color: #dc3545; }
        .badge-sem-permissao { background-color: #e9ecef; color: #6c757d; }

        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .table td {
            font-size: 0.9rem;
            vertical-align: middle;
        }

        .action-buttons .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        .form-label.small {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .form-select-sm, .form-control-sm {
            font-size: 0.875rem;
            padding: 0.25rem 0.5rem;
        }

        .btn-sm {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <div class="container-fluid mt-4">
        <!-- Alertas -->
        <?php if (isset($message)): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Cabeçalho -->
        <div class="row mb-4">
            <div class="col">
                <h2 class="text-primary">
                    <i class="fas fa-user-shield me-2"></i>
                    Gerenciar Permissões - Educação Corporativa
                </h2>
                <p class="text-muted">Gerencie os níveis de acesso dos usuários ao sistema de educação corporativa.</p>
            </div>
        </div>

        <!-- Filtros -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-filter"></i> Filtros
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-2 align-items-end">
                    <div class="col-lg-3 col-md-4">
                        <label class="form-label small">Nome ou Usuário</label>
                        <input type="text" name="nome" class="form-control form-control-sm" value="<?php echo htmlspecialchars($filtro_nome); ?>" placeholder="Digite nome ou usuário...">
                    </div>
                    <div class="col-lg-2 col-md-3">
                        <label class="form-label small">Setor</label>
                        <select name="setor" class="form-select form-select-sm">
                            <option value="">Todos</option>
                            <?php foreach ($setores_disponiveis as $setor): ?>
                            <option value="<?php echo $setor['id']; ?>" <?php echo $filtro_setor == $setor['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($setor['nome']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-lg-2 col-md-3">
                        <label class="form-label small">Nível de Acesso</label>
                        <select name="nivel" class="form-select form-select-sm">
                            <option value="">Todos</option>
                            <option value="admin" <?php echo $filtro_nivel == 'admin' ? 'selected' : ''; ?>>Administrador</option>
                            <option value="gestor" <?php echo $filtro_nivel == 'gestor' ? 'selected' : ''; ?>>Gestor</option>
                            <option value="comum" <?php echo $filtro_nivel == 'comum' ? 'selected' : ''; ?>>Usuário</option>
                            <option value="sem_permissao" <?php echo $filtro_nivel == 'sem_permissao' ? 'selected' : ''; ?>>Sem Permissão</option>
                        </select>
                    </div>
                    <div class="col-lg-3 col-md-4">
                        <button type="submit" class="btn btn-primary btn-sm me-1">
                            <i class="fas fa-search"></i> Filtrar
                        </button>
                        <a href="permissions.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-times"></i> Limpar
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Tabela de Usuários -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-users"></i> Usuários e Permissões
                </h6>
                <span class="badge bg-light text-dark"><?php echo $total_records; ?> usuários encontrados</span>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Usuário</th>
                                <th>Nome Completo</th>
                                <th>Email</th>
                                <th>Setores</th>
                                <th>Nível de Acesso</th>
                                <th>Permissão Criada</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($usuarios as $usuario): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($usuario['username']); ?></strong>
                                    <br><small class="text-muted">ID: <?php echo $usuario['id']; ?></small>
                                </td>
                                <td><?php echo htmlspecialchars($usuario['nome_completo']); ?></td>
                                <td>
                                    <?php if ($usuario['email']): ?>
                                        <a href="mailto:<?php echo htmlspecialchars($usuario['email']); ?>" class="text-decoration-none">
                                            <?php echo htmlspecialchars($usuario['email']); ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($usuario['setores']): ?>
                                        <small><?php echo htmlspecialchars($usuario['setores']); ?></small>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($usuario['nivel_acesso']): ?>
                                        <span class="badge permission-badge badge-<?php echo strtolower($usuario['nivel_acesso']); ?>">
                                            <?php
                                            switch($usuario['nivel_acesso']) {
                                                case 'ADMIN': echo 'Administrador'; break;
                                                case 'GESTOR': echo 'Gestor'; break;
                                                case 'COMUM': echo 'Usuário'; break;
                                                default: echo ucfirst(strtolower($usuario['nivel_acesso']));
                                            }
                                            ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="badge permission-badge badge-sem-permissao">
                                            Sem Permissão
                                        </span>
                                    <?php endif; ?>

                                    <?php if ($usuario['permissao_criada']): ?>
                                        <br><small class="text-muted">
                                            Criada em: <?php echo date('d/m/Y', strtotime($usuario['permissao_criada'])); ?>
                                            <?php if ($usuario['criado_por']): ?>
                                                por <?php echo htmlspecialchars($usuario['criado_por']); ?>
                                            <?php endif; ?>
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($usuario['permissao_criada']): ?>
                                        <?php echo date('d/m/Y H:i', strtotime($usuario['permissao_criada'])); ?>
                                        <?php if ($usuario['atualizado_por']): ?>
                                            <br><small class="text-muted">
                                                Atualizado por: <?php echo htmlspecialchars($usuario['atualizado_por']); ?>
                                            </small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td class="action-buttons">
                                    <button class="btn btn-primary btn-sm" onclick="editarPermissao(<?php echo $usuario['id']; ?>, '<?php echo htmlspecialchars($usuario['nome_completo']); ?>', '<?php echo $usuario['nivel_acesso'] ?? ''; ?>')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <?php if ($usuario['nivel_acesso']): ?>
                                        <button class="btn btn-danger btn-sm" onclick="removerPermissao(<?php echo $usuario['id']; ?>, '<?php echo htmlspecialchars($usuario['nome_completo']); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Paginação -->
        <?php if ($total_pages > 1): ?>
        <nav class="mt-4">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&nome=<?php echo $filtro_nome; ?>&setor=<?php echo $filtro_setor; ?>&nivel=<?php echo $filtro_nivel; ?>">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                <?php endif; ?>

                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?>&nome=<?php echo $filtro_nome; ?>&setor=<?php echo $filtro_setor; ?>&nivel=<?php echo $filtro_nivel; ?>">
                        <?php echo $i; ?>
                    </a>
                </li>
                <?php endfor; ?>

                <?php if ($page < $total_pages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&nome=<?php echo $filtro_nome; ?>&setor=<?php echo $filtro_setor; ?>&nivel=<?php echo $filtro_nivel; ?>">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
    </div>

    <!-- Modal Editar Permissão -->
    <div class="modal fade" id="editPermissionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Editar Permissão</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update_permission">
                        <input type="hidden" name="usuario_id" id="edit_usuario_id">

                        <div class="mb-3">
                            <label class="form-label">Usuário</label>
                            <input type="text" id="edit_usuario_nome" class="form-control" readonly>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Nível de Acesso</label>
                            <select name="nivel_acesso" id="edit_nivel_acesso" class="form-select" required>
                                <option value="">Selecione o nível...</option>
                                <option value="COMUM">Usuário</option>
                                <option value="GESTOR">Gestor</option>
                                <option value="ADMIN">Administrador</option>
                            </select>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Níveis de Acesso:</h6>
                            <ul class="mb-0">
                                <li><strong>Usuário:</strong> Acesso básico ao sistema</li>
                                <li><strong>Gestor:</strong> Acesso de supervisão e relatórios</li>
                                <li><strong>Administrador:</strong> Acesso total, incluindo gerenciamento de permissões</li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">Salvar</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Remover Permissão -->
    <div class="modal fade" id="removePermissionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">Remover Acesso</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="remove_permission">
                        <input type="hidden" name="usuario_id" id="remove_usuario_id">
                        <p>Remover acesso do usuário: <strong id="remove_usuario_nome"></strong>?</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-danger">Remover</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editarPermissao(usuarioId, nomeUsuario, nivelAtual) {
            document.getElementById('edit_usuario_id').value = usuarioId;
            document.getElementById('edit_usuario_nome').value = nomeUsuario;
            document.getElementById('edit_nivel_acesso').value = nivelAtual;
            new bootstrap.Modal(document.getElementById('editPermissionModal')).show();
        }

        function removerPermissao(usuarioId, nomeUsuario) {
            document.getElementById('remove_usuario_id').value = usuarioId;
            document.getElementById('remove_usuario_nome').textContent = nomeUsuario;
            new bootstrap.Modal(document.getElementById('removePermissionModal')).show();
        }
    </script>
</body>
</html>
