<?php
/**
 * Análise de Divergência - Cursos Vencidos
 * 
 * Investigar por que o card mostra cursos vencidos mas a seção de trilhas não mostra.
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

// CPF do colaborador para análise
$cpf_teste = '14692053607'; // CPF normalizado sem pontuação

echo "<h1>🔍 Análise de Divergência - Cursos Vencidos</h1>";
echo "<p><strong>CPF Analisado:</strong> 146.920.536-07 (normalizado: $cpf_teste)</p>";

// Análise 1: Consulta dos cards (estatísticas)
echo "<h2>1. 📊 Consulta dos Cards (Estatísticas)</h2>";

try {
    $query_cards = "
        SELECT 
            cpf, usuario,
            COUNT(DISTINCT trilha) as total_trilhas,
            COUNT(DISTINCT recurso) as total_cursos,
            SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as cursos_aprovados,
            COUNT(CASE WHEN validade_recurso IS NOT NULL 
                       AND validade_recurso != '0000-00-00' 
                       AND validade_recurso <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) 
                       AND validade_recurso >= CURDATE() 
                  THEN 1 END) as cursos_a_vencer,
            COUNT(CASE WHEN validade_recurso IS NOT NULL 
                       AND validade_recurso != '0000-00-00' 
                       AND validade_recurso < CURDATE() 
                  THEN 1 END) as cursos_vencidos,
            AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento
        FROM edu_relatorio_educacao
        WHERE cpf = ?
        GROUP BY cpf, usuario
    ";
    
    $stmt_cards = $pdo_edu->prepare($query_cards);
    $stmt_cards->execute([$cpf_teste]);
    $resultado_cards = $stmt_cards->fetch();
    
    if ($resultado_cards) {
        echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>📈 Resultado da Consulta dos Cards</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>Métrica</th>";
        echo "<th style='padding: 10px;'>Valor</th>";
        echo "</tr>";
        echo "<tr><td style='padding: 10px;'><strong>Total Trilhas</strong></td><td style='padding: 10px;'>" . $resultado_cards['total_trilhas'] . "</td></tr>";
        echo "<tr><td style='padding: 10px;'><strong>Total Cursos</strong></td><td style='padding: 10px;'>" . $resultado_cards['total_cursos'] . "</td></tr>";
        echo "<tr><td style='padding: 10px;'><strong>Cursos Aprovados</strong></td><td style='padding: 10px;'>" . $resultado_cards['cursos_aprovados'] . "</td></tr>";
        echo "<tr><td style='padding: 10px;'><strong>Cursos A Vencer</strong></td><td style='padding: 10px;'>" . $resultado_cards['cursos_a_vencer'] . "</td></tr>";
        echo "<tr style='background: #f8d7da;'><td style='padding: 10px;'><strong>Cursos Vencidos</strong></td><td style='padding: 10px; font-weight: bold; color: #dc3545;'>" . $resultado_cards['cursos_vencidos'] . "</td></tr>";
        echo "</table>";
        echo "</div>";
        
        if ($resultado_cards['cursos_vencidos'] > 0) {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p>🚨 <strong>DIVERGÊNCIA CONFIRMADA:</strong> Cards mostram {$resultado_cards['cursos_vencidos']} curso(s) vencido(s)</p>";
            echo "</div>";
        }
        
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>⚠️ <strong>Colaborador não encontrado na consulta dos cards</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro na consulta dos cards:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Análise 2: Consulta detalhada dos cursos
echo "<h2>2. 📋 Consulta Detalhada dos Cursos</h2>";

try {
    $query_cursos = "
        SELECT
            trilha, codigo_trilha, recurso, codigo_recurso,
            aprovacao, nota_recurso, aproveitamento, carga_horaria_recurso,
            data_conclusao, validade_recurso, andamento_etapa,
            concluir_trilha_ate, data_admissao,
            CASE 
                WHEN validade_recurso IS NULL OR validade_recurso = '0000-00-00' THEN 'SEM_VALIDADE'
                WHEN validade_recurso < CURDATE() THEN 'VENCIDO'
                WHEN validade_recurso <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN 'A_VENCER'
                ELSE 'EM_DIA'
            END as status_validade,
            DATEDIFF(CURDATE(), validade_recurso) as dias_vencimento
        FROM edu_relatorio_educacao
        WHERE cpf = ?
        ORDER BY validade_recurso, trilha, recurso
    ";
    
    $stmt_cursos = $pdo_edu->prepare($query_cursos);
    $stmt_cursos->execute([$cpf_teste]);
    $cursos_detalhados = $stmt_cursos->fetchAll();
    
    if (!empty($cursos_detalhados)) {
        echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>📚 Cursos Detalhados (" . count($cursos_detalhados) . " encontrados)</h3>";
        
        // Agrupar por status
        $por_status = [];
        foreach ($cursos_detalhados as $curso) {
            $por_status[$curso['status_validade']][] = $curso;
        }
        
        foreach (['VENCIDO', 'A_VENCER', 'EM_DIA', 'SEM_VALIDADE'] as $status) {
            if (isset($por_status[$status])) {
                $cor = [
                    'VENCIDO' => '#dc3545',
                    'A_VENCER' => '#ffc107', 
                    'EM_DIA' => '#28a745',
                    'SEM_VALIDADE' => '#6c757d'
                ][$status];
                
                echo "<h4 style='color: $cor;'>$status (" . count($por_status[$status]) . " cursos)</h4>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px; margin-bottom: 20px;'>";
                echo "<tr style='background: #f8f9fa;'>";
                echo "<th style='padding: 5px;'>Trilha</th>";
                echo "<th style='padding: 5px;'>Curso</th>";
                echo "<th style='padding: 5px;'>Validade</th>";
                echo "<th style='padding: 5px;'>Dias</th>";
                echo "<th style='padding: 5px;'>Conclusão</th>";
                echo "<th style='padding: 5px;'>Aprovação</th>";
                echo "</tr>";
                
                foreach ($por_status[$status] as $curso) {
                    echo "<tr>";
                    echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($curso['trilha'], 0, 30)) . "...</td>";
                    echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($curso['recurso'], 0, 30)) . "...</td>";
                    echo "<td style='padding: 5px;'>" . ($curso['validade_recurso'] && $curso['validade_recurso'] !== '0000-00-00' ? date('d/m/Y', strtotime($curso['validade_recurso'])) : 'N/A') . "</td>";
                    echo "<td style='padding: 5px; text-align: center;'>" . ($curso['dias_vencimento'] !== null ? $curso['dias_vencimento'] : 'N/A') . "</td>";
                    echo "<td style='padding: 5px;'>" . ($curso['data_conclusao'] && $curso['data_conclusao'] !== '0000-00-00' ? date('d/m/Y', strtotime($curso['data_conclusao'])) : 'N/A') . "</td>";
                    echo "<td style='padding: 5px;'>" . htmlspecialchars($curso['aprovacao'] ?: 'N/A') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
        echo "</div>";
        
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>⚠️ <strong>Nenhum curso encontrado para este colaborador</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro na consulta detalhada:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Análise 3: Comparar com lógica do modal
echo "<h2>3. 🔄 Comparação com Lógica do Modal</h2>";

try {
    // Buscar configurações de prazos personalizados
    $stmt_prazos = $pdo_edu->query("SELECT * FROM edu_prazos_personalizados WHERE prazo_personalizado_ativo = 1");
    $prazos_config = [];
    foreach ($stmt_prazos->fetchAll(PDO::FETCH_ASSOC) as $config) {
        $key = $config['codigo_trilha'] . '|' . $config['codigo_recurso'];
        $prazos_config[$key] = $config;
    }
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>🔄 Simulação da Lógica do Modal</h3>";
    
    if (!empty($cursos_detalhados)) {
        echo "<p><strong>Aplicando a mesma lógica usada no modal de detalhes...</strong></p>";
        
        $cursos_processados = [];
        foreach ($cursos_detalhados as $curso) {
            $key = $curso['codigo_trilha'] . '|' . $curso['codigo_recurso'];
            
            // Aplicar lógica do modal
            if (isset($prazos_config[$key])) {
                // Curso com prazo personalizado
                $data_corte = '2023-01-01';
                $elegivel_prazo_personalizado = ($curso['data_admissao'] > $data_corte);
                
                if ($elegivel_prazo_personalizado) {
                    // Simular cálculo de prazo personalizado
                    $config = $prazos_config[$key];
                    $tem_conclusao = !empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00';
                    $renovacao_prazo = !empty($config['renovacao_prazo_dias']) ? (int)$config['renovacao_prazo_dias'] : 0;
                    
                    if ($tem_conclusao && $renovacao_prazo <= 0) {
                        $curso['prazo_calculado'] = null;
                        $curso['motivo_prazo'] = 'Curso concluído (sem renovação)';
                        $curso['status_final'] = 'CONCLUIDO_SEM_RENOVACAO';
                    } else {
                        $curso['prazo_calculado'] = $curso['concluir_trilha_ate']; // Simplificado
                        $curso['motivo_prazo'] = 'Prazo personalizado aplicado';
                        $curso['status_final'] = 'COM_PRAZO_PERSONALIZADO';
                    }
                } else {
                    $curso['prazo_calculado'] = $curso['concluir_trilha_ate'];
                    $curso['motivo_prazo'] = 'Prazo padrão (admitido antes de 01/01/2023)';
                    $curso['status_final'] = 'PRAZO_PADRAO';
                }
            } else {
                // Curso sem prazo personalizado
                if (!empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00') {
                    $curso['prazo_calculado'] = null;
                    $curso['motivo_prazo'] = 'Curso concluído (sem renovação)';
                    $curso['status_final'] = 'CONCLUIDO_SEM_RENOVACAO';
                } else {
                    $curso['prazo_calculado'] = $curso['concluir_trilha_ate'];
                    $curso['motivo_prazo'] = 'Prazo padrão (sem configuração personalizada)';
                    $curso['status_final'] = 'PRAZO_PADRAO';
                }
            }
            
            $cursos_processados[] = $curso;
        }
        
        // Mostrar resultados processados
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 5px;'>Curso</th>";
        echo "<th style='padding: 5px;'>Status Validade</th>";
        echo "<th style='padding: 5px;'>Status Final Modal</th>";
        echo "<th style='padding: 5px;'>Prazo Calculado</th>";
        echo "<th style='padding: 5px;'>Motivo</th>";
        echo "</tr>";
        
        foreach ($cursos_processados as $curso) {
            $cor_status = [
                'VENCIDO' => '#dc3545',
                'A_VENCER' => '#ffc107',
                'EM_DIA' => '#28a745',
                'SEM_VALIDADE' => '#6c757d'
            ][$curso['status_validade']] ?? '#6c757d';
            
            echo "<tr>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($curso['recurso'], 0, 25)) . "...</td>";
            echo "<td style='padding: 5px; color: $cor_status; font-weight: bold;'>" . $curso['status_validade'] . "</td>";
            echo "<td style='padding: 5px;'>" . $curso['status_final'] . "</td>";
            echo "<td style='padding: 5px;'>" . ($curso['prazo_calculado'] ? date('d/m/Y', strtotime($curso['prazo_calculado'])) : 'NULL') . "</td>";
            echo "<td style='padding: 5px; font-size: 10px;'>" . htmlspecialchars(substr($curso['motivo_prazo'], 0, 30)) . "...</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro na simulação:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Análise 4: Possíveis causas da divergência
echo "<h2>4. 🔍 Possíveis Causas da Divergência</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>⚠️ Hipóteses para a Divergência</h3>";

echo "<h4>1. Diferença nas Consultas:</h4>";
echo "<ul>";
echo "<li><strong>Cards:</strong> Usam campo 'validade_recurso' diretamente</li>";
echo "<li><strong>Modal:</strong> Aplicam lógica de prazos personalizados e novas regras</li>";
echo "<li><strong>Possível Causa:</strong> Campo 'validade_recurso' pode estar desatualizado</li>";
echo "</ul>";

echo "<h4>2. Novas Regras de Negócio:</h4>";
echo "<ul>";
echo "<li><strong>Cursos Concluídos:</strong> Modal pode estar marcando como 'sem prazo'</li>";
echo "<li><strong>Sem Renovação:</strong> Cursos concluídos sem renovação não aparecem como vencidos</li>";
echo "<li><strong>Cards Antigos:</strong> Podem não estar aplicando as novas regras</li>";
echo "</ul>";

echo "<h4>3. Sincronização de Dados:</h4>";
echo "<ul>";
echo "<li><strong>Campo 'validade_recurso':</strong> Pode não estar sendo atualizado</li>";
echo "<li><strong>Cálculos Diferentes:</strong> Cards vs Modal usando lógicas diferentes</li>";
echo "<li><strong>Cache:</strong> Dados podem estar em cache desatualizado</li>";
echo "</ul>";
echo "</div>";

// Recomendações
echo "<h2>5. 💡 Recomendações</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>✅ Ações Recomendadas</h3>";

echo "<h4>1. Atualizar Consulta dos Cards:</h4>";
echo "<ul>";
echo "<li>Aplicar a mesma lógica de prazos personalizados nos cards</li>";
echo "<li>Considerar cursos concluídos sem renovação</li>";
echo "<li>Usar prazos calculados em vez de 'validade_recurso'</li>";
echo "</ul>";

echo "<h4>2. Sincronizar Lógicas:</h4>";
echo "<ul>";
echo "<li>Unificar cálculo de prazos entre cards e modal</li>";
echo "<li>Criar função centralizada para cálculo de status</li>";
echo "<li>Aplicar novas regras de negócio em ambos os locais</li>";
echo "</ul>";

echo "<h4>3. Verificar Dados:</h4>";
echo "<ul>";
echo "<li>Validar se 'validade_recurso' está sendo atualizado</li>";
echo "<li>Verificar se prazos personalizados estão sendo aplicados</li>";
echo "<li>Confirmar se cursos concluídos estão sendo tratados corretamente</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Análise executada em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='detalhes_colaborador.php?cpf=14692053607' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>👤 Ver Modal do Colaborador</a>";
echo "<a href='analise_colaboradores.php?aba=colaboradores' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>📊 Ver Cards</a>";
echo "</p>";
?>
