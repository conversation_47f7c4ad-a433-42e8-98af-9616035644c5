-- <PERSON><PERSON><PERSON> para criar tabela de prazos personalizados
USE sicoob_access_control;

-- Tabela para configurações de prazos personalizados por curso
CREATE TABLE IF NOT EXISTS edu_prazos_personalizados (
    id INT AUTO_INCREMENT PRIMARY KEY,
    codigo_trilha VARCHAR(50) NOT NULL,
    trilha VARCHAR(255) NOT NULL,
    codigo_recurso VARCHAR(50) NOT NULL,
    recurso VARCHAR(255) NOT NULL,
    prazo_personalizado_ativo BOOLEAN DEFAULT FALSE,
    primeiro_prazo_dias INT NULL COMMENT 'Prazo em dias para quem nunca fez o curso',
    renovacao_prazo_dias INT NULL COMMENT 'Prazo em dias para renovação (quem já fez)',
    usuario_criacao INT NOT NULL,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    usuario_atualizacao INT NULL,
    data_atualizacao TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_curso (codigo_trilha, codigo_recurso),
    INDEX idx_trilha (codigo_trilha),
    INDEX idx_recurso (codigo_recurso),
    INDEX idx_ativo (prazo_personalizado_ativo),
    
    FOREIGN KEY (usuario_criacao) REFERENCES usuarios(id),
    FOREIGN KEY (usuario_atualizacao) REFERENCES usuarios(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela para log de alterações de prazos
CREATE TABLE IF NOT EXISTS edu_log_prazos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    prazo_id INT NOT NULL,
    acao ENUM('criado', 'ativado', 'desativado', 'atualizado') NOT NULL,
    valores_anteriores JSON NULL,
    valores_novos JSON NULL,
    usuario_id INT NOT NULL,
    data_acao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_prazo (prazo_id),
    INDEX idx_usuario (usuario_id),
    INDEX idx_data (data_acao),
    
    FOREIGN KEY (prazo_id) REFERENCES edu_prazos_personalizados(id) ON DELETE CASCADE,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
