# Instruções de Uso - Sistema de Educação Corporativa

## 🚀 Como Começar

### 1. Acesso ao Si<PERSON>
- Acesse: `http://localhost/d/rh/educacao-corporativa/`
- Faça login com suas credenciais do sistema principal
- O sistema herda as permissões do ecossistema 'd'

### 2. Primeira Utilização
- Se for a primeira vez, execute a instalação através de: `install.php`
- Ou execute o script: `php run_install.php`
- Verifique se as tabelas foram criadas corretamente

## 📁 Importação de Dados

### Preparação do Arquivo CSV

1. **Formato do Arquivo**:
   - Extensão: `.csv`
   - Codificação: UTF-8, ISO-8859-1 ou Windows-1252 (detectada automaticamente)
   - Separador: vírgula (,) ou ponto e vírgula (;)
   - Tamanho máximo: 10MB
   - **Acentos e caracteres especiais são tratados automaticamente**

2. **Estrutura das Colunas** (34 colunas obrigatórias):
   ```
   1. Código da Unidade
   2. Hierarquia da Unidade
   3. Usuário
   4. Identificador
   5. Situação do Usuário
   6. CPF (obrigatório)
   7. E-mail
   8. Data de Admissão
   9. Função
   10. Superior imediato
   11. Tipo de trilha
   12. Etapa
   13. Código da Trilha
   14. Trilha
   15. Aprovado na Trilha
   16. Aproveitamento
   17. Situação da Trilha
   18. Iniciar trilha em
   19. Concluir trilha até
   20. Data de aprovação na trilha
   21. Carga Horária da Trilha
   22. Prazo da etapa da trilha jornada
   23. Andamento da etapa
   24. Total de horas essenciais da etapa
   25. Horas essenciais feitas
   26. Horas complementares feitas
   27. Código do Recurso
   28. Recurso
   29. Nota do Recurso
   30. Aprovação
   31. Carga Horária do Recurso
   32. Data da Conclusão
   33. Validade Recurso
   34. Responsável pela Associação
   ```

### Processo de Importação

1. **Acesse a página de importação**:
   - Clique em "Importar Relatório" no menu
   - Ou acesse diretamente: `importar.php`

2. **Upload do Arquivo**:
   - Arraste o arquivo CSV para a área de upload
   - Ou clique para selecionar o arquivo
   - Aguarde a validação automática

3. **Processamento**:
   - Clique em "Importar Arquivo"
   - Aguarde o processamento (pode levar alguns minutos)
   - Verifique o resultado da importação

## ⚠️ Pontos Importantes

### Tratamento de Dados

1. **CPF**:
   - Será automaticamente formatado com zeros à esquerda
   - Exemplo: `12345678901` ou `012345678901`
   - Campo obrigatório para importação

2. **Datas**:
   - Formatos aceitos: `dd/mm/aaaa`, `dd-mm-aaaa`, `aaaa-mm-dd`
   - Campos vazios são permitidos
   - Exemplo: `15/06/2024` ou `2024-06-15`

3. **Valores Numéricos**:
   - Aceita formato brasileiro: `85,5` ou `85.5`
   - Remove caracteres não numéricos automaticamente

4. **Carga Horária**:
   - Aceita formato hh:mm: `10:25`, `08:30`, `15:45`
   - Converte automaticamente números: `1025` vira `10:25`, `830` vira `8:30`
   - Aplica-se às colunas "Carga Horária da Trilha" e "Carga Horária do Recurso"

5. **Acentos e Caracteres Especiais**:
   - Detecta automaticamente a codificação do arquivo (UTF-8, ISO-8859-1, Windows-1252)
   - Converte automaticamente para UTF-8 antes de salvar no banco
   - Palavras como "Comunicação", "Negociação", "Organização" são preservadas corretamente

### Limpeza de Dados

⚠️ **ATENÇÃO**: A cada nova importação, **TODOS** os dados anteriores são removidos e substituídos pelos novos dados do arquivo CSV.

- Certifique-se de que o arquivo contém todos os dados necessários
- Mantenha backup dos dados importantes
- O histórico de importações é preservado na tabela de controle

## 📊 Monitoramento

### Logs de Importação
- Todas as importações são registradas
- Status: Processando, Concluído, Erro
- Detalhes de erros são armazenados
- Contadores de registros processados

### Estatísticas
- Total de registros no sistema
- Número de importações realizadas
- Data da última importação

## 🔧 Solução de Problemas

### Erros Comuns

1. **"CPF é obrigatório"**:
   - Verifique se a coluna CPF está preenchida
   - Remova linhas com CPF vazio

2. **"Arquivo muito grande"**:
   - Divida o arquivo em partes menores
   - Limite: 10MB por arquivo

3. **"Tipo de arquivo não permitido"**:
   - Certifique-se de que o arquivo tem extensão `.csv`
   - Converta de Excel para CSV se necessário

4. **Erro de formato de data**:
   - Use formato brasileiro: `dd/mm/aaaa`
   - Ou formato ISO: `aaaa-mm-dd`

### Verificação de Dados

1. **Antes da Importação**:
   - Conte o número de colunas (deve ser 34)
   - Verifique se há cabeçalho
   - Confirme que CPFs estão preenchidos

2. **Após a Importação**:
   - Verifique o número de registros importados
   - Analise os erros reportados
   - Confirme se os dados estão corretos

## 📞 Suporte

### Arquivo de Exemplo
- Use o arquivo `exemplo_importacao.csv` como referência
- Contém 5 registros de exemplo com todos os campos
- Estrutura correta das colunas

### Logs do Sistema
- Logs são armazenados em: `logs/`
- Erros detalhados para diagnóstico
- Histórico de operações

### Contato
- Para suporte técnico, consulte a equipe de TI
- Documentação completa em: `README.md`
- Versão do sistema: 1.0.0

---

## 📝 Exemplo de Uso Rápido

1. Baixe o arquivo `exemplo_importacao.csv`
2. Acesse `importar.php`
3. Faça upload do arquivo de exemplo
4. Clique em "Importar Arquivo"
5. Verifique os resultados na página principal

**Pronto!** O sistema está funcionando e você pode começar a usar com seus próprios dados.
