# Sistema de Educação Corporativa

Sistema para gerenciamento e importação de dados de educação corporativa da organização.

## Características

- **Importação de Relatórios CSV**: Interface para upload e processamento de arquivos CSV com dados de educação corporativa
- **Tratamento Automático de CPF**: Adiciona zeros à esquerda automaticamente quando necessário
- **Limpeza de Dados**: A cada importação, os dados anteriores são removidos e substituídos pelos novos
- **Validação de Dados**: Validação de formatos de data, CPF e valores numéricos
- **Interface Responsiva**: Design moderno seguindo a identidade visual Sicoob
- **Controle de Importações**: Histórico e status de todas as importações realizadas
- **Integração com API Intranet**: Conexão em tempo real com dados de usuários e agências
- **Sistema de Prazos Personalizados**: Configuração de prazos específicos por curso
- **Cache Inteligente**: Sistema de cache para otimização de performance da API
- **Análise de Colaboradores**: Dashboard completo para análise individual e coletiva dos colaboradores
- **Exportação de Dados**: Exportação completa dos dados com integração da Intranet

## Estrutura do Projeto

```
rh/educacao-corporativa/
├── config/
│   ├── config.php          # Configurações específicas do projeto
│   └── database.php        # Configuração de banco de dados
├── classes/
│   └── IntranetAPI.php     # Classe para integração com API da Intranet
├── sql/
│   ├── create_tables.sql   # Script de criação das tabelas
│   └── install.sql         # Script de instalação completo
├── uploads/                # Diretório para arquivos temporários
├── logs/                   # Diretório para logs do sistema
├── cache/                  # Diretório para cache da API
├── auth_check.php          # Verificação de autenticação
├── index.php               # Página principal
├── importar.php            # Interface de importação
├── analise_colaboradores.php # Análise detalhada de colaboradores
├── detalhes_colaborador.php  # Modal de detalhes (AJAX)
├── exportar_colaboradores.php # Exportação de dados
├── gerenciar_trilhas.php   # Gerenciamento de trilhas e prazos
├── test_api.php            # Página de testes da API (admin)
└── README.md               # Este arquivo
```

## Instalação

1. **Executar Script de Instalação**:
   ```sql
   -- Execute no banco sicoob_access_control
   SOURCE rh/educacao-corporativa/sql/install.sql;
   ```

2. **Verificar Permissões**:
   - Certifique-se de que o usuário tem acesso ao sistema principal
   - As permissões são herdadas do sistema de autenticação principal

3. **Configurar Diretórios**:
   - Os diretórios `uploads/` e `logs/` são criados automaticamente
   - Verifique se o servidor web tem permissão de escrita

## Formato do Arquivo CSV

O arquivo CSV deve conter as seguintes colunas na ordem especificada:

1. Código da Unidade
2. Hierarquia da Unidade
3. Usuário
4. Identificador
5. Situação do Usuário
6. CPF
7. E-mail
8. Data de Admissão
9. Função
10. Superior imediato
11. Tipo de trilha
12. Etapa
13. Código da Trilha
14. Trilha
15. Aprovado na Trilha
16. Aproveitamento
17. Situação da Trilha
18. Iniciar trilha em
19. Concluir trilha até
20. Data de aprovação na trilha
21. Carga Horária da Trilha
22. Prazo da etapa da trilha jornada
23. Andamento da etapa
24. Total de horas essenciais da etapa
25. Horas essenciais feitas
26. Horas complementares feitas
27. Código do Recurso
28. Recurso
29. Nota do Recurso
30. Aprovação
31. Carga Horária do Recurso
32. Data da Conclusão
33. Validade Recurso
34. Responsável pela Associação

## Tratamento de Dados

### CPF
- Automaticamente formatado com zeros à esquerda até 11 dígitos
- Caracteres não numéricos são removidos
- Campo obrigatório para importação

### Datas
- Aceita formatos: dd/mm/aaaa, dd-mm-aaaa, aaaa-mm-dd
- Convertidas automaticamente para formato MySQL (aaaa-mm-dd)
- Campos de data vazios são aceitos

### Valores Numéricos
- Aceita formato brasileiro (vírgula como separador decimal)
- Remove caracteres não numéricos
- Converte automaticamente para formato decimal

## Banco de Dados

### Tabelas Criadas

#### edu_relatorio_educacao
Tabela principal que armazena todos os dados do relatório de educação corporativa.

#### edu_importacoes
Tabela de controle que registra todas as importações realizadas, incluindo:
- Nome do arquivo
- Tamanho do arquivo
- Total de registros processados
- Registros importados com sucesso
- Registros com erro
- Status da importação
- Detalhes de erros
- Data e usuário da importação

## Funcionalidades

### Importação
- Upload via drag-and-drop ou seleção de arquivo
- Validação de tipo e tamanho de arquivo
- Processamento em lotes para melhor performance
- Feedback em tempo real do progresso
- Relatório detalhado de erros

### Segurança
- Autenticação herdada do sistema principal
- Validação de permissões de usuário
- Logs de todas as operações
- Sanitização de dados de entrada

## Configurações

### Limites de Upload
- Tamanho máximo: 10MB
- Formato aceito: .csv
- Processamento em lotes de 100 registros

### Diretórios
- Uploads: `rh/educacao-corporativa/uploads/`
- Logs: `rh/educacao-corporativa/logs/`

## Manutenção

### Logs
Os logs do sistema são armazenados no diretório `logs/` e incluem:
- Erros de importação
- Problemas de conexão com banco
- Validações de dados

### Limpeza
- Arquivos temporários são removidos automaticamente após processamento
- Dados antigos são substituídos a cada nova importação
- Histórico de importações é mantido na tabela `edu_importacoes`

## Integração com API da Intranet

O sistema possui integração completa com a API da Intranet Sicoob:

### Funcionalidades da API
- **Busca de Usuários**: Lista todos os usuários da intranet
- **Busca de Agências**: Lista todas as agências
- **Busca Específica**: Localiza usuário por email ou agência por número
- **Cache Inteligente**: Sistema de cache de 1 hora para otimização
- **Logs Detalhados**: Monitoramento completo das chamadas

### Como Usar
```php
// Criar instância da API
$api = new IntranetAPI();

// Buscar todos os usuários
$usuarios = $api->listarUsuarios();

// Buscar usuário específico
$usuario = $api->buscarUsuarioPorEmail('<EMAIL>');

// Buscar agência específica
$agencia = $api->buscarAgenciaPorNumero('001');
```

### Teste da API
- Acesse `test_api.php` (apenas administradores)
- Teste todas as funcionalidades da API
- Visualize dados em tempo real
- Monitore performance e cache

Para documentação completa da API, consulte: `API_INTRANET.md`

## Análise de Colaboradores

O sistema possui uma funcionalidade completa de análise de colaboradores que integra dados do sistema com a API da Intranet:

### Funcionalidades da Análise
- **Dashboard de Estatísticas**: Métricas consolidadas em tempo real
- **Filtros Avançados**: Busca por CPF, nome, trilha, situação, período
- **Integração por CPF**: Relacionamento automático com dados da Intranet
- **Visualização Detalhada**: Cards responsivos com informações consolidadas
- **Modal de Detalhes**: Popup com informações completas via AJAX
- **Exportação Excel**: Download dos dados filtrados com informações da Intranet

### Como Usar
1. Acesse `analise_colaboradores.php`
2. Use os filtros para segmentar os dados
3. Visualize colaboradores em cards responsivos
4. Clique em "Ver Detalhes" para informações completas
5. Use "Exportar" para baixar dados em Excel

### Dados Exibidos
- **Sistema**: Trilhas, cursos, aprovações, notas, datas
- **Intranet**: Nome completo, agência, setor, função, status
- **Métricas**: Progresso, aproveitamento, estatísticas consolidadas

Para documentação completa da análise, consulte: `ANALISE_COLABORADORES.md`

## Suporte

Para suporte técnico ou dúvidas sobre o sistema, consulte a documentação do ecossistema principal ou entre em contato com a equipe de TI.

## Versão

Sistema de Educação Corporativa v1.0.0
Parte do ecossistema 'd' - Sicoob
