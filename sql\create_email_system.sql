-- Sistema de E-mails para Educação Corporativa
-- Criação das tabelas necessárias para envio de e-mails individuais, em massa e agendados

-- Tabela de templates de e-mail
CREATE TABLE IF NOT EXISTS edu_email_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    assunto VARCHAR(255) NOT NULL,
    corpo_html TEXT NOT NULL,
    corpo_texto TEXT NOT NULL,
    tipo ENUM('a_vencer', 'vencidos', 'personalizado') NOT NULL,
    ativo BOOLEAN DEFAULT TRUE,
    variaveis_disponiveis JSON,
    usuario_criacao INT NOT NULL,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    usuario_atualizacao INT,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_tipo (tipo),
    INDEX idx_ativo (ativo),
    INDEX idx_usuario_criacao (usuario_criacao),
    
    FOREIGN KEY (usuario_criacao) REFERENCES usuarios(id),
    FOREIGN KEY (usuario_atualizacao) REFERENCES usuarios(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de agendamentos de e-mail
CREATE TABLE IF NOT EXISTS edu_email_agendamentos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    descricao TEXT,
    template_id INT NOT NULL,
    tipo_destinatario ENUM('a_vencer', 'vencidos', 'todos', 'filtro_personalizado') NOT NULL,
    filtros_json JSON,
    frequencia ENUM('unico', 'diario', 'semanal', 'quinzenal', 'mensal') NOT NULL,
    dia_semana TINYINT NULL COMMENT '1=Segunda, 7=Domingo',
    dia_mes TINYINT NULL COMMENT '1-31',
    hora_envio TIME NOT NULL DEFAULT '09:00:00',
    data_inicio DATE NOT NULL,
    data_fim DATE NULL,
    ativo BOOLEAN DEFAULT TRUE,
    ultima_execucao TIMESTAMP NULL,
    proxima_execucao TIMESTAMP NULL,
    total_envios INT DEFAULT 0,
    total_sucessos INT DEFAULT 0,
    total_erros INT DEFAULT 0,
    usuario_criacao INT NOT NULL,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    usuario_atualizacao INT,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_ativo (ativo),
    INDEX idx_proxima_execucao (proxima_execucao),
    INDEX idx_tipo_destinatario (tipo_destinatario),
    INDEX idx_frequencia (frequencia),
    INDEX idx_usuario_criacao (usuario_criacao),
    
    FOREIGN KEY (template_id) REFERENCES edu_email_templates(id),
    FOREIGN KEY (usuario_criacao) REFERENCES usuarios(id),
    FOREIGN KEY (usuario_atualizacao) REFERENCES usuarios(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de envios de e-mail (log)
CREATE TABLE IF NOT EXISTS edu_email_envios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    agendamento_id INT NULL,
    template_id INT NOT NULL,
    destinatario_cpf VARCHAR(11) NOT NULL,
    destinatario_nome VARCHAR(255) NOT NULL,
    destinatario_email VARCHAR(255) NOT NULL,
    assunto VARCHAR(255) NOT NULL,
    corpo_html TEXT NOT NULL,
    tipo_envio ENUM('manual', 'agendado') NOT NULL,
    status ENUM('pendente', 'enviado', 'erro', 'cancelado') NOT NULL DEFAULT 'pendente',
    erro_detalhes TEXT NULL,
    data_agendamento TIMESTAMP NULL,
    data_envio TIMESTAMP NULL,
    tentativas INT DEFAULT 0,
    usuario_envio INT NOT NULL,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_status (status),
    INDEX idx_destinatario_cpf (destinatario_cpf),
    INDEX idx_tipo_envio (tipo_envio),
    INDEX idx_data_agendamento (data_agendamento),
    INDEX idx_data_envio (data_envio),
    INDEX idx_agendamento (agendamento_id),
    INDEX idx_template (template_id),
    INDEX idx_usuario_envio (usuario_envio),
    
    FOREIGN KEY (agendamento_id) REFERENCES edu_email_agendamentos(id) ON DELETE SET NULL,
    FOREIGN KEY (template_id) REFERENCES edu_email_templates(id),
    FOREIGN KEY (usuario_envio) REFERENCES usuarios(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de filas de e-mail para processamento em lote
CREATE TABLE IF NOT EXISTS edu_email_fila (
    id INT AUTO_INCREMENT PRIMARY KEY,
    envio_id INT NOT NULL,
    prioridade TINYINT DEFAULT 5 COMMENT '1=Alta, 5=Normal, 9=Baixa',
    tentativas INT DEFAULT 0,
    max_tentativas INT DEFAULT 3,
    data_agendamento TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_processamento TIMESTAMP NULL,
    status ENUM('pendente', 'processando', 'enviado', 'erro', 'cancelado') NOT NULL DEFAULT 'pendente',
    erro_detalhes TEXT NULL,
    
    INDEX idx_status (status),
    INDEX idx_prioridade (prioridade),
    INDEX idx_data_agendamento (data_agendamento),
    INDEX idx_tentativas (tentativas),
    
    FOREIGN KEY (envio_id) REFERENCES edu_email_envios(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Inserir templates padrão
INSERT INTO edu_email_templates (nome, assunto, corpo_html, corpo_texto, tipo, variaveis_disponiveis, usuario_criacao) VALUES
('Cursos A Vencer - Padrão', 'Atenção: Cursos com prazo próximo ao vencimento', 
'<html><body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
<div style="max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #00AE9D, #C9D200); padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 20px;">
        <h1 style="color: white; margin: 0;">🎓 Sicoob Educação Corporativa</h1>
    </div>
    
    <h2 style="color: #2C5530;">Olá, {{nome_colaborador}}!</h2>
    
    <p>Esperamos que você esteja bem! Estamos entrando em contato para lembrá-lo sobre cursos que estão próximos ao vencimento.</p>
    
    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;">
        <h3 style="color: #856404; margin-top: 0;">⏰ Cursos A Vencer</h3>
        <p style="margin-bottom: 0;">Você possui <strong>{{total_cursos_a_vencer}}</strong> curso(s) que vencem em breve. É importante concluí-los dentro do prazo estabelecido.</p>
    </div>
    
    <p><strong>Seus dados:</strong></p>
    <ul>
        <li><strong>CPF:</strong> {{cpf_colaborador}}</li>
        <li><strong>E-mail:</strong> {{email_colaborador}}</li>
        <li><strong>Função:</strong> {{funcao_colaborador}}</li>
        <li><strong>PA:</strong> {{pa_colaborador}}</li>
    </ul>
    
    <p>Para acessar seus cursos e verificar os prazos, entre em contato com o setor responsável ou acesse o sistema de educação corporativa.</p>
    
    <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px; padding: 15px; margin: 20px 0;">
        <p style="margin: 0;"><strong>💡 Dica:</strong> Organize seu tempo e priorize a conclusão dos cursos com prazos mais próximos!</p>
    </div>
    
    <p>Em caso de dúvidas, entre em contato conosco.</p>
    
    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
    <p style="font-size: 12px; color: #666; text-align: center;">
        Este é um e-mail automático do Sistema Sicoob de Educação Corporativa.<br>
        Enviado em {{data_envio}}
    </p>
</div>
</body></html>',
'Olá, {{nome_colaborador}}!

Esperamos que você esteja bem! Estamos entrando em contato para lembrá-lo sobre cursos que estão próximos ao vencimento.

CURSOS A VENCER
Você possui {{total_cursos_a_vencer}} curso(s) que vencem em breve. É importante concluí-los dentro do prazo estabelecido.

Seus dados:
- CPF: {{cpf_colaborador}}
- E-mail: {{email_colaborador}}
- Função: {{funcao_colaborador}}
- PA: {{pa_colaborador}}

Para acessar seus cursos e verificar os prazos, entre em contato com o setor responsável ou acesse o sistema de educação corporativa.

Em caso de dúvidas, entre em contato conosco.

---
Este é um e-mail automático do Sistema Sicoob de Educação Corporativa.
Enviado em {{data_envio}}',
'a_vencer',
'["nome_colaborador", "cpf_colaborador", "email_colaborador", "funcao_colaborador", "pa_colaborador", "total_cursos_a_vencer", "data_envio"]',
1),

('Cursos Vencidos - Padrão', 'URGENTE: Cursos com prazo vencido', 
'<html><body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
<div style="max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #dc3545, #c82333); padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 20px;">
        <h1 style="color: white; margin: 0;">🚨 Sicoob Educação Corporativa</h1>
    </div>
    
    <h2 style="color: #721c24;">Olá, {{nome_colaborador}}!</h2>
    
    <p>Estamos entrando em contato para informá-lo sobre cursos que estão com prazo vencido e precisam de atenção imediata.</p>
    
    <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
        <h3 style="color: #721c24; margin-top: 0;">🔴 Cursos Vencidos</h3>
        <p style="margin-bottom: 0;">Você possui <strong>{{total_cursos_vencidos}}</strong> curso(s) com prazo vencido. É necessário regularizar esta situação o quanto antes.</p>
    </div>
    
    <p><strong>Seus dados:</strong></p>
    <ul>
        <li><strong>CPF:</strong> {{cpf_colaborador}}</li>
        <li><strong>E-mail:</strong> {{email_colaborador}}</li>
        <li><strong>Função:</strong> {{funcao_colaborador}}</li>
        <li><strong>PA:</strong> {{pa_colaborador}}</li>
    </ul>
    
    <p><strong>Ação necessária:</strong> Entre em contato imediatamente com o setor responsável para regularizar a situação dos cursos vencidos.</p>
    
    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;">
        <p style="margin: 0;"><strong>⚠️ Importante:</strong> Cursos vencidos podem impactar sua avaliação e desenvolvimento profissional.</p>
    </div>
    
    <p>Para mais informações, entre em contato conosco urgentemente.</p>
    
    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
    <p style="font-size: 12px; color: #666; text-align: center;">
        Este é um e-mail automático do Sistema Sicoob de Educação Corporativa.<br>
        Enviado em {{data_envio}}
    </p>
</div>
</body></html>',
'Olá, {{nome_colaborador}}!

Estamos entrando em contato para informá-lo sobre cursos que estão com prazo vencido e precisam de atenção imediata.

CURSOS VENCIDOS
Você possui {{total_cursos_vencidos}} curso(s) com prazo vencido. É necessário regularizar esta situação o quanto antes.

Seus dados:
- CPF: {{cpf_colaborador}}
- E-mail: {{email_colaborador}}
- Função: {{funcao_colaborador}}
- PA: {{pa_colaborador}}

AÇÃO NECESSÁRIA: Entre em contato imediatamente com o setor responsável para regularizar a situação dos cursos vencidos.

IMPORTANTE: Cursos vencidos podem impactar sua avaliação e desenvolvimento profissional.

Para mais informações, entre em contato conosco urgentemente.

---
Este é um e-mail automático do Sistema Sicoob de Educação Corporativa.
Enviado em {{data_envio}}',
'vencidos',
'["nome_colaborador", "cpf_colaborador", "email_colaborador", "funcao_colaborador", "pa_colaborador", "total_cursos_vencidos", "data_envio"]',
1);
