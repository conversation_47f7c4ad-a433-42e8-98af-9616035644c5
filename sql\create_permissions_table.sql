-- <PERSON><PERSON>r tabela de permissões específica para Educação Corporativa
CREATE TABLE IF NOT EXISTS edu_permissoes_usuarios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT NOT NULL,
    nivel_acesso ENUM('ADMIN', 'GESTOR', 'COMUM') NOT NULL DEFAULT 'COMUM',
    ativo BOOLEAN DEFAULT TRUE,
    data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    usuario_criacao INT,
    usuario_atualizacao INT,
    
    UNIQUE KEY unique_usuario_ativo (usuario_id),
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id),
    FOREIGN KEY (usuario_criacao) REFERENCES usuarios(id),
    FOREIGN KEY (usuario_atualizacao) REFERENCES usuarios(id),
    
    INDEX idx_usuario_id (usuario_id),
    INDEX idx_nivel_acesso (nivel_acesso),
    INDEX idx_ativo (ativo)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Permissões específicas para o sistema de Educação Corporativa';

-- Inserir permissão de administrador para o usuário ID 10 (padrão do sistema)
INSERT INTO edu_permissoes_usuarios (usuario_id, nivel_acesso, usuario_criacao) 
VALUES (10, 'ADMIN', 10)
ON DUPLICATE KEY UPDATE 
    nivel_acesso = 'ADMIN',
    data_atualizacao = CURRENT_TIMESTAMP,
    usuario_atualizacao = 10;

-- Verificar se a tabela foi criada
DESCRIBE edu_permissoes_usuarios;

-- Mostrar dados inseridos
SELECT * FROM edu_permissoes_usuarios;

-- Mensagem de sucesso
SELECT 'Tabela edu_permissoes_usuarios criada com sucesso!' as status;
