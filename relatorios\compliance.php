<?php
// Relatório de Compliance
echo '<table border="1" style="border-collapse: collapse;">';
echo '<tr style="background-color: #dc3545; color: white; font-weight: bold;">';
echo '<td colspan="10" style="padding: 10px; text-align: center; font-size: 16px;">';
echo 'RELATÓRIO DE COMPLIANCE';
echo '</td>';
echo '</tr>';

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td style="padding: 8px;">Gerado em:</td>';
echo '<td colspan="9" style="padding: 8px;">' . date('d/m/Y H:i:s') . '</td>';
echo '</tr>';

// Buscar todos os colaboradores
$colaboradores_query = "SELECT DISTINCT cpf, usuario, funcao FROM edu_relatorio_educacao ORDER BY usuario";
$todos_colaboradores = $pdo_edu->query($colaboradores_query)->fetchAll();

$colaboradores_compliance = [];

foreach ($todos_colaboradores as $colaborador) {
    $cursos_colaborador = buscarCursosColaborador($colaborador['cpf'], $pdo_edu, $prazos_config);
    
    $total_cursos = count($cursos_colaborador);
    $cursos_aprovados = 0;
    $cursos_vencidos = 0;
    $cursos_a_vencer = 0;
    $cursos_obrigatorios_pendentes = 0;
    
    foreach ($cursos_colaborador as $curso) {
        if ($curso['aprovacao'] === 'Sim') {
            $cursos_aprovados++;
        } elseif ($curso['status_prazo'] === 'vencido') {
            $cursos_vencidos++;
            $cursos_obrigatorios_pendentes++;
        } elseif ($curso['status_prazo'] === 'a_vencer') {
            $cursos_a_vencer++;
        }
    }
    
    // Determinar status de compliance
    $status_compliance = 'Conforme';
    $cor_compliance = '#28a745';
    $risco = 'Baixo';
    
    if ($cursos_vencidos > 0) {
        $status_compliance = 'Não Conforme';
        $cor_compliance = '#dc3545';
        $risco = $cursos_vencidos > 3 ? 'Crítico' : 'Alto';
    } elseif ($cursos_a_vencer > 0) {
        $status_compliance = 'Atenção';
        $cor_compliance = '#fd7e14';
        $risco = $cursos_a_vencer > 2 ? 'Médio' : 'Baixo';
    }
    
    $percentual_conformidade = $total_cursos > 0 ? ($cursos_aprovados / $total_cursos) * 100 : 0;
    
    $colaborador['total_cursos'] = $total_cursos;
    $colaborador['cursos_aprovados'] = $cursos_aprovados;
    $colaborador['cursos_vencidos'] = $cursos_vencidos;
    $colaborador['cursos_a_vencer'] = $cursos_a_vencer;
    $colaborador['cursos_obrigatorios_pendentes'] = $cursos_obrigatorios_pendentes;
    $colaborador['percentual_conformidade'] = $percentual_conformidade;
    $colaborador['status_compliance'] = $status_compliance;
    $colaborador['cor_compliance'] = $cor_compliance;
    $colaborador['risco'] = $risco;
    
    $colaboradores_compliance[] = $colaborador;
}

// Ordenar por risco (crítico primeiro)
usort($colaboradores_compliance, function($a, $b) {
    $ordem_risco = ['Crítico' => 4, 'Alto' => 3, 'Médio' => 2, 'Baixo' => 1];
    return $ordem_risco[$b['risco']] - $ordem_risco[$a['risco']];
});

// Estatísticas gerais
$total_colaboradores = count($colaboradores_compliance);
$nao_conformes = count(array_filter($colaboradores_compliance, function($c) { return $c['status_compliance'] === 'Não Conforme'; }));
$atencao = count(array_filter($colaboradores_compliance, function($c) { return $c['status_compliance'] === 'Atenção'; }));
$conformes = count(array_filter($colaboradores_compliance, function($c) { return $c['status_compliance'] === 'Conforme'; }));

echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Total de Colaboradores:</td>';
echo '<td style="padding: 8px;">' . $total_colaboradores . '</td>';
echo '<td style="padding: 8px; font-weight: bold;">Conformes:</td>';
echo '<td style="padding: 8px; color: #28a745; font-weight: bold;">' . $conformes . ' (' . number_format(($conformes/$total_colaboradores)*100, 1) . '%)</td>';
echo '<td style="padding: 8px; font-weight: bold;">Atenção:</td>';
echo '<td style="padding: 8px; color: #fd7e14; font-weight: bold;">' . $atencao . ' (' . number_format(($atencao/$total_colaboradores)*100, 1) . '%)</td>';
echo '<td style="padding: 8px; font-weight: bold;">Não Conformes:</td>';
echo '<td colspan="3" style="padding: 8px; color: #dc3545; font-weight: bold;">' . $nao_conformes . ' (' . number_format(($nao_conformes/$total_colaboradores)*100, 1) . '%)</td>';
echo '</tr>';

echo '<tr><td colspan="10" style="padding: 5px;"></td></tr>';

// Cabeçalhos
echo '<tr style="background-color: #dc3545; color: white; font-weight: bold;">';
echo '<td style="padding: 8px;">CPF</td>';
echo '<td style="padding: 8px;">Nome</td>';
echo '<td style="padding: 8px;">Função</td>';
echo '<td style="padding: 8px; text-align: center;">Total Cursos</td>';
echo '<td style="padding: 8px; text-align: center;">Aprovados</td>';
echo '<td style="padding: 8px; text-align: center;">Vencidos</td>';
echo '<td style="padding: 8px; text-align: center;">A Vencer</td>';
echo '<td style="padding: 8px; text-align: center;">% Conformidade</td>';
echo '<td style="padding: 8px; text-align: center;">Status</td>';
echo '<td style="padding: 8px; text-align: center;">Risco</td>';
echo '</tr>';

// Dados
foreach ($colaboradores_compliance as $colaborador) {
    echo '<tr>';
    echo '<td style="padding: 6px;">' . formatarCpf($colaborador['cpf']) . '</td>';
    echo '<td style="padding: 6px;">' . htmlspecialchars($colaborador['usuario']) . '</td>';
    echo '<td style="padding: 6px;">' . htmlspecialchars($colaborador['funcao']) . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . $colaborador['total_cursos'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; color: #28a745;">' . $colaborador['cursos_aprovados'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; color: #dc3545; font-weight: bold;">' . $colaborador['cursos_vencidos'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; color: #fd7e14;">' . $colaborador['cursos_a_vencer'] . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . number_format($colaborador['percentual_conformidade'], 1) . '%</td>';
    echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: ' . $colaborador['cor_compliance'] . ';">' . $colaborador['status_compliance'] . '</td>';
    
    $cor_risco = '#28a745';
    if ($colaborador['risco'] === 'Crítico') $cor_risco = '#6f42c1';
    elseif ($colaborador['risco'] === 'Alto') $cor_risco = '#dc3545';
    elseif ($colaborador['risco'] === 'Médio') $cor_risco = '#fd7e14';
    
    echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: ' . $cor_risco . ';">' . $colaborador['risco'] . '</td>';
    echo '</tr>';
}

echo '</table>';
?>
