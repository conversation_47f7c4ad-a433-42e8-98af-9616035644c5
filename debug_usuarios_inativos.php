<?php
/**
 * Debug para identificar por que usuários inativos ainda aparecem na página principal
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/IntranetAPI.php';

// Criar instância da API
$api = new IntranetAPI();

echo "<h1>🐛 Debug: Usuários Inativos na Página Principal</h1>\n";

// Forçar limpeza de TODOS os caches
echo "<h2>1. 🧹 Limpeza Forçada de Cache</h2>\n";

$cache_files_removed = 0;
$cache_dir = EDU_API_CACHE_PATH;

if (is_dir($cache_dir)) {
    $files = glob($cache_dir . '*.json');
    foreach ($files as $file) {
        if (unlink($file)) {
            $cache_files_removed++;
        }
    }
}

echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>✅ Cache limpo:</strong> $cache_files_removed arquivos removidos</p>";
echo "</div>";

// Buscar dados da API sem cache
echo "<h2>2. 📊 Dados da API (SEM CACHE)</h2>\n";

$usuarios_intranet_todos = $api->listarUsuarios(false, false); // Todos os usuários, sem cache
$usuarios_intranet_ativos = $api->listarUsuariosAtivos(false); // Apenas ativos, sem cache

echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>📊 Estatísticas da API:</strong></p>";
echo "<ul>";
echo "<li><strong>Total de usuários:</strong> " . count($usuarios_intranet_todos) . "</li>";
echo "<li><strong>Usuários ativos:</strong> " . count($usuarios_intranet_ativos) . "</li>";
echo "<li><strong>Usuários inativos:</strong> " . (count($usuarios_intranet_todos) - count($usuarios_intranet_ativos)) . "</li>";
echo "</ul>";
echo "</div>";

// Criar mapas
$mapa_usuarios_ativos = [];
$mapa_usuarios_todos = [];

foreach ($usuarios_intranet_ativos as $usuario) {
    if (!empty($usuario['cpf'])) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
        $mapa_usuarios_ativos[$cpf_normalizado] = $usuario;
    }
}

foreach ($usuarios_intranet_todos as $usuario) {
    if (!empty($usuario['cpf'])) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
        $mapa_usuarios_todos[$cpf_normalizado] = $usuario;
    }
}

// Simular exatamente a lógica da página principal
echo "<h2>3. 🔍 Simulação da Lógica da Página Principal</h2>\n";

// Buscar colaboradores do banco
$colaboradores_query = "
    SELECT
        cpf,
        MAX(usuario) as usuario,
        COUNT(DISTINCT trilha) as total_trilhas,
        COUNT(DISTINCT CONCAT(codigo_trilha, '|', codigo_recurso)) as total_cursos
    FROM edu_relatorio_educacao
    GROUP BY cpf
    ORDER BY MAX(usuario)
    LIMIT 20
";

$stmt_colaboradores = $pdo_edu->prepare($colaboradores_query);
$stmt_colaboradores->execute();
$todos_colaboradores = $stmt_colaboradores->fetchAll();

echo "<div style='background: #e2e3e5; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>📋 Simulação do Agrupamento (primeiros 20):</strong></p>";
echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='padding: 5px;'>CPF</th>";
echo "<th style='padding: 5px;'>Nome</th>";
echo "<th style='padding: 5px;'>Status API</th>";
echo "<th style='padding: 5px;'>Ação</th>";
echo "<th style='padding: 5px;'>Resultado</th>";
echo "</tr>";

$colaboradores_processados = 0;
$colaboradores_filtrados = 0;
$colaboradores_exibidos = 0;

foreach ($todos_colaboradores as $colaborador) {
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    
    $usuario_intranet_ativo = $mapa_usuarios_ativos[$cpf_normalizado] ?? null;
    $usuario_intranet_todos = $mapa_usuarios_todos[$cpf_normalizado] ?? null;
    
    $status_api = 'Não encontrado';
    $acao = '';
    $resultado = '';
    $cor_linha = '';
    
    $colaboradores_processados++;
    
    if ($usuario_intranet_todos && !$usuario_intranet_ativo) {
        // Existe na Intranet mas está INATIVO
        $status_api = 'Inativo (status=' . ($usuario_intranet_todos['status'] ?? 'indefinido') . ')';
        $acao = 'FILTRAR (continue)';
        $resultado = '❌ NÃO EXIBE';
        $cor_linha = '#f8d7da';
        $colaboradores_filtrados++;
    } elseif ($usuario_intranet_ativo) {
        // Ativo na Intranet
        $status_api = 'Ativo (status=' . ($usuario_intranet_ativo['status'] ?? 'indefinido') . ')';
        $acao = 'EXIBIR';
        $resultado = '✅ EXIBE';
        $cor_linha = '#d4edda';
        $colaboradores_exibidos++;
    } else {
        // Não encontrado na Intranet
        $status_api = 'Não encontrado';
        $acao = 'EXIBIR (Sem PA)';
        $resultado = '✅ EXIBE';
        $cor_linha = '#fff3cd';
        $colaboradores_exibidos++;
    }
    
    echo "<tr style='background-color: $cor_linha;'>";
    echo "<td style='padding: 5px;'>" . substr($colaborador['cpf'], 0, 3) . "***</td>";
    echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($colaborador['usuario'], 0, 25)) . "</td>";
    echo "<td style='padding: 5px;'>$status_api</td>";
    echo "<td style='padding: 5px;'><strong>$acao</strong></td>";
    echo "<td style='padding: 5px;'><strong>$resultado</strong></td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📊 Resumo da Simulação:</h3>";
echo "<ul>";
echo "<li><strong>Colaboradores processados:</strong> $colaboradores_processados</li>";
echo "<li><strong>Colaboradores filtrados (inativos):</strong> $colaboradores_filtrados</li>";
echo "<li><strong>Colaboradores que devem aparecer:</strong> $colaboradores_exibidos</li>";
echo "</ul>";
echo "</div>";

// Verificar se há diferença entre a simulação e a página real
echo "<h2>4. 🔍 Verificação da Página Real</h2>\n";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Próximos Passos:</h3>";
echo "<ol>";
echo "<li><strong>Cache limpo:</strong> Todos os caches foram removidos</li>";
echo "<li><strong>Teste a página:</strong> <a href='analise_colaboradores.php?aba=colaboradores&nocache=1' style='color: #007bff;'>Acessar página com nocache</a></li>";
echo "<li><strong>Verifique:</strong> Deve mostrar apenas $colaboradores_exibidos colaboradores (sem os inativos)</li>";
echo "<li><strong>Se ainda aparecem inativos:</strong> Pode haver outro cache ou sessão</li>";
echo "</ol>";
echo "</div>";

// Verificar se há algum cache de sessão ou outro tipo
echo "<h2>5. 🔍 Verificação de Outros Caches</h2>\n";

echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>🔧 Informações Técnicas:</strong></p>";
echo "<ul>";
echo "<li><strong>Sessão PHP:</strong> " . (session_status() == PHP_SESSION_ACTIVE ? 'Ativa' : 'Inativa') . "</li>";
echo "<li><strong>Cache de opcodes:</strong> " . (function_exists('opcache_get_status') ? 'Disponível' : 'Não disponível') . "</li>";
echo "<li><strong>Diretório de cache:</strong> " . EDU_API_CACHE_PATH . "</li>";
echo "<li><strong>Tempo de cache:</strong> " . EDU_API_CACHE_TIME . " segundos</li>";
echo "</ul>";
echo "</div>";

// Mostrar exemplo de usuário inativo específico
echo "<h2>6. 🔍 Verificação de Usuário Específico</h2>\n";

// Buscar ALICE BEATRIZ DA SILVA especificamente
$alice_query = "SELECT cpf, usuario FROM edu_relatorio_educacao WHERE usuario LIKE '%ALICE BEATRIZ%' LIMIT 1";
$stmt_alice = $pdo_edu->prepare($alice_query);
$stmt_alice->execute();
$alice_data = $stmt_alice->fetch();

if ($alice_data) {
    $alice_cpf = str_pad(preg_replace('/[^0-9]/', '', $alice_data['cpf']), 11, '0', STR_PAD_LEFT);
    $alice_ativo = $mapa_usuarios_ativos[$alice_cpf] ?? null;
    $alice_todos = $mapa_usuarios_todos[$alice_cpf] ?? null;
    
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>🔍 Verificação: ALICE BEATRIZ DA SILVA</strong></p>";
    echo "<ul>";
    echo "<li><strong>CPF:</strong> " . substr($alice_data['cpf'], 0, 3) . "***</li>";
    echo "<li><strong>Encontrada no banco:</strong> ✅ Sim</li>";
    echo "<li><strong>Encontrada na API (todos):</strong> " . ($alice_todos ? '✅ Sim' : '❌ Não') . "</li>";
    echo "<li><strong>Encontrada na API (ativos):</strong> " . ($alice_ativo ? '✅ Sim' : '❌ Não') . "</li>";
    
    if ($alice_todos && !$alice_ativo) {
        echo "<li><strong>Status na API:</strong> Inativo (status=" . ($alice_todos['status'] ?? 'indefinido') . ")</li>";
        echo "<li><strong>Deve aparecer na página:</strong> ❌ NÃO (deve ser filtrada)</li>";
    } elseif ($alice_ativo) {
        echo "<li><strong>Status na API:</strong> Ativo</li>";
        echo "<li><strong>Deve aparecer na página:</strong> ✅ SIM</li>";
    } else {
        echo "<li><strong>Status na API:</strong> Não encontrada</li>";
        echo "<li><strong>Deve aparecer na página:</strong> ✅ SIM (como Sem PA)</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<p><em>Debug executado em: " . date('d/m/Y H:i:s') . "</em></p>";
?>
