<?php
// <PERSON><PERSON> as configurações do projeto principal
require_once __DIR__ . '/../../../config/database.php';

// Configurações específicas do projeto Educação Corporativa
define('EDU_PROJECT_NAME', 'Sistema de Educação Corporativa');
define('EDU_PROJECT_VERSION', '1.0.0');

// Configurações de Timezone
date_default_timezone_set('America/Sao_Paulo');

// Configurações de Debug
define('EDU_DEBUG_MODE', true);

// Configurações de Log
define('EDU_LOG_PATH', __DIR__ . '/../logs/');

// Configurações de Upload
define('EDU_UPLOAD_PATH', __DIR__ . '/../uploads/');
define('EDU_ALLOWED_EXTENSIONS', ['csv']);
define('EDU_MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB

// Configurações de Importação
define('EDU_BATCH_SIZE', 100); // Processar em lotes de 100 registros

// Configurações de Análise de Dados
define('EDU_RECORDS_PER_PAGE', 50); // Registros por página na análise
define('EDU_CACHE_ANALYSIS_TIME', 1800); // 30 minutos para cache de análises

// Configurações da API da Intranet
define('EDU_API_URL', 'https://intranet.sicoobcredilivre.com.br/api');
define('EDU_API_USER', 'UFL7GXZ14LU9NOR');
define('EDU_API_TOKEN', '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG');

// Configurações de Cache da API
define('EDU_API_CACHE_TIME', 3600); // 1 hora em segundos
define('EDU_API_CACHE_PATH', __DIR__ . '/../cache/');

// Criar diretórios se não existirem
if (!file_exists(EDU_LOG_PATH)) {
    mkdir(EDU_LOG_PATH, 0755, true);
}

if (!file_exists(EDU_UPLOAD_PATH)) {
    mkdir(EDU_UPLOAD_PATH, 0755, true);
}

if (!file_exists(EDU_API_CACHE_PATH)) {
    mkdir(EDU_API_CACHE_PATH, 0755, true);
}
?>
