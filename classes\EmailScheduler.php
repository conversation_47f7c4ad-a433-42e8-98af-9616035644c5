<?php
/**
 * Agendador de E-mails para Educação Corporativa
 * 
 * Classe responsável por gerenciar agendamentos automáticos de e-mails
 */

require_once __DIR__ . '/EmailManager.php';

class EmailScheduler {
    private $pdo;
    private $emailManager;
    
    public function __construct() {
        global $pdo_edu;
        $this->pdo = $pdo_edu;
        $this->emailManager = new EmailManager();
    }
    
    /**
     * Cria um novo agendamento
     */
    public function criarAgendamento($dados) {
        $stmt = $this->pdo->prepare("
            INSERT INTO edu_email_agendamentos 
            (nome, descricao, template_id, tipo_destinatario, filtros_json, frequencia, 
             dia_semana, dia_mes, hora_envio, data_inicio, data_fim, ativo, usuario_criacao) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $dados['nome'],
            $dados['descricao'] ?? null,
            $dados['template_id'],
            $dados['tipo_destinatario'],
            json_encode($dados['filtros'] ?? []),
            $dados['frequencia'],
            $dados['dia_semana'] ?? null,
            $dados['dia_mes'] ?? null,
            $dados['hora_envio'],
            $dados['data_inicio'],
            $dados['data_fim'] ?? null,
            $dados['ativo'] ?? true,
            $_SESSION['user_id'] ?? 1
        ]);
        
        $agendamento_id = $this->pdo->lastInsertId();
        
        // Calcular próxima execução
        $this->calcularProximaExecucao($agendamento_id);
        
        return $agendamento_id;
    }
    
    /**
     * Atualiza um agendamento existente
     */
    public function atualizarAgendamento($id, $dados) {
        $stmt = $this->pdo->prepare("
            UPDATE edu_email_agendamentos 
            SET nome = ?, descricao = ?, template_id = ?, tipo_destinatario = ?, 
                filtros_json = ?, frequencia = ?, dia_semana = ?, dia_mes = ?, 
                hora_envio = ?, data_inicio = ?, data_fim = ?, ativo = ?, 
                usuario_atualizacao = ?
            WHERE id = ?
        ");
        
        $stmt->execute([
            $dados['nome'],
            $dados['descricao'] ?? null,
            $dados['template_id'],
            $dados['tipo_destinatario'],
            json_encode($dados['filtros'] ?? []),
            $dados['frequencia'],
            $dados['dia_semana'] ?? null,
            $dados['dia_mes'] ?? null,
            $dados['hora_envio'],
            $dados['data_inicio'],
            $dados['data_fim'] ?? null,
            $dados['ativo'] ?? true,
            $_SESSION['user_id'] ?? 1,
            $id
        ]);
        
        // Recalcular próxima execução
        $this->calcularProximaExecucao($id);
        
        return $stmt->rowCount() > 0;
    }
    
    /**
     * Calcula a próxima execução de um agendamento
     */
    public function calcularProximaExecucao($agendamento_id) {
        $stmt = $this->pdo->prepare("SELECT * FROM edu_email_agendamentos WHERE id = ?");
        $stmt->execute([$agendamento_id]);
        $agendamento = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$agendamento || !$agendamento['ativo']) {
            return null;
        }
        
        $agora = new DateTime();
        $data_inicio = new DateTime($agendamento['data_inicio']);
        $hora_envio = $agendamento['hora_envio'];
        
        // Se ainda não passou da data de início, usar data de início
        if ($agora < $data_inicio) {
            $proxima = clone $data_inicio;
        } else {
            $proxima = clone $agora;
        }
        
        // Definir hora do envio
        list($hora, $minuto, $segundo) = explode(':', $hora_envio);
        $proxima->setTime($hora, $minuto, $segundo);
        
        // Se já passou da hora hoje, começar do próximo período
        if ($proxima <= $agora) {
            switch ($agendamento['frequencia']) {
                case 'diario':
                    $proxima->add(new DateInterval('P1D'));
                    break;
                case 'semanal':
                    $proxima->add(new DateInterval('P7D'));
                    break;
                case 'quinzenal':
                    $proxima->add(new DateInterval('P14D'));
                    break;
                case 'mensal':
                    $proxima->add(new DateInterval('P1M'));
                    break;
                case 'unico':
                    // Execução única já passou
                    $proxima = null;
                    break;
            }
        }
        
        // Ajustar para dia específico se necessário
        if ($proxima && $agendamento['frequencia'] === 'semanal' && $agendamento['dia_semana']) {
            $dia_semana_atual = $proxima->format('N'); // 1=Segunda, 7=Domingo
            $diferenca = $agendamento['dia_semana'] - $dia_semana_atual;
            if ($diferenca < 0) {
                $diferenca += 7;
            }
            $proxima->add(new DateInterval("P{$diferenca}D"));
        }
        
        if ($proxima && $agendamento['frequencia'] === 'mensal' && $agendamento['dia_mes']) {
            $proxima->setDate($proxima->format('Y'), $proxima->format('m'), $agendamento['dia_mes']);
            // Se o dia já passou neste mês, ir para o próximo
            if ($proxima <= $agora) {
                $proxima->add(new DateInterval('P1M'));
                $proxima->setDate($proxima->format('Y'), $proxima->format('m'), $agendamento['dia_mes']);
            }
        }
        
        // Verificar se não passou da data fim
        if ($proxima && $agendamento['data_fim']) {
            $data_fim = new DateTime($agendamento['data_fim']);
            if ($proxima > $data_fim) {
                $proxima = null;
            }
        }
        
        // Atualizar no banco
        $stmt = $this->pdo->prepare("
            UPDATE edu_email_agendamentos 
            SET proxima_execucao = ? 
            WHERE id = ?
        ");
        $stmt->execute([
            $proxima ? $proxima->format('Y-m-d H:i:s') : null,
            $agendamento_id
        ]);
        
        return $proxima;
    }
    
    /**
     * Executa agendamentos que estão na hora
     */
    public function executarAgendamentos() {
        $stmt = $this->pdo->prepare("
            SELECT * FROM edu_email_agendamentos 
            WHERE ativo = 1 
            AND proxima_execucao IS NOT NULL 
            AND proxima_execucao <= NOW()
            ORDER BY proxima_execucao ASC
        ");
        $stmt->execute();
        $agendamentos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $resultados = [];
        
        foreach ($agendamentos as $agendamento) {
            try {
                $resultado = $this->executarAgendamento($agendamento);
                $resultados[] = [
                    'agendamento_id' => $agendamento['id'],
                    'nome' => $agendamento['nome'],
                    'status' => 'sucesso',
                    'emails_agendados' => $resultado['total_emails'],
                    'detalhes' => $resultado
                ];
                
                // Atualizar estatísticas
                $this->atualizarEstatisticasAgendamento($agendamento['id'], $resultado['total_emails']);
                
            } catch (Exception $e) {
                $resultados[] = [
                    'agendamento_id' => $agendamento['id'],
                    'nome' => $agendamento['nome'],
                    'status' => 'erro',
                    'erro' => $e->getMessage()
                ];
            }
            
            // Calcular próxima execução
            $this->calcularProximaExecucao($agendamento['id']);
        }
        
        return $resultados;
    }
    
    /**
     * Executa um agendamento específico
     */
    private function executarAgendamento($agendamento) {
        $filtros = json_decode($agendamento['filtros_json'], true) ?: [];
        $colaboradores = [];
        
        // Buscar colaboradores baseado no tipo
        switch ($agendamento['tipo_destinatario']) {
            case 'a_vencer':
                $dias_limite = $filtros['dias_limite'] ?? 30;
                $colaboradores = $this->emailManager->buscarColaboradoresAVencer($dias_limite, $filtros);
                break;
                
            case 'vencidos':
                $colaboradores = $this->emailManager->buscarColaboradoresVencidos($filtros);
                break;
                
            case 'todos':
                // Buscar todos os colaboradores (implementar se necessário)
                break;
                
            case 'filtro_personalizado':
                // Implementar filtros personalizados se necessário
                break;
        }
        
        $total_emails = 0;
        $erros = [];
        
        // Agendar envios para cada colaborador
        foreach ($colaboradores as $colaborador) {
            try {
                if (!empty($colaborador['email'])) {
                    $this->emailManager->agendarEnvio(
                        $agendamento['template_id'],
                        $colaborador,
                        'agendado',
                        $agendamento['id']
                    );
                    $total_emails++;
                }
            } catch (Exception $e) {
                $erros[] = "Erro ao agendar para {$colaborador['nome']}: " . $e->getMessage();
            }
        }
        
        return [
            'total_colaboradores' => count($colaboradores),
            'total_emails' => $total_emails,
            'erros' => $erros
        ];
    }
    
    /**
     * Atualiza estatísticas do agendamento
     */
    private function atualizarEstatisticasAgendamento($agendamento_id, $emails_enviados) {
        $stmt = $this->pdo->prepare("
            UPDATE edu_email_agendamentos 
            SET ultima_execucao = NOW(),
                total_envios = total_envios + ?
            WHERE id = ?
        ");
        $stmt->execute([$emails_enviados, $agendamento_id]);
    }
    
    /**
     * Lista agendamentos
     */
    public function listarAgendamentos($ativo = null) {
        $where = "1=1";
        $params = [];

        if ($ativo !== null) {
            $where .= " AND ativo = ?";
            $params[] = $ativo;
        }

        $stmt = $this->pdo->prepare("
            SELECT a.*, t.nome as template_nome, t.tipo as template_tipo
            FROM edu_email_agendamentos a
            LEFT JOIN edu_email_templates t ON a.template_id = t.id
            WHERE $where
            ORDER BY a.data_criacao DESC
        ");
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Busca agendamento por ID
     */
    public function buscarAgendamento($id) {
        $stmt = $this->pdo->prepare("
            SELECT a.*, t.nome as template_nome, t.tipo as template_tipo
            FROM edu_email_agendamentos a
            LEFT JOIN edu_email_templates t ON a.template_id = t.id
            WHERE a.id = ?
        ");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Ativa/desativa agendamento
     */
    public function alterarStatusAgendamento($id, $ativo) {
        $stmt = $this->pdo->prepare("
            UPDATE edu_email_agendamentos 
            SET ativo = ?, usuario_atualizacao = ?
            WHERE id = ?
        ");
        $stmt->execute([$ativo, $_SESSION['user_id'] ?? 1, $id]);
        
        if ($ativo) {
            $this->calcularProximaExecucao($id);
        } else {
            // Limpar próxima execução se desativado
            $stmt = $this->pdo->prepare("UPDATE edu_email_agendamentos SET proxima_execucao = NULL WHERE id = ?");
            $stmt->execute([$id]);
        }
        
        return $stmt->rowCount() > 0;
    }
    
    /**
     * Remove agendamento
     */
    public function removerAgendamento($id) {
        $stmt = $this->pdo->prepare("DELETE FROM edu_email_agendamentos WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->rowCount() > 0;
    }
}
?>
