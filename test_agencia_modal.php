<?php
/**
 * Teste da Correção - Exibição de Agência no Modal
 * 
 * Este arquivo testa se a exibição da agência no formato "número - nome" está funcionando no modal.
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'classes/IntranetAPI.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

echo "<h1>🏢 Teste da Correção - Agência no Modal</h1>";

// Teste 1: Verificar problema original
echo "<h2>1. ❌ Problema Identificado</h2>";

echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Problema Original:</strong></p>";
echo "<ul>";
echo "<li>❌ <strong>Fora do Modal:</strong> Agência exibida como '88 - UAD'</li>";
echo "<li>❌ <strong>Dentro do <PERSON>dal:</strong> Agência exibida apenas como '23' (ID da agência)</li>";
echo "<li>❌ <strong>Inconsistência:</strong> Formatos diferentes na mesma aplicação</li>";
echo "<li>❌ <strong>Causa:</strong> Modal não buscava dados das agências da API</li>";
echo "</ul>";
echo "</div>";

// Teste 2: Verificar dados da API de agências
echo "<h2>2. 🌐 Verificação da API de Agências</h2>";

try {
    $api = new IntranetAPI();
    $agencias_api = $api->listarAgencias();
    
    if ($agencias_api !== false && !empty($agencias_api)) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>API de Agências Funcionando:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Total de agências:</strong> " . count($agencias_api) . "</li>";
        echo "<li><strong>Campos disponíveis:</strong> id, numero, nome</li>";
        echo "</ul>";
        echo "</div>";
        
        // Criar mapeamento como no modal
        $mapa_agencias = [];
        foreach ($agencias_api as $agencia) {
            if (!empty($agencia['id'])) {
                $mapa_agencias[$agencia['id']] = $agencia;
            }
        }
        
        echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>📋 Exemplo de Agências Mapeadas</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Número</th>";
        echo "<th style='padding: 8px;'>Nome</th>";
        echo "<th style='padding: 8px;'>Formato Final</th>";
        echo "</tr>";
        
        $count = 0;
        foreach ($mapa_agencias as $id => $agencia) {
            if ($count >= 10) break; // Mostrar apenas 10 exemplos
            
            $formato_final = $agencia['numero'] . ' - ' . $agencia['nome'];
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($id) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($agencia['numero']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($agencia['nome']) . "</td>";
            echo "<td style='padding: 8px; font-weight: bold; color: #28a745;'>" . htmlspecialchars($formato_final) . "</td>";
            echo "</tr>";
            $count++;
        }
        echo "</table>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>❌ <strong>Erro na API de Agências</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro na API:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 3: Simular lógica do modal
echo "<h2>3. 🧪 Teste da Lógica do Modal</h2>";

try {
    // Buscar alguns colaboradores para teste
    $query_colaboradores = "
        SELECT DISTINCT cpf, usuario, codigo_unidade
        FROM edu_relatorio_educacao 
        WHERE cpf IS NOT NULL AND cpf != ''
        LIMIT 5
    ";
    
    $stmt = $pdo_edu->prepare($query_colaboradores);
    $stmt->execute();
    $colaboradores_teste = $stmt->fetchAll();
    
    if (!empty($colaboradores_teste) && isset($mapa_agencias)) {
        // Buscar usuários da API
        $usuarios_api = $api->listarUsuarios();
        $mapa_usuarios_cpf = [];
        
        if ($usuarios_api !== false) {
            foreach ($usuarios_api as $usuario) {
                if (!empty($usuario['cpf'])) {
                    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                    $mapa_usuarios_cpf[$cpf_normalizado] = $usuario;
                }
            }
        }
        
        echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
        echo "<h3>🎯 Simulação da Lógica do Modal</h3>";
        
        foreach ($colaboradores_teste as $colaborador) {
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
            $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;
            
            // Aplicar a mesma lógica do modal
            $agencia_info = 'N/A';
            if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
                $agencia_id = $usuario_intranet['agencia'];
                if (isset($mapa_agencias[$agencia_id])) {
                    $agencia_data = $mapa_agencias[$agencia_id];
                    $agencia_info = $agencia_data['numero'] . ' - ' . $agencia_data['nome'];
                } else {
                    $agencia_info = $agencia_id; // Fallback para ID se não encontrar
                }
            } elseif (!empty($colaborador['codigo_unidade'])) {
                $agencia_info = $colaborador['codigo_unidade']; // Fallback para código da unidade
            }
            
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px; background: white;'>";
            echo "<h6><strong>" . htmlspecialchars($colaborador['usuario']) . "</strong> (" . substr($colaborador['cpf'], 0, 3) . "***)</h6>";
            echo "<table style='width: 100%; font-size: 0.9rem;'>";
            echo "<tr>";
            echo "<td style='padding: 5px;'><strong>Código Unidade (Relatório):</strong></td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($colaborador['codigo_unidade'] ?: 'N/A') . "</td>";
            echo "</tr>";
            echo "<tr>";
            echo "<td style='padding: 5px;'><strong>Agência ID (Intranet):</strong></td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($usuario_intranet['agencia'] ?? 'N/A') . "</td>";
            echo "</tr>";
            echo "<tr>";
            echo "<td style='padding: 5px;'><strong>Resultado Final:</strong></td>";
            echo "<td style='padding: 5px; font-weight: bold; color: #28a745;'>" . htmlspecialchars($agencia_info) . "</td>";
            echo "</tr>";
            echo "</table>";
            echo "</div>";
        }
        echo "</div>";
        
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>⚠️ <strong>Nenhum colaborador encontrado para teste</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro no teste:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 4: Verificar implementação
echo "<h2>4. ✅ Implementação da Correção</h2>";

echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Correções Implementadas:</strong></p>";
echo "<ul>";
echo "<li>✅ <strong>Busca de Agências:</strong> Modal agora busca agências da API Intranet</li>";
echo "<li>✅ <strong>Mapeamento:</strong> Criação de \$mapa_agencias por ID</li>";
echo "<li>✅ <strong>Lógica de Exibição:</strong> Formato 'número - nome' aplicado</li>";
echo "<li>✅ <strong>Fallbacks:</strong> ID da agência ou código da unidade quando necessário</li>";
echo "</ul>";
echo "</div>";

// Mostrar código da implementação
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #28a745;'>";
echo "<h4>📝 Código da Implementação:</h4>";
echo "<pre style='background: #ffffff; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
// 1. Buscar agências da Intranet (adicionado)
\$agencias_intranet = \$api->listarAgencias();
\$mapa_agencias = [];
if (\$agencias_intranet !== false) {
    foreach (\$agencias_intranet as \$agencia) {
        if (!empty(\$agencia['id'])) {
            \$mapa_agencias[\$agencia['id']] = \$agencia;
        }
    }
}

// 2. Lógica de exibição da agência (modificado)
\$agencia_info = 'N/A';
if (\$usuario_intranet && !empty(\$usuario_intranet['agencia'])) {
    \$agencia_id = \$usuario_intranet['agencia'];
    if (isset(\$mapa_agencias[\$agencia_id])) {
        \$agencia_data = \$mapa_agencias[\$agencia_id];
        \$agencia_info = \$agencia_data['numero'] . ' - ' . \$agencia_data['nome'];
    } else {
        \$agencia_info = \$agencia_id; // Fallback para ID
    }
} elseif (!empty(\$colaborador['codigo_unidade'])) {
    \$agencia_info = \$colaborador['codigo_unidade']; // Fallback para código
}
");
echo "</pre>";
echo "</div>";

// Resumo final
echo "<h2>5. 📋 Resumo da Correção</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Correção da Exibição de Agência no Modal</h3>";

echo "<h4>❌ Antes (Inconsistente):</h4>";
echo "<ul>";
echo "<li><strong>Fora do Modal:</strong> '88 - UAD' (formato completo)</li>";
echo "<li><strong>Dentro do Modal:</strong> '23' (apenas ID)</li>";
echo "<li><strong>Problema:</strong> Modal não buscava dados das agências</li>";
echo "</ul>";

echo "<h4>✅ Depois (Consistente):</h4>";
echo "<ul>";
echo "<li><strong>Fora do Modal:</strong> '88 - UAD' (formato completo)</li>";
echo "<li><strong>Dentro do Modal:</strong> '88 - UAD' (formato completo)</li>";
echo "<li><strong>Solução:</strong> Modal agora busca e mapeia agências da API</li>";
echo "</ul>";

echo "<h4>🔄 Fluxo de Priorização:</h4>";
echo "<ol>";
echo "<li><strong>Primeira Prioridade:</strong> Agência da Intranet (formato 'número - nome')</li>";
echo "<li><strong>Segunda Prioridade:</strong> ID da agência (fallback)</li>";
echo "<li><strong>Terceira Prioridade:</strong> Código da unidade do relatório</li>";
echo "<li><strong>Última Opção:</strong> 'N/A' se nenhum dado disponível</li>";
echo "</ol>";

echo "<h4>✅ Benefícios:</h4>";
echo "<ul>";
echo "<li><strong>Consistência:</strong> Mesmo formato em toda a aplicação</li>";
echo "<li><strong>Clareza:</strong> Usuários veem 'número - nome' em vez de apenas ID</li>";
echo "<li><strong>Robustez:</strong> Múltiplos fallbacks para diferentes cenários</li>";
echo "<li><strong>Manutenibilidade:</strong> Lógica centralizada e reutilizável</li>";
echo "</ul>";

echo "<h4>🎨 Exemplos de Exibição:</h4>";
echo "<ul>";
echo "<li><strong>Ideal:</strong> '88 - UAD' (dados completos da Intranet)</li>";
echo "<li><strong>Fallback 1:</strong> '23' (ID da agência quando nome não disponível)</li>";
echo "<li><strong>Fallback 2:</strong> '001' (código da unidade do relatório)</li>";
echo "<li><strong>Fallback 3:</strong> 'N/A' (nenhum dado disponível)</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>👥 Testar Modal</a>";
echo "<a href='detalhes_colaborador.php?cpf=08813368666' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>📋 Testar Direto</a>";
echo "</p>";
?>
