<?php
/**
 * Teste de Correção dos Erros
 * 
 * Este arquivo testa se os erros da aba de cursos foram corrigidos.
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'classes/IntranetAPI.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

echo "<h1>🔧 Teste de Correção dos Erros</h1>";

// Teste 1: Verificar se as constantes estão definidas
echo "<h2>1. 📋 Verificação das Constantes</h2>";

$constantes_necessarias = [
    'EDU_RECORDS_PER_PAGE' => 'Registros por página',
    'EDU_PROJECT_NAME' => 'Nome do projeto',
    'EDU_API_CACHE_TIME' => 'Tempo de cache da API',
    'EDU_BATCH_SIZE' => 'Tamanho do lote'
];

echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p>✅ <strong>Constantes Definidas:</strong></p>";
echo "<ul>";
foreach ($constantes_necessarias as $constante => $descricao) {
    if (defined($constante)) {
        echo "<li><strong>$constante:</strong> " . constant($constante) . " ($descricao)</li>";
    } else {
        echo "<li style='color: red;'><strong>$constante:</strong> ❌ NÃO DEFINIDA ($descricao)</li>";
    }
}
echo "</ul>";
echo "</div>";

// Teste 2: Simular consulta de cursos
echo "<h2>2. 🎓 Teste da Consulta de Cursos</h2>";

try {
    // Simular os parâmetros da aba de cursos
    $aba_ativa = 'cursos';
    $filtros = ['page' => 1];
    $offset_cursos = ($filtros['page'] - 1) * EDU_RECORDS_PER_PAGE;
    
    echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Parâmetros de Teste:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Aba ativa:</strong> $aba_ativa</li>";
    echo "<li><strong>Página:</strong> " . $filtros['page'] . "</li>";
    echo "<li><strong>Registros por página:</strong> " . EDU_RECORDS_PER_PAGE . "</li>";
    echo "<li><strong>Offset calculado:</strong> $offset_cursos</li>";
    echo "</ul>";
    echo "</div>";
    
    // Teste da consulta de cursos
    $where_conditions_cursos = [];
    $params_cursos = [];
    
    $cursos_query = "
        SELECT 
            codigo_recurso,
            recurso,
            trilha,
            codigo_trilha,
            carga_horaria_recurso,
            COUNT(DISTINCT cpf) as total_colaboradores,
            SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as total_aprovados,
            COUNT(*) as total_registros,
            AVG(CASE WHEN nota_recurso > 0 THEN nota_recurso ELSE NULL END) as media_notas,
            AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento,
            COUNT(CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00' THEN 1 END) as total_concluidos,
            COUNT(CASE WHEN validade_recurso IS NOT NULL AND validade_recurso != '0000-00-00' AND validade_recurso <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND validade_recurso >= CURDATE() THEN 1 END) as a_vencer,
            COUNT(CASE WHEN validade_recurso IS NOT NULL AND validade_recurso != '0000-00-00' AND validade_recurso < CURDATE() THEN 1 END) as vencidos,
            MAX(data_importacao) as ultima_atualizacao
        FROM edu_relatorio_educacao
        " . (!empty($where_conditions_cursos) ? "WHERE " . implode(" AND ", $where_conditions_cursos) : "") . "
        GROUP BY codigo_recurso, recurso, trilha, codigo_trilha, carga_horaria_recurso
        ORDER BY recurso
        LIMIT ? OFFSET ?";
    
    // Adicionar parâmetros de paginação
    $params_cursos[] = EDU_RECORDS_PER_PAGE;
    $params_cursos[] = $offset_cursos;
    
    $stmt_cursos = $pdo_edu->prepare($cursos_query);
    $stmt_cursos->execute($params_cursos);
    $cursos_data = $stmt_cursos->fetchAll();
    
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>✅ <strong>Consulta de Cursos Executada com Sucesso!</strong></p>";
    echo "<ul>";
    echo "<li><strong>Cursos encontrados:</strong> " . count($cursos_data) . "</li>";
    echo "<li><strong>Parâmetros usados:</strong> " . count($params_cursos) . " (" . implode(', ', $params_cursos) . ")</li>";
    echo "</ul>";
    
    if (!empty($cursos_data)) {
        echo "<p><strong>Primeiros 3 cursos encontrados:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 5px;'>Código</th>";
        echo "<th style='padding: 5px;'>Curso</th>";
        echo "<th style='padding: 5px;'>Trilha</th>";
        echo "<th style='padding: 5px;'>Colaboradores</th>";
        echo "<th style='padding: 5px;'>Aprovados</th>";
        echo "</tr>";
        
        for ($i = 0; $i < min(3, count($cursos_data)); $i++) {
            $curso = $cursos_data[$i];
            echo "<tr>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($curso['codigo_recurso']) . "</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($curso['recurso'], 0, 30)) . "...</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($curso['trilha']) . "</td>";
            echo "<td style='padding: 5px; text-align: center;'>" . $curso['total_colaboradores'] . "</td>";
            echo "<td style='padding: 5px; text-align: center;'>" . $curso['total_aprovados'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro na consulta de cursos:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 3: Verificar contagem de cursos
echo "<h2>3. 📊 Teste de Contagem de Cursos</h2>";

try {
    $count_cursos_query = "
        SELECT COUNT(DISTINCT CONCAT(codigo_recurso, '-', recurso)) as total
        FROM edu_relatorio_educacao
    ";
    
    $stmt_count_cursos = $pdo_edu->prepare($count_cursos_query);
    $stmt_count_cursos->execute();
    $total_cursos_count = $stmt_count_cursos->fetch()['total'];
    $total_pages_cursos = ceil($total_cursos_count / EDU_RECORDS_PER_PAGE);
    
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>✅ <strong>Contagem de Cursos:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Total de cursos únicos:</strong> " . number_format($total_cursos_count) . "</li>";
    echo "<li><strong>Registros por página:</strong> " . EDU_RECORDS_PER_PAGE . "</li>";
    echo "<li><strong>Total de páginas:</strong> " . $total_pages_cursos . "</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro na contagem de cursos:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Teste 4: Verificar estrutura da tabela
echo "<h2>4. 🗃️ Verificação da Estrutura da Tabela</h2>";

try {
    $describe_query = "DESCRIBE edu_relatorio_educacao";
    $stmt_describe = $pdo_edu->prepare($describe_query);
    $stmt_describe->execute();
    $colunas = $stmt_describe->fetchAll();
    
    echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Colunas da Tabela edu_relatorio_educacao:</strong></p>";
    echo "<div style='max-height: 200px; overflow-y: auto;'>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 11px;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 3px;'>Campo</th>";
    echo "<th style='padding: 3px;'>Tipo</th>";
    echo "<th style='padding: 3px;'>Nulo</th>";
    echo "<th style='padding: 3px;'>Chave</th>";
    echo "</tr>";
    
    foreach ($colunas as $coluna) {
        echo "<tr>";
        echo "<td style='padding: 3px;'>" . htmlspecialchars($coluna['Field']) . "</td>";
        echo "<td style='padding: 3px;'>" . htmlspecialchars($coluna['Type']) . "</td>";
        echo "<td style='padding: 3px;'>" . htmlspecialchars($coluna['Null']) . "</td>";
        echo "<td style='padding: 3px;'>" . htmlspecialchars($coluna['Key']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>❌ <strong>Erro ao verificar estrutura:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Resumo final
echo "<h2>5. 📋 Resumo da Correção</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔧 Correções Implementadas</h3>";

echo "<h4>✅ Problemas Corrigidos:</h4>";
echo "<ul>";
echo "<li><strong>Undefined variable \$offset:</strong> Criada variável \$offset_cursos específica para cursos</li>";
echo "<li><strong>SQL syntax error:</strong> Corrigida consulta com parâmetros LIMIT e OFFSET</li>";
echo "<li><strong>Constantes não definidas:</strong> Incluído config/config.php com todas as constantes</li>";
echo "<li><strong>Parâmetros de paginação:</strong> Implementada lógica correta para LIMIT e OFFSET</li>";
echo "</ul>";

echo "<h4>✅ Melhorias Implementadas:</h4>";
echo "<ul>";
echo "<li><strong>Consultas Separadas:</strong> Lógica independente para colaboradores e cursos</li>";
echo "<li><strong>Parâmetros Específicos:</strong> Arrays de parâmetros separados por aba</li>";
echo "<li><strong>Paginação Correta:</strong> Offset e limit calculados corretamente</li>";
echo "<li><strong>Contagem Precisa:</strong> Remoção de parâmetros de paginação na contagem</li>";
echo "</ul>";

echo "<h4>✅ Estrutura Final:</h4>";
echo "<ul>";
echo "<li><strong>Aba Colaboradores:</strong> Usa \$offset e \$params</li>";
echo "<li><strong>Aba Cursos:</strong> Usa \$offset_cursos e \$params_cursos</li>";
echo "<li><strong>Paginação:</strong> Independente para cada aba</li>";
echo "<li><strong>Filtros:</strong> Específicos por tipo de análise</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>👥 Testar Aba Colaboradores</a>";
echo "<a href='analise_colaboradores.php?aba=cursos' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🎓 Testar Aba Cursos</a>";
echo "</p>";
?>
