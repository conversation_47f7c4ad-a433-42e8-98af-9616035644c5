# 📧 Sistema Completo de E-mails - Educação Corporativa

## 📋 **Visão Geral**

Sistema completo de envio de e-mails para colaboradores com funcionalidades de:
- **Envio individual** para colaboradores específicos
- **Envio em massa** baseado em critérios
- **Agendamentos automáticos** com diferentes frequências
- **Templates personalizáveis** com variáveis dinâmicas
- **Fila de processamento** para envios em lote
- **Histórico completo** de envios e estatísticas

## 🏗️ **Arquitetura do Sistema**

### **Componentes Principais**

1. **EmailManager.php** - Gerenciador principal de e-mails
2. **EmailScheduler.php** - Gerenciador de agendamentos
3. **emails.php** - Interface principal de envios
4. **agendamentos_emails.php** - Interface de agendamentos
5. **cron_emails.php** - Script de processamento automático

### **Estrutura do Banco de Dados**

#### **edu_email_templates**
- Armazena templates de e-mail com variáveis dinâmicas
- Tipos: 'a_vencer', 'vencidos', 'personalizado'
- Suporte a HTML e texto simples

#### **edu_email_agendamentos**
- Configurações de envios automáticos
- Frequências: único, diário, semanal, quinzenal, mensal
- Filtros personalizáveis por PA, função, trilha

#### **edu_email_envios**
- Log de todos os e-mails enviados
- Status: pendente, enviado, erro, cancelado
- Rastreamento de tentativas e erros

#### **edu_email_fila**
- Fila de processamento para envios em lote
- Sistema de prioridades e tentativas
- Controle de status de processamento

## 🚀 **Funcionalidades Implementadas**

### **1. Envio Individual**
```php
// Exemplo de uso
$emailManager = new EmailManager();
$emailManager->agendarEnvioIndividual($template_id, $cpf_colaborador);
```

**Características:**
- Busca automática dos dados do colaborador
- Cálculo dinâmico de cursos a vencer/vencidos
- Validação de e-mail obrigatória
- Processamento via fila

### **2. Envio em Massa**
```php
// Exemplo de uso
$filtros = [
    'pa' => '3049',
    'funcao' => 'Gerente',
    'dias_limite' => 30
];
$emailManager->agendarEnvioMassa($template_id, 'a_vencer', $filtros);
```

**Características:**
- Filtros por PA, função, trilha
- Seleção por tipo: 'a_vencer' ou 'vencidos'
- Processamento em lote otimizado
- Controle de erros individual

### **3. Agendamentos Automáticos**

#### **Frequências Disponíveis:**
- **Único**: Execução única em data específica
- **Diário**: Todos os dias no horário definido
- **Semanal**: Dia específico da semana
- **Quinzenal**: A cada 15 dias
- **Mensal**: Dia específico do mês

#### **Exemplos de Configuração:**

**Exemplo 1: Semanal para cursos a vencer**
```json
{
    "nome": "Lembrete Semanal - Cursos A Vencer",
    "frequencia": "semanal",
    "dia_semana": 1,
    "hora_envio": "09:00",
    "tipo_destinatario": "a_vencer",
    "filtros": {"dias_limite": 30}
}
```

**Exemplo 2: Mensal para cursos vencidos**
```json
{
    "nome": "Alerta Mensal - Cursos Vencidos",
    "frequencia": "mensal",
    "dia_mes": 1,
    "hora_envio": "08:00",
    "tipo_destinatario": "vencidos"
}
```

**Exemplo 3: Quinzenal para PA específico**
```json
{
    "nome": "Lembrete PA 3049 - Quinzenal",
    "frequencia": "quinzenal",
    "hora_envio": "10:00",
    "tipo_destinatario": "a_vencer",
    "filtros": {"pa": "3049", "dias_limite": 15}
}
```

### **4. Sistema de Templates**

#### **Variáveis Disponíveis:**
- `{{nome_colaborador}}` - Nome do colaborador
- `{{cpf_colaborador}}` - CPF formatado
- `{{email_colaborador}}` - E-mail do colaborador
- `{{funcao_colaborador}}` - Função/cargo
- `{{pa_colaborador}}` - PA formatado (número - nome)
- `{{total_cursos_a_vencer}}` - Quantidade de cursos a vencer
- `{{total_cursos_vencidos}}` - Quantidade de cursos vencidos
- `{{data_envio}}` - Data/hora do envio

#### **Templates Padrão:**

**Template "Cursos A Vencer":**
- Assunto: "Atenção: Cursos com prazo próximo ao vencimento"
- Design responsivo com cores Sicoob
- Informações detalhadas do colaborador
- Call-to-action claro

**Template "Cursos Vencidos":**
- Assunto: "URGENTE: Cursos com prazo vencido"
- Design de alerta com cores vermelhas
- Enfoque na urgência da situação
- Instruções claras de ação

### **5. Processamento Automático**

#### **Script Cron (cron_emails.php):**
```bash
# Executar a cada minuto
* * * * * /usr/bin/php /caminho/para/rh/educacao-corporativa/cron_emails.php
```

**Funcionalidades do Cron:**
- Executa agendamentos na hora correta
- Processa fila de e-mails pendentes
- Gera logs detalhados
- Rotaciona logs automaticamente
- Monitora erros e alertas

#### **Sistema de Logs:**
- Arquivo: `logs/email_cron.log`
- Rotação automática (30 dias)
- Níveis: INFO, WARNING, ERROR, CRITICAL
- Monitoramento de performance

## 📊 **Interface do Usuário**

### **Página Principal (emails.php)**

#### **Estatísticas em Tempo Real:**
- Envios do dia
- Taxa de sucesso
- Erros registrados
- Fila pendente

#### **Abas Funcionais:**
1. **Enviar E-mails** - Envios individuais e em massa
2. **Agendamentos** - Configuração de envios automáticos
3. **Templates** - Gerenciamento de templates
4. **Histórico** - Logs e estatísticas

### **Página de Agendamentos (agendamentos_emails.php)**

#### **Formulário de Criação:**
- Nome e descrição do agendamento
- Seleção de template
- Configuração de destinatários
- Filtros opcionais
- Configuração de frequência
- Datas de início e fim

#### **Lista de Agendamentos:**
- Status ativo/inativo
- Próxima execução
- Estatísticas de envios
- Ações: ativar/desativar/remover

## 🔧 **Configuração e Instalação**

### **1. Executar Instalação:**
```
http://localhost/d/rh/educacao-corporativa/install_email_system.php
```

### **2. Configurar Cron Job:**
```bash
# Adicionar ao crontab
* * * * * /usr/bin/php /caminho/completo/rh/educacao-corporativa/cron_emails.php
```

### **3. Configurar SMTP:**
O sistema utiliza a configuração existente em `d/config/smtp.php`

### **4. Permissões:**
- **Gestor**: Acesso completo ao sistema
- **Admin**: Acesso completo ao sistema
- **Comum**: Sem acesso (pode ser ajustado)

## 📈 **Casos de Uso Práticos**

### **Caso 1: Lembrete Semanal**
"Enviar e-mail toda segunda-feira às 9h para colaboradores com cursos que vencem em 30 dias"

**Configuração:**
- Frequência: Semanal
- Dia: Segunda-feira (1)
- Hora: 09:00
- Tipo: A vencer
- Filtro: 30 dias

### **Caso 2: Alerta Mensal de Vencidos**
"Enviar e-mail todo dia 1º do mês às 8h para colaboradores com cursos vencidos"

**Configuração:**
- Frequência: Mensal
- Dia: 1
- Hora: 08:00
- Tipo: Vencidos

### **Caso 3: Lembrete Quinzenal por PA**
"Enviar e-mail a cada 15 dias para colaboradores do PA 3049 com cursos a vencer em 15 dias"

**Configuração:**
- Frequência: Quinzenal
- Hora: 10:00
- Tipo: A vencer
- Filtros: PA=3049, dias_limite=15

## 🛡️ **Segurança e Confiabilidade**

### **Controle de Erros:**
- Máximo de 3 tentativas por e-mail
- Log detalhado de erros
- Alertas automáticos para muitos erros
- Isolamento de falhas

### **Performance:**
- Processamento em lote (50 e-mails por execução)
- Índices otimizados no banco
- Cache de configurações
- Fila de prioridades

### **Monitoramento:**
- Logs automáticos
- Estatísticas em tempo real
- Alertas de sistema
- Rotação de logs

## 🔄 **Manutenção**

### **Limpeza Automática:**
- Logs antigos (30 dias)
- E-mails processados (configurável)
- Estatísticas agregadas

### **Backup:**
- Tabelas de configuração
- Templates personalizados
- Histórico de envios

---

**✅ Sistema Completo de E-mails Implementado!**

O sistema está pronto para uso em produção com todas as funcionalidades solicitadas: envios individuais, em massa e agendamentos automáticos com total flexibilidade de configuração.
