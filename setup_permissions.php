<?php
/**
 * <PERSON><PERSON>t para criar a tabela de permissões específicas do projeto de Educação Corporativa
 * Execute este arquivo apenas uma vez para configurar o sistema
 */

require_once '../../config/database.php';

try {
    echo "<h2>Configurando Sistema de Permissões - Educação Corporativa</h2>";
    
    // Criar tabela de permissões específica
    $sql_create_table = "
        CREATE TABLE IF NOT EXISTS edu_permissoes_usuarios (
            id INT AUTO_INCREMENT PRIMARY KEY,
            usuario_id INT NOT NULL,
            nivel_acesso ENUM('ADMIN', 'GESTOR', 'COMUM') NOT NULL DEFAULT 'COMUM',
            ativo BOOLEAN DEFAULT TRUE,
            data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
            data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            usuario_criacao INT,
            usuario_atualizacao INT,
            
            UNIQUE KEY unique_usuario_ativo (usuario_id),
            FOREIGN KEY (usuario_id) REFERENCES usuarios(id),
            FOREIGN KEY (usuario_criacao) REFERENCES usuarios(id),
            FOREIGN KEY (usuario_atualizacao) REFERENCES usuarios(id),
            
            INDEX idx_usuario_id (usuario_id),
            INDEX idx_nivel_acesso (nivel_acesso),
            INDEX idx_ativo (ativo)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
        COMMENT='Permissões específicas para o sistema de Educação Corporativa'
    ";
    
    $pdo->exec($sql_create_table);
    echo "<p>✅ Tabela 'edu_permissoes_usuarios' criada com sucesso!</p>";
    
    // Inserir permissão de administrador para o usuário ID 10 (padrão do sistema)
    $sql_insert_admin = "
        INSERT INTO edu_permissoes_usuarios (usuario_id, nivel_acesso, usuario_criacao) 
        VALUES (10, 'ADMIN', 10)
        ON DUPLICATE KEY UPDATE 
            nivel_acesso = 'ADMIN',
            data_atualizacao = CURRENT_TIMESTAMP,
            usuario_atualizacao = 10
    ";
    
    $pdo->exec($sql_insert_admin);
    echo "<p>✅ Permissão de administrador criada para usuário ID 10!</p>";
    
    // Verificar estrutura da tabela
    $stmt = $pdo->query("DESCRIBE edu_permissoes_usuarios");
    $columns = $stmt->fetchAll();
    
    echo "<h3>Estrutura da Tabela:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Mostrar dados inseridos
    $stmt = $pdo->query("SELECT * FROM edu_permissoes_usuarios");
    $permissions = $stmt->fetchAll();
    
    echo "<h3>Permissões Criadas:</h3>";
    if (empty($permissions)) {
        echo "<p>Nenhuma permissão encontrada.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Usuário ID</th><th>Nível</th><th>Ativo</th><th>Data Criação</th></tr>";
        foreach ($permissions as $perm) {
            echo "<tr>";
            echo "<td>" . $perm['id'] . "</td>";
            echo "<td>" . $perm['usuario_id'] . "</td>";
            echo "<td>" . $perm['nivel_acesso'] . "</td>";
            echo "<td>" . ($perm['ativo'] ? 'Sim' : 'Não') . "</td>";
            echo "<td>" . $perm['data_criacao'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>✅ Configuração Concluída!</h3>";
    echo "<p><strong>Próximos passos:</strong></p>";
    echo "<ul>";
    echo "<li>Acesse a página de permissões: <a href='permissions.php'>permissions.php</a></li>";
    echo "<li>Adicione usuários ao projeto conforme necessário</li>";
    echo "<li>Defina os níveis de acesso apropriados</li>";
    echo "</ul>";
    
    echo "<p><strong>Importante:</strong> Este sistema de permissões é específico do projeto de Educação Corporativa e não afeta as permissões globais do sistema.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erro: " . $e->getMessage() . "</p>";
}
?>
