<?php
// Relatório Personalizado Avançado
echo '<table border="1" style="border-collapse: collapse;">';
echo '<tr style="background-color: #6c757d; color: white; font-weight: bold;">';
echo '<td colspan="8" style="padding: 10px; text-align: center; font-size: 16px;">';
echo 'RELATÓRIO PERSONALIZADO AVANÇADO';
echo '</td>';
echo '</tr>';

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td style="padding: 8px;">Gerado em:</td>';
echo '<td colspan="7" style="padding: 8px;">' . date('d/m/Y H:i:s') . '</td>';
echo '</tr>';

$periodo_analise = $filtros['periodo_analise'] ?? 'mes_atual';
$agrupamento = $filtros['agrupamento'] ?? 'pa';

echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Período de Análise:</td>';
echo '<td colspan="3" style="padding: 8px;">' . ucfirst(str_replace('_', ' ', $periodo_analise)) . '</td>';
echo '<td style="padding: 8px; font-weight: bold;">Agrupamento:</td>';
echo '<td colspan="3" style="padding: 8px;">' . ucfirst(str_replace('_', ' ', $agrupamento)) . '</td>';
echo '</tr>';

// Buscar dados baseado no agrupamento
$dados_agrupados = [];

if ($agrupamento === 'pa') {
    // Agrupar por PA
    $colaboradores_query = "SELECT DISTINCT cpf, usuario, funcao FROM edu_relatorio_educacao";
    $todos_colaboradores = $pdo_edu->query($colaboradores_query)->fetchAll();
    
    foreach ($todos_colaboradores as $colaborador) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
        $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;
        
        $grupo_key = 'Sem PA';
        if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
            $agencia_id = $usuario_intranet['agencia'];
            if (isset($mapa_agencias[$agencia_id])) {
                $agencia_data = $mapa_agencias[$agencia_id];
                $grupo_key = $agencia_data['numero'] . ' - ' . $agencia_data['nome'];
            } else {
                $grupo_key = 'PA ' . $agencia_id;
            }
        }
        
        if (!isset($dados_agrupados[$grupo_key])) {
            $dados_agrupados[$grupo_key] = ['colaboradores' => 0, 'cursos' => 0, 'aprovados' => 0];
        }
        
        $dados_agrupados[$grupo_key]['colaboradores']++;
        
        $cursos_colaborador = buscarCursosColaborador($colaborador['cpf'], $pdo_edu, $prazos_config);
        foreach ($cursos_colaborador as $curso) {
            $dados_agrupados[$grupo_key]['cursos']++;
            if ($curso['aprovacao'] === 'Sim') {
                $dados_agrupados[$grupo_key]['aprovados']++;
            }
        }
    }
    
} elseif ($agrupamento === 'trilha') {
    // Agrupar por trilha
    $trilhas_query = "
        SELECT trilha, COUNT(DISTINCT cpf) as colaboradores, COUNT(*) as cursos,
               SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as aprovados
        FROM edu_relatorio_educacao
        WHERE trilha IS NOT NULL AND trilha != ''
        GROUP BY trilha
    ";
    $trilhas = $pdo_edu->query($trilhas_query)->fetchAll();
    
    foreach ($trilhas as $trilha) {
        $dados_agrupados[$trilha['trilha']] = [
            'colaboradores' => $trilha['colaboradores'],
            'cursos' => $trilha['cursos'],
            'aprovados' => $trilha['aprovados']
        ];
    }
    
} elseif ($agrupamento === 'funcao') {
    // Agrupar por função
    $funcoes_query = "
        SELECT funcao, COUNT(DISTINCT cpf) as colaboradores, COUNT(*) as cursos,
               SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as aprovados
        FROM edu_relatorio_educacao
        WHERE funcao IS NOT NULL AND funcao != ''
        GROUP BY funcao
    ";
    $funcoes = $pdo_edu->query($funcoes_query)->fetchAll();
    
    foreach ($funcoes as $funcao) {
        $dados_agrupados[$funcao['funcao']] = [
            'colaboradores' => $funcao['colaboradores'],
            'cursos' => $funcao['cursos'],
            'aprovados' => $funcao['aprovados']
        ];
    }
    
} else {
    // Agrupar por status
    $status_dados = [
        'Aprovados' => ['colaboradores' => 0, 'cursos' => 0, 'aprovados' => 0],
        'Vencidos' => ['colaboradores' => 0, 'cursos' => 0, 'aprovados' => 0],
        'A Vencer' => ['colaboradores' => 0, 'cursos' => 0, 'aprovados' => 0],
        'Em Andamento' => ['colaboradores' => 0, 'cursos' => 0, 'aprovados' => 0]
    ];
    
    $colaboradores_query = "SELECT DISTINCT cpf FROM edu_relatorio_educacao";
    $todos_colaboradores = $pdo_edu->query($colaboradores_query)->fetchAll();
    
    foreach ($todos_colaboradores as $colaborador) {
        $cursos_colaborador = buscarCursosColaborador($colaborador['cpf'], $pdo_edu, $prazos_config);
        
        $tem_aprovado = false;
        $tem_vencido = false;
        $tem_a_vencer = false;
        $tem_andamento = false;
        
        foreach ($cursos_colaborador as $curso) {
            if ($curso['aprovacao'] === 'Sim') {
                $tem_aprovado = true;
                $status_dados['Aprovados']['cursos']++;
                $status_dados['Aprovados']['aprovados']++;
            } elseif ($curso['status_prazo'] === 'vencido') {
                $tem_vencido = true;
                $status_dados['Vencidos']['cursos']++;
            } elseif ($curso['status_prazo'] === 'a_vencer') {
                $tem_a_vencer = true;
                $status_dados['A Vencer']['cursos']++;
            } elseif (!empty($curso['andamento_etapa'])) {
                $tem_andamento = true;
                $status_dados['Em Andamento']['cursos']++;
            }
        }
        
        if ($tem_aprovado) $status_dados['Aprovados']['colaboradores']++;
        if ($tem_vencido) $status_dados['Vencidos']['colaboradores']++;
        if ($tem_a_vencer) $status_dados['A Vencer']['colaboradores']++;
        if ($tem_andamento) $status_dados['Em Andamento']['colaboradores']++;
    }
    
    $dados_agrupados = $status_dados;
}

// Cabeçalhos
echo '<tr style="background-color: #6c757d; color: white; font-weight: bold;">';
echo '<td style="padding: 8px;">' . ucfirst($agrupamento) . '</td>';
echo '<td style="padding: 8px; text-align: center;">Colaboradores</td>';
echo '<td style="padding: 8px; text-align: center;">Total Cursos</td>';
echo '<td style="padding: 8px; text-align: center;">Aprovados</td>';
echo '<td style="padding: 8px; text-align: center;">% Aprovação</td>';
echo '<td style="padding: 8px; text-align: center;">Média por Colaborador</td>';
echo '<td style="padding: 8px; text-align: center;">Performance</td>';
echo '<td style="padding: 8px; text-align: center;">Ranking</td>';
echo '</tr>';

// Calcular ranking
$dados_com_ranking = [];
foreach ($dados_agrupados as $nome => $dados) {
    $perc_aprovacao = $dados['cursos'] > 0 ? ($dados['aprovados'] / $dados['cursos']) * 100 : 0;
    $media_por_colaborador = $dados['colaboradores'] > 0 ? $dados['cursos'] / $dados['colaboradores'] : 0;
    
    $dados_com_ranking[] = [
        'nome' => $nome,
        'dados' => $dados,
        'perc_aprovacao' => $perc_aprovacao,
        'media_por_colaborador' => $media_por_colaborador
    ];
}

// Ordenar por percentual de aprovação
usort($dados_com_ranking, function($a, $b) {
    return $b['perc_aprovacao'] - $a['perc_aprovacao'];
});

// Dados
$posicao = 1;
foreach ($dados_com_ranking as $item) {
    $dados = $item['dados'];
    $perc_aprovacao = $item['perc_aprovacao'];
    $media_por_colaborador = $item['media_por_colaborador'];
    
    $performance = 'Excelente';
    $cor_performance = '#28a745';
    
    if ($perc_aprovacao < 40) {
        $performance = 'Crítico';
        $cor_performance = '#dc3545';
    } elseif ($perc_aprovacao < 60) {
        $performance = 'Regular';
        $cor_performance = '#fd7e14';
    } elseif ($perc_aprovacao < 80) {
        $performance = 'Bom';
        $cor_performance = '#007bff';
    }
    
    echo '<tr>';
    echo '<td style="padding: 6px; font-weight: bold;">' . htmlspecialchars($item['nome']) . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . $dados['colaboradores'] . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . $dados['cursos'] . '</td>';
    echo '<td style="padding: 6px; text-align: center;">' . $dados['aprovados'] . '</td>';
    echo '<td style="padding: 6px; text-align: center; font-weight: bold;">' . number_format($perc_aprovacao, 1) . '%</td>';
    echo '<td style="padding: 6px; text-align: center;">' . number_format($media_por_colaborador, 1) . '</td>';
    echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: ' . $cor_performance . ';">' . $performance . '</td>';
    echo '<td style="padding: 6px; text-align: center; font-weight: bold;">' . $posicao . 'º</td>';
    echo '</tr>';
    
    $posicao++;
}

echo '</table>';
?>
