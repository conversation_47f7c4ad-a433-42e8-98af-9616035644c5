-- Script para criar tabela de trilhas obrigatórias
-- Execute este script para adicionar a funcionalidade de trilhas obrigatórias

USE mci;

-- Criar tabela para gerenciar trilhas obrigatórias
CREATE TABLE IF NOT EXISTS edu_trilhas_obrigatorias (
    id INT AUTO_INCREMENT PRIMARY KEY,
    codigo_trilha VARCHAR(50) NOT NULL,
    trilha VARCHAR(255) NOT NULL,
    obrigatoria BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    usuario_criacao INT,
    usuario_atualizacao INT,
    
    UNIQUE KEY unique_codigo_trilha (codigo_trilha),
    INDEX idx_obrigatoria (obrigatoria),
    INDEX idx_trilha (trilha),
    
    FOREIG<PERSON> KEY (usuario_criacao) REFERENCES usuarios(id),
    FOREIGN KEY (usuario_atualizacao) REFERENCES usuarios(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Inserir trilhas existentes como não obrigatórias por padrão
INSERT IGNORE INTO edu_trilhas_obrigatorias (codigo_trilha, trilha, obrigatoria, usuario_criacao)
SELECT DISTINCT 
    codigo_trilha, 
    trilha, 
    FALSE,
    1 -- ID do usuário admin padrão
FROM edu_relatorio_educacao 
WHERE codigo_trilha IS NOT NULL 
AND codigo_trilha != ''
ORDER BY trilha;

-- Criar log para auditoria de mudanças
CREATE TABLE IF NOT EXISTS edu_log_trilhas_obrigatorias (
    id INT AUTO_INCREMENT PRIMARY KEY,
    trilha_id INT NOT NULL,
    acao ENUM('criada', 'atualizada', 'marcada_obrigatoria', 'desmarcada_obrigatoria') NOT NULL,
    valores_anteriores JSON,
    valores_novos JSON,
    usuario_id INT NOT NULL,
    data_acao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_trilha_id (trilha_id),
    INDEX idx_usuario_id (usuario_id),
    INDEX idx_data_acao (data_acao),
    
    FOREIGN KEY (trilha_id) REFERENCES edu_trilhas_obrigatorias(id) ON DELETE CASCADE,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
