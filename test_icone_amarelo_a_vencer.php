<?php
/**
 * Teste do Ícone Amarelo para Cursos "A Vencer"
 * 
 * Verificar se o ícone amarelo para trilhas com cursos "A Vencer" foi implementado corretamente.
 */

require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'config/config.php';

// Verificar se o usuário é administrador
checkEduPageAccess('admin');

echo "<h1>🔧 Teste do Ícone Amarelo para Cursos \"A Vencer\"</h1>";

// Teste 1: Verificar implementação do ícone amarelo
echo "<h2>1. ✅ Ícone Amarelo para Cursos \"A Vencer\" Implementado</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>⏰ Nova Funcionalidade Implementada:</h3>";

echo "<h4>Contexto:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Já Existia:</strong> Ícone vermelho para trilhas com cursos vencidos</li>";
echo "<li>✅ <strong>Solicitado:</strong> Ícone amarelo para trilhas com cursos \"A Vencer\"</li>";
echo "<li>✅ <strong>Local:</strong> Modal \"Detalhes do Colaborador\" → Seção \"Trilhas de Aprendizagem\"</li>";
echo "<li>✅ <strong>Objetivo:</strong> Alertar visualmente sobre cursos próximos do vencimento</li>";
echo "</ul>";

echo "<h4>Implementação Realizada:</h4>";
echo "<pre style='background: #ffffff; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
echo htmlspecialchars("
// ANTES (Só verificava vencidos)
<?php
// Verificar se há cursos vencidos nesta trilha
\$tem_cursos_vencidos = false;
if (isset(\$cursos_por_trilha[\$trilha['trilha']])) {
    foreach (\$cursos_por_trilha[\$trilha['trilha']] as \$curso) {
        if (\$curso['status_prazo'] === 'vencido') {
            \$tem_cursos_vencidos = true;
            break;
        }
    }
}
?>

<?php if (\$tem_cursos_vencidos): ?>
    <i class=\"fas fa-exclamation-triangle text-danger\"
       title=\"Esta trilha possui cursos vencidos\"
       data-bs-toggle=\"tooltip\"></i>
<?php endif; ?>

// DEPOIS (Verifica vencidos E a vencer)
<?php
// Verificar se há cursos vencidos e a vencer nesta trilha
\$tem_cursos_vencidos = false;
\$tem_cursos_a_vencer = false;
if (isset(\$cursos_por_trilha[\$trilha['trilha']])) {
    foreach (\$cursos_por_trilha[\$trilha['trilha']] as \$curso) {
        if (\$curso['status_prazo'] === 'vencido') {
            \$tem_cursos_vencidos = true;
        } elseif (\$curso['status_prazo'] === 'a_vencer') {
            \$tem_cursos_a_vencer = true;
        }
    }
}
?>

<?php if (\$tem_cursos_vencidos): ?>
    <i class=\"fas fa-exclamation-triangle text-danger\"
       title=\"Esta trilha possui cursos vencidos\"
       data-bs-toggle=\"tooltip\"></i>
<?php endif; ?>

<?php if (\$tem_cursos_a_vencer): ?>
    <i class=\"fas fa-clock text-warning\"
       title=\"Esta trilha possui cursos a vencer\"
       data-bs-toggle=\"tooltip\"></i>
<?php endif; ?>
");
echo "</pre>";

echo "<h4>Principais Mudanças:</h4>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 8px;'>Aspecto</th>";
echo "<th style='padding: 8px;'>Antes</th>";
echo "<th style='padding: 8px;'>Depois</th>";
echo "<th style='padding: 8px;'>Benefício</th>";
echo "</tr>";
echo "<tr><td style='padding: 8px;'>Verificação</td><td style='padding: 8px;'>Só vencidos</td><td style='padding: 8px;'>Vencidos + A vencer</td><td style='padding: 8px;'>Alerta preventivo</td></tr>";
echo "<tr><td style='padding: 8px;'>Variáveis</td><td style='padding: 8px;'>\$tem_cursos_vencidos</td><td style='padding: 8px;'>\$tem_cursos_vencidos + \$tem_cursos_a_vencer</td><td style='padding: 8px;'>Controle separado</td></tr>";
echo "<tr><td style='padding: 8px;'>Loop</td><td style='padding: 8px;'>Break no primeiro vencido</td><td style='padding: 8px;'>Continua verificando ambos</td><td style='padding: 8px;'>Detecção completa</td></tr>";
echo "<tr><td style='padding: 8px;'>Ícones</td><td style='padding: 8px;'>Só vermelho</td><td style='padding: 8px;'>Vermelho + Amarelo</td><td style='padding: 8px;'>Diferenciação visual</td></tr>";
echo "</table>";
echo "</div>";

// Teste 2: Especificações do ícone amarelo
echo "<h2>2. 📋 Especificações do Ícone Amarelo</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>⚙️ Detalhes Técnicos:</h3>";

echo "<h4>Características do Ícone:</h4>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 8px;'>Propriedade</th>";
echo "<th style='padding: 8px;'>Valor</th>";
echo "<th style='padding: 8px;'>Descrição</th>";
echo "</tr>";
echo "<tr><td style='padding: 8px;'>Ícone</td><td style='padding: 8px;'>fas fa-clock</td><td style='padding: 8px;'>Relógio (simboliza tempo/prazo)</td></tr>";
echo "<tr><td style='padding: 8px;'>Cor</td><td style='padding: 8px;'>text-warning</td><td style='padding: 8px;'>Amarelo/laranja (alerta moderado)</td></tr>";
echo "<tr><td style='padding: 8px;'>Tooltip</td><td style='padding: 8px;'>\"Esta trilha possui cursos a vencer\"</td><td style='padding: 8px;'>Explicação ao passar mouse</td></tr>";
echo "<tr><td style='padding: 8px;'>Bootstrap</td><td style='padding: 8px;'>data-bs-toggle=\"tooltip\"</td><td style='padding: 8px;'>Ativação do tooltip</td></tr>";
echo "</table>";

echo "<h4>Condição de Exibição:</h4>";
echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Lógica:</strong> <code>\$curso['status_prazo'] === 'a_vencer'</code></p>";
echo "<p><strong>Quando aparece:</strong> Quando pelo menos um curso da trilha tem status \"a_vencer\"</p>";
echo "<p><strong>Critério \"A Vencer\":</strong> Cursos que vencem em até 30 dias</p>";
echo "</div>";

echo "<h4>Posicionamento:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Local:</strong> Cabeçalho de cada trilha no accordion</li>";
echo "<li>✅ <strong>Posição:</strong> À direita, antes do badge de status</li>";
echo "<li>✅ <strong>Ordem:</strong> Ícone vermelho (vencidos) → Ícone amarelo (a vencer) → Badge status</li>";
echo "<li>✅ <strong>Espaçamento:</strong> gap-2 entre elementos</li>";
echo "</ul>";
echo "</div>";

// Teste 3: Comparação visual dos ícones
echo "<h2>3. 🎨 Comparação Visual dos Ícones</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>👁️ Diferenciação Visual:</h3>";

echo "<h4>Ícone Vermelho (Vencidos):</h4>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #dc3545;'>";
echo "<div style='display: flex; align-items: center; gap: 10px;'>";
echo "<i class='fas fa-exclamation-triangle' style='color: #dc3545; font-size: 1.2rem;'></i>";
echo "<div>";
echo "<strong>Ícone:</strong> fas fa-exclamation-triangle<br>";
echo "<strong>Cor:</strong> text-danger (#dc3545)<br>";
echo "<strong>Significado:</strong> Cursos já vencidos (urgente)<br>";
echo "<strong>Tooltip:</strong> \"Esta trilha possui cursos vencidos\"";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<h4>Ícone Amarelo (A Vencer):</h4>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #ffc107;'>";
echo "<div style='display: flex; align-items: center; gap: 10px;'>";
echo "<i class='fas fa-clock' style='color: #ffc107; font-size: 1.2rem;'></i>";
echo "<div>";
echo "<strong>Ícone:</strong> fas fa-clock<br>";
echo "<strong>Cor:</strong> text-warning (#ffc107)<br>";
echo "<strong>Significado:</strong> Cursos próximos do vencimento (atenção)<br>";
echo "<strong>Tooltip:</strong> \"Esta trilha possui cursos a vencer\"";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<h4>Exemplo de Trilha com Ambos os Ícones:</h4>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; border: 1px solid #dee2e6;'>";
echo "<div style='display: flex; justify-content: space-between; align-items: center;'>";
echo "<div>";
echo "<strong>Trilha de Compliance</strong><br>";
echo "<small style='color: #6c757d;'>8 cursos • 5 aprovados</small>";
echo "</div>";
echo "<div style='display: flex; align-items: center; gap: 8px;'>";
echo "<i class='fas fa-exclamation-triangle' style='color: #dc3545;' title='Esta trilha possui cursos vencidos'></i>";
echo "<i class='fas fa-clock' style='color: #ffc107;' title='Esta trilha possui cursos a vencer'></i>";
echo "<span style='background: #6c757d; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.875rem;'>Em Andamento</span>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<h4>Hierarquia de Prioridade:</h4>";
echo "<ol>";
echo "<li><strong style='color: #dc3545;'>🔴 Vermelho (Vencidos):</strong> Máxima prioridade - ação imediata</li>";
echo "<li><strong style='color: #ffc107;'>🟡 Amarelo (A Vencer):</strong> Prioridade alta - ação preventiva</li>";
echo "<li><strong style='color: #28a745;'>🟢 Verde (Em Dia):</strong> Situação normal - monitoramento</li>";
echo "</ol>";
echo "</div>";

// Teste 4: Como testar a funcionalidade
echo "<h2>4. 🧪 Como Testar a Funcionalidade</h2>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📋 Passos para Verificação:</h3>";

echo "<h4>Teste 1: Trilha com Cursos A Vencer</h4>";
echo "<ol>";
echo "<li><strong>Acesse:</strong> analise_colaboradores.php</li>";
echo "<li><strong>Encontre:</strong> Colaborador com cursos próximos do vencimento</li>";
echo "<li><strong>Clique:</strong> \"Ver Detalhes\" do colaborador</li>";
echo "<li><strong>Vá para:</strong> Seção \"Trilhas de Aprendizagem\"</li>";
echo "<li><strong>Observe:</strong> Cabeçalhos das trilhas</li>";
echo "<li><strong>Verifique:</strong> Ícone de relógio amarelo ao lado do badge</li>";
echo "<li><strong>Teste:</strong> Tooltip ao passar o mouse</li>";
echo "</ol>";

echo "<h4>Teste 2: Trilha com Cursos Vencidos E A Vencer</h4>";
echo "<ol>";
echo "<li><strong>Encontre:</strong> Trilha que tenha ambos os tipos de curso</li>";
echo "<li><strong>Observe:</strong> Devem aparecer AMBOS os ícones</li>";
echo "<li><strong>Verifique:</strong> Ícone vermelho (triângulo) + Ícone amarelo (relógio)</li>";
echo "<li><strong>Confirme:</strong> Ordem: vermelho → amarelo → badge</li>";
echo "<li><strong>Teste:</strong> Tooltips específicos para cada ícone</li>";
echo "</ol>";

echo "<h4>Teste 3: Trilha Sem Problemas</h4>";
echo "<ol>";
echo "<li><strong>Encontre:</strong> Trilha com todos os cursos em dia</li>";
echo "<li><strong>Observe:</strong> Não deve aparecer nenhum ícone de alerta</li>";
echo "<li><strong>Verifique:</strong> Só o badge de status da trilha</li>";
echo "<li><strong>Confirme:</strong> Layout limpo sem ícones desnecessários</li>";
echo "</ol>";

echo "<h4>Teste 4: Diferentes Cenários</h4>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 8px;'>Cenário</th>";
echo "<th style='padding: 8px;'>Ícone Vermelho</th>";
echo "<th style='padding: 8px;'>Ícone Amarelo</th>";
echo "<th style='padding: 8px;'>Resultado Esperado</th>";
echo "</tr>";
echo "<tr><td style='padding: 8px;'>Só cursos vencidos</td><td style='padding: 8px;'>✅ Sim</td><td style='padding: 8px;'>❌ Não</td><td style='padding: 8px;'>Só triângulo vermelho</td></tr>";
echo "<tr><td style='padding: 8px;'>Só cursos a vencer</td><td style='padding: 8px;'>❌ Não</td><td style='padding: 8px;'>✅ Sim</td><td style='padding: 8px;'>Só relógio amarelo</td></tr>";
echo "<tr><td style='padding: 8px;'>Vencidos + A vencer</td><td style='padding: 8px;'>✅ Sim</td><td style='padding: 8px;'>✅ Sim</td><td style='padding: 8px;'>Ambos os ícones</td></tr>";
echo "<tr><td style='padding: 8px;'>Todos em dia</td><td style='padding: 8px;'>❌ Não</td><td style='padding: 8px;'>❌ Não</td><td style='padding: 8px;'>Nenhum ícone</td></tr>";
echo "</table>";
echo "</div>";

// Teste 5: Benefícios da implementação
echo "<h2>5. 🎨 Benefícios da Implementação</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🚀 Melhorias Alcançadas:</h3>";

echo "<h4>✅ Gestão Preventiva:</h4>";
echo "<ul>";
echo "<li><strong>Alerta Antecipado:</strong> Identifica cursos próximos do vencimento</li>";
echo "<li><strong>Ação Preventiva:</strong> Permite intervenção antes do vencimento</li>";
echo "<li><strong>Redução de Vencidos:</strong> Evita que cursos \"a vencer\" se tornem \"vencidos\"</li>";
echo "<li><strong>Planejamento:</strong> Facilita organização de cronogramas de estudo</li>";
echo "</ul>";

echo "<h4>✅ Interface Informativa:</h4>";
echo "<ul>";
echo "<li><strong>Diferenciação Visual:</strong> Cores e ícones distintos para cada situação</li>";
echo "<li><strong>Informação Rápida:</strong> Status visível sem precisar abrir trilha</li>";
echo "<li><strong>Hierarquia Clara:</strong> Prioridades bem definidas (vermelho > amarelo)</li>";
echo "<li><strong>Tooltips Explicativos:</strong> Contexto adicional ao passar mouse</li>";
echo "</ul>";

echo "<h4>✅ Experiência do Usuário:</h4>";
echo "<ul>";
echo "<li><strong>Navegação Eficiente:</strong> Identificação rápida de trilhas que precisam atenção</li>";
echo "<li><strong>Tomada de Decisão:</strong> Informações claras para priorizar ações</li>";
echo "<li><strong>Redução de Surpresas:</strong> Alertas antecipados evitam vencimentos inesperados</li>";
echo "<li><strong>Interface Intuitiva:</strong> Símbolos universais (relógio = tempo)</li>";
echo "</ul>";

echo "<h4>✅ Gestão Organizacional:</h4>";
echo "<ul>";
echo "<li><strong>Monitoramento Proativo:</strong> Acompanhamento preventivo de prazos</li>";
echo "<li><strong>Redução de Riscos:</strong> Menos cursos vencidos por falta de atenção</li>";
echo "<li><strong>Eficiência Operacional:</strong> Gestores podem focar nas prioridades</li>";
echo "<li><strong>Compliance:</strong> Melhor aderência aos prazos de treinamento</li>";
echo "</ul>";
echo "</div>";

// Resumo final
echo "<h2>6. 📋 Resumo da Implementação</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Ícone Amarelo para Cursos \"A Vencer\" Implementado com Sucesso</h3>";

echo "<h4>✅ Funcionalidade Implementada:</h4>";
echo "<ul>";
echo "<li><strong>Ícone:</strong> fas fa-clock (relógio)</li>";
echo "<li><strong>Cor:</strong> text-warning (amarelo)</li>";
echo "<li><strong>Condição:</strong> Trilha com pelo menos um curso \"a vencer\"</li>";
echo "<li><strong>Tooltip:</strong> \"Esta trilha possui cursos a vencer\"</li>";
echo "</ul>";

echo "<h4>✅ Lógica Implementada:</h4>";
echo "<ul>";
echo "<li><strong>Verificação Dupla:</strong> Vencidos E a vencer na mesma iteração</li>";
echo "<li><strong>Variáveis Separadas:</strong> \$tem_cursos_vencidos + \$tem_cursos_a_vencer</li>";
echo "<li><strong>Exibição Condicional:</strong> Cada ícone aparece independentemente</li>";
echo "<li><strong>Posicionamento:</strong> Ícones antes do badge de status</li>";
echo "</ul>";

echo "<h4>✅ Arquivo Modificado:</h4>";
echo "<ul>";
echo "<li><strong>detalhes_colaborador.php:</strong> Seção \"Trilhas de Aprendizagem\"</li>";
echo "<li><strong>Linhas:</strong> 724-750 (lógica de verificação e exibição)</li>";
echo "<li><strong>Compatibilidade:</strong> Mantém funcionalidade existente</li>";
echo "</ul>";

echo "<h4>🚀 Resultado Final:</h4>";
echo "<ul>";
echo "<li><strong>Sistema Completo:</strong> Alertas para vencidos E a vencer</li>";
echo "<li><strong>Gestão Preventiva:</strong> Identificação antecipada de problemas</li>";
echo "<li><strong>Interface Rica:</strong> Informações visuais claras e intuitivas</li>";
echo "<li><strong>Experiência Melhorada:</strong> Usuários podem agir preventivamente</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📊 Testar na Análise</a>";
echo "<a href='detalhes_colaborador.php?cpf=12345678901' style='background: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>⏰ Testar Ícone Amarelo</a>";
echo "</p>";
?>
