<?php
/**
 * Script de processamento automático de e-mails
 * 
 * Este script deve ser executado via cron a cada minuto para:
 * 1. Executar agendamentos que estão na hora
 * 2. Processar fila de e-mails pendentes
 * 
 * Configuração do cron (executar a cada minuto):
 * * * * * * /usr/bin/php /caminho/para/rh/educacao-corporativa/cron_emails.php
 */

// Definir que é execução via CLI
if (php_sapi_name() !== 'cli') {
    die('Este script deve ser executado via linha de comando (CLI)');
}

// Incluir dependências
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/classes/EmailManager.php';
require_once __DIR__ . '/classes/EmailScheduler.php';

// Configurar timezone
date_default_timezone_set('America/Sao_Paulo');

// Função para log
function logMessage($message, $type = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $logFile = __DIR__ . '/logs/email_cron.log';
    
    // Criar diretório de logs se não existir
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logEntry = "[$timestamp] [$type] $message" . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    
    // Também exibir no console se executado manualmente
    echo $logEntry;
}

try {
    logMessage("Iniciando processamento automático de e-mails");
    
    $emailManager = new EmailManager();
    $emailScheduler = new EmailScheduler();
    
    // 1. Executar agendamentos que estão na hora
    logMessage("Verificando agendamentos para execução...");
    $resultados_agendamentos = $emailScheduler->executarAgendamentos();
    
    if (!empty($resultados_agendamentos)) {
        foreach ($resultados_agendamentos as $resultado) {
            if ($resultado['status'] === 'sucesso') {
                logMessage("Agendamento '{$resultado['nome']}' executado com sucesso. E-mails agendados: {$resultado['emails_agendados']}");
            } else {
                logMessage("Erro no agendamento '{$resultado['nome']}': {$resultado['erro']}", 'ERROR');
            }
        }
    } else {
        logMessage("Nenhum agendamento para executar no momento");
    }
    
    // 2. Processar fila de e-mails pendentes
    logMessage("Processando fila de e-mails...");
    $resultados_fila = $emailManager->processarFila(50); // Processar até 50 e-mails por execução
    
    if (!empty($resultados_fila)) {
        $sucessos = count(array_filter($resultados_fila, fn($r) => $r['status'] === 'sucesso'));
        $erros = count(array_filter($resultados_fila, fn($r) => $r['status'] === 'erro'));
        
        logMessage("Fila processada. Sucessos: $sucessos, Erros: $erros");
        
        // Log detalhado dos erros
        foreach ($resultados_fila as $resultado) {
            if ($resultado['status'] === 'erro') {
                logMessage("Erro ao enviar para {$resultado['email']}: {$resultado['erro']}", 'ERROR');
            }
        }
    } else {
        logMessage("Nenhum e-mail na fila para processar");
    }
    
    // 3. Limpeza de logs antigos (manter apenas últimos 30 dias)
    $logFile = __DIR__ . '/logs/email_cron.log';
    if (file_exists($logFile) && filesize($logFile) > 10 * 1024 * 1024) { // Se arquivo > 10MB
        logMessage("Rotacionando arquivo de log...");
        
        $lines = file($logFile);
        $cutoffDate = date('Y-m-d', strtotime('-30 days'));
        $newLines = [];
        
        foreach ($lines as $line) {
            if (preg_match('/\[(\d{4}-\d{2}-\d{2})/', $line, $matches)) {
                if ($matches[1] >= $cutoffDate) {
                    $newLines[] = $line;
                }
            }
        }
        
        file_put_contents($logFile, implode('', $newLines));
        logMessage("Log rotacionado. Linhas mantidas: " . count($newLines));
    }
    
    // 4. Estatísticas rápidas
    $stmt = $pdo_edu->prepare("
        SELECT 
            COUNT(*) as total_pendentes,
            COUNT(CASE WHEN tentativas >= max_tentativas THEN 1 END) as total_falharam
        FROM edu_email_fila 
        WHERE status = 'pendente' OR status = 'erro'
    ");
    $stmt->execute();
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($stats['total_pendentes'] > 0) {
        logMessage("Estatísticas: {$stats['total_pendentes']} e-mails pendentes, {$stats['total_falharam']} falharam definitivamente");
    }
    
    logMessage("Processamento automático concluído com sucesso");
    
} catch (Exception $e) {
    logMessage("Erro crítico no processamento: " . $e->getMessage(), 'CRITICAL');
    logMessage("Stack trace: " . $e->getTraceAsString(), 'DEBUG');
    exit(1);
}

// Verificar se há muitos e-mails com erro e alertar
try {
    $stmt = $pdo_edu->prepare("
        SELECT COUNT(*) as erros_recentes
        FROM edu_email_envios 
        WHERE status = 'erro' 
        AND data_criacao >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
    ");
    $stmt->execute();
    $erros_recentes = $stmt->fetch(PDO::FETCH_ASSOC)['erros_recentes'];
    
    if ($erros_recentes > 10) {
        logMessage("ALERTA: $erros_recentes e-mails falharam na última hora. Verificar configuração SMTP.", 'WARNING');
    }
} catch (Exception $e) {
    logMessage("Erro ao verificar estatísticas de erro: " . $e->getMessage(), 'ERROR');
}

exit(0);
?>
