<?php
/**
 * Teste para verificar se os relatórios estão aplicando o filtro de usuários inativos
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/IntranetAPI.php';

echo "<h1>🧪 Teste: Filtro de Usuários Inativos nos Relatórios</h1>\n";

// Criar instância da API
$api = new IntranetAPI();

// Buscar dados da API SEM CACHE
echo "<h2>1. 📊 Dados da API</h2>\n";
$usuarios_intranet_todos = $api->listarUsuarios(false, false);
$usuarios_intranet_ativos = $api->listarUsuariosAtivos(false);

echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>API Status:</strong></p>";
echo "<ul>";
echo "<li><strong>Total usuários:</strong> " . count($usuarios_intranet_todos) . "</li>";
echo "<li><strong>Usuários ativos:</strong> " . count($usuarios_intranet_ativos) . "</li>";
echo "<li><strong>Usuários inativos:</strong> " . (count($usuarios_intranet_todos) - count($usuarios_intranet_ativos)) . "</li>";
echo "</ul>";
echo "</div>";

// Criar mapas
$mapa_usuarios_ativos = [];
$mapa_usuarios_todos = [];

foreach ($usuarios_intranet_ativos as $usuario) {
    if (!empty($usuario['cpf'])) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
        $mapa_usuarios_ativos[$cpf_normalizado] = $usuario;
    }
}

foreach ($usuarios_intranet_todos as $usuario) {
    if (!empty($usuario['cpf'])) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
        $mapa_usuarios_todos[$cpf_normalizado] = $usuario;
    }
}

echo "<h2>2. 🔍 Teste da Função de Filtro</h2>\n";

// Simular a função de filtro
function aplicarFiltroUsuariosInativos($colaboradores, $mapa_usuarios_ativos, $mapa_usuarios_todos) {
    $colaboradores_filtrados = [];
    
    foreach ($colaboradores as $colaborador) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
        
        $usuario_intranet_ativo = $mapa_usuarios_ativos[$cpf_normalizado] ?? null;
        $usuario_intranet_todos = $mapa_usuarios_todos[$cpf_normalizado] ?? null;
        
        // FILTRO: Se existe na Intranet mas está INATIVO, não incluir
        if ($usuario_intranet_todos && !$usuario_intranet_ativo) {
            continue; // Usuário inativo - PULAR
        }
        
        // Incluir colaborador na lista filtrada
        $colaboradores_filtrados[] = $colaborador;
    }
    
    return $colaboradores_filtrados;
}

// Buscar colaboradores do banco
$colaboradores_query = "
    SELECT
        cpf,
        MAX(usuario) as usuario,
        COUNT(DISTINCT trilha) as total_trilhas,
        COUNT(DISTINCT CONCAT(codigo_trilha, '|', codigo_recurso)) as total_cursos
    FROM edu_relatorio_educacao
    GROUP BY cpf
    ORDER BY MAX(usuario)
    LIMIT 20
";

$stmt_colaboradores = $pdo_edu->prepare($colaboradores_query);
$stmt_colaboradores->execute();
$todos_colaboradores_bruto = $stmt_colaboradores->fetchAll();

// Aplicar filtro
$colaboradores_filtrados = aplicarFiltroUsuariosInativos($todos_colaboradores_bruto, $mapa_usuarios_ativos, $mapa_usuarios_todos);

echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Resultado do Filtro:</strong></p>";
echo "<ul>";
echo "<li><strong>Colaboradores no banco (bruto):</strong> " . count($todos_colaboradores_bruto) . "</li>";
echo "<li><strong>Colaboradores após filtro:</strong> " . count($colaboradores_filtrados) . "</li>";
echo "<li><strong>Colaboradores filtrados (inativos):</strong> " . (count($todos_colaboradores_bruto) - count($colaboradores_filtrados)) . "</li>";
echo "</ul>";
echo "</div>";

echo "<h2>3. 📋 Análise Detalhada</h2>\n";

echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='padding: 5px;'>CPF</th>";
echo "<th style='padding: 5px;'>Nome</th>";
echo "<th style='padding: 5px;'>Existe Todos?</th>";
echo "<th style='padding: 5px;'>Existe Ativos?</th>";
echo "<th style='padding: 5px;'>Status</th>";
echo "<th style='padding: 5px;'>Resultado</th>";
echo "</tr>";

$total_ativos = 0;
$total_inativos = 0;
$total_nao_encontrados = 0;

foreach ($todos_colaboradores_bruto as $colaborador) {
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    
    $usuario_intranet_ativo = $mapa_usuarios_ativos[$cpf_normalizado] ?? null;
    $usuario_intranet_todos = $mapa_usuarios_todos[$cpf_normalizado] ?? null;
    
    $existe_todos = $usuario_intranet_todos ? 'SIM' : 'NÃO';
    $existe_ativo = $usuario_intranet_ativo ? 'SIM' : 'NÃO';
    
    $status = '';
    $resultado = '';
    $cor_linha = '';
    
    if ($usuario_intranet_todos && !$usuario_intranet_ativo) {
        // Inativo
        $status = 'INATIVO';
        $resultado = '❌ FILTRADO';
        $cor_linha = '#f8d7da';
        $total_inativos++;
    } elseif ($usuario_intranet_ativo) {
        // Ativo
        $status = 'ATIVO';
        $resultado = '✅ INCLUÍDO';
        $cor_linha = '#d4edda';
        $total_ativos++;
    } else {
        // Não encontrado
        $status = 'NÃO ENCONTRADO';
        $resultado = '✅ INCLUÍDO (Sem PA)';
        $cor_linha = '#fff3cd';
        $total_nao_encontrados++;
    }
    
    echo "<tr style='background-color: $cor_linha;'>";
    echo "<td style='padding: 5px;'>" . substr($colaborador['cpf'], 0, 3) . "***</td>";
    echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($colaborador['usuario'], 0, 20)) . "</td>";
    echo "<td style='padding: 5px;'>$existe_todos</td>";
    echo "<td style='padding: 5px;'>$existe_ativo</td>";
    echo "<td style='padding: 5px;'><strong>$status</strong></td>";
    echo "<td style='padding: 5px;'><strong>$resultado</strong></td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📊 Resumo da Análise:</h3>";
echo "<ul>";
echo "<li><strong>Colaboradores ativos:</strong> $total_ativos (incluídos nos relatórios)</li>";
echo "<li><strong>Colaboradores inativos:</strong> $total_inativos (filtrados dos relatórios)</li>";
echo "<li><strong>Colaboradores não encontrados:</strong> $total_nao_encontrados (incluídos como 'Sem PA')</li>";
echo "<li><strong>Total que aparecerá nos relatórios:</strong> " . ($total_ativos + $total_nao_encontrados) . "</li>";
echo "</ul>";
echo "</div>";

echo "<h2>4. 🧪 Teste dos Relatórios</h2>\n";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>✅ Relatórios Atualizados com Filtro:</h3>";
echo "<p>Os seguintes relatórios foram atualizados para aplicar o filtro de usuários inativos:</p>";
echo "<ul>";
echo "<li>✅ <strong>Colaboradores Completo</strong></li>";
echo "<li>✅ <strong>Colaboradores A Vencer</strong></li>";
echo "<li>✅ <strong>Colaboradores Aprovados</strong></li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔧 Como Testar:</h3>";
echo "<ol>";
echo "<li><strong>Acesse a página de relatórios:</strong> <a href='relatorios.php'>relatorios.php</a></li>";
echo "<li><strong>Gere um relatório de colaboradores</strong></li>";
echo "<li><strong>Verifique se usuários inativos não aparecem</strong> (ex: ALICE BEATRIZ, CLESIO)</li>";
echo "<li><strong>Compare com os números desta análise</strong></li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Resultado Esperado nos Relatórios:</h3>";
echo "<ul>";
echo "<li><strong>Total de colaboradores:</strong> " . count($colaboradores_filtrados) . " (não " . count($todos_colaboradores_bruto) . ")</li>";
echo "<li><strong>Usuários inativos:</strong> NÃO devem aparecer</li>";
echo "<li><strong>Usuários ativos:</strong> Devem aparecer com dados da Intranet</li>";
echo "<li><strong>Usuários não encontrados:</strong> Devem aparecer como 'Sem PA Definido'</li>";
echo "</ul>";
echo "</div>";

// Verificar colaboradores específicos
echo "<h2>5. 🔍 Verificação de Colaboradores Específicos</h2>\n";

$colaboradores_especificos = ['ALICE BEATRIZ', 'CLESIO'];

foreach ($colaboradores_especificos as $nome_busca) {
    $query_especifico = "SELECT cpf, usuario FROM edu_relatorio_educacao WHERE usuario LIKE ? LIMIT 1";
    $stmt_especifico = $pdo_edu->prepare($query_especifico);
    $stmt_especifico->execute(['%' . $nome_busca . '%']);
    $colaborador_especifico = $stmt_especifico->fetch();
    
    if ($colaborador_especifico) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador_especifico['cpf']), 11, '0', STR_PAD_LEFT);
        $usuario_ativo = $mapa_usuarios_ativos[$cpf_normalizado] ?? null;
        $usuario_todos = $mapa_usuarios_todos[$cpf_normalizado] ?? null;
        
        $deve_ser_filtrado = ($usuario_todos && !$usuario_ativo);
        
        echo "<div style='background: " . ($deve_ser_filtrado ? '#f8d7da' : '#d4edda') . "; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>" . htmlspecialchars($colaborador_especifico['usuario']) . ":</strong></p>";
        echo "<ul>";
        echo "<li><strong>Status:</strong> " . ($deve_ser_filtrado ? 'INATIVO (deve ser filtrado)' : 'ATIVO ou não encontrado') . "</li>";
        echo "<li><strong>Deve aparecer nos relatórios:</strong> " . ($deve_ser_filtrado ? '❌ NÃO' : '✅ SIM') . "</li>";
        echo "</ul>";
        echo "</div>";
    }
}

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
?>
