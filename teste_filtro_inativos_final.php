<?php
/**
 * Teste final para verificar se usuários inativos estão sendo filtrados corretamente
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/IntranetAPI.php';

// Forçar limpeza de cache
$cache_files_removed = 0;
$cache_dir = EDU_API_CACHE_PATH;

if (is_dir($cache_dir)) {
    $files = glob($cache_dir . '*.json');
    foreach ($files as $file) {
        if (unlink($file)) {
            $cache_files_removed++;
        }
    }
}

echo "<h1>🧪 Teste Final: Filtro de Usuários Inativos</h1>\n";
echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>✅ Cache limpo:</strong> $cache_files_removed arquivos removidos</p>";
echo "</div>";

// Criar instância da API
$api = new IntranetAPI();

// Buscar dados da API sem cache
$usuarios_intranet_todos = $api->listarUsuarios(false, false);
$usuarios_intranet_ativos = $api->listarUsuariosAtivos(false);

// Criar mapas
$mapa_usuarios_ativos = [];
$mapa_usuarios_todos = [];

foreach ($usuarios_intranet_ativos as $usuario) {
    if (!empty($usuario['cpf'])) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
        $mapa_usuarios_ativos[$cpf_normalizado] = $usuario;
    }
}

foreach ($usuarios_intranet_todos as $usuario) {
    if (!empty($usuario['cpf'])) {
        $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
        $mapa_usuarios_todos[$cpf_normalizado] = $usuario;
    }
}

// Buscar ALICE especificamente
$alice_query = "SELECT cpf, usuario FROM edu_relatorio_educacao WHERE usuario LIKE '%ALICE BEATRIZ%' LIMIT 1";
$stmt_alice = $pdo_edu->prepare($alice_query);
$stmt_alice->execute();
$alice_data = $stmt_alice->fetch();

echo "<h2>🔍 Teste Específico: ALICE BEATRIZ DA SILVA</h2>\n";

if ($alice_data) {
    $alice_cpf = str_pad(preg_replace('/[^0-9]/', '', $alice_data['cpf']), 11, '0', STR_PAD_LEFT);
    $alice_ativo = $mapa_usuarios_ativos[$alice_cpf] ?? null;
    $alice_todos = $mapa_usuarios_todos[$alice_cpf] ?? null;
    
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #e9ecef;'>";
    echo "<th style='padding: 10px;'>Verificação</th>";
    echo "<th style='padding: 10px;'>Resultado</th>";
    echo "<th style='padding: 10px;'>Status</th>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td style='padding: 10px;'><strong>Encontrada no banco de dados</strong></td>";
    echo "<td style='padding: 10px;'>✅ Sim</td>";
    echo "<td style='padding: 10px;'>Nome: " . htmlspecialchars($alice_data['usuario']) . "</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td style='padding: 10px;'><strong>Encontrada na API (todos)</strong></td>";
    echo "<td style='padding: 10px;'>" . ($alice_todos ? '✅ Sim' : '❌ Não') . "</td>";
    echo "<td style='padding: 10px;'>" . ($alice_todos ? 'Status: ' . ($alice_todos['status'] ?? 'indefinido') : 'N/A') . "</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td style='padding: 10px;'><strong>Encontrada na API (ativos)</strong></td>";
    echo "<td style='padding: 10px;'>" . ($alice_ativo ? '✅ Sim' : '❌ Não') . "</td>";
    echo "<td style='padding: 10px;'>" . ($alice_ativo ? 'Status: ' . ($alice_ativo['status'] ?? 'indefinido') : 'N/A') . "</td>";
    echo "</tr>";
    
    // Aplicar a lógica de filtro
    $deve_ser_filtrada = ($alice_todos && !$alice_ativo);
    
    echo "<tr style='background: " . ($deve_ser_filtrada ? '#f8d7da' : '#d4edda') . ";'>";
    echo "<td style='padding: 10px;'><strong>Deve ser filtrada?</strong></td>";
    echo "<td style='padding: 10px;'>" . ($deve_ser_filtrada ? '✅ SIM' : '❌ NÃO') . "</td>";
    echo "<td style='padding: 10px;'>" . ($deve_ser_filtrada ? 'Inativa na Intranet' : 'Ativa ou não encontrada') . "</td>";
    echo "</tr>";
    
    echo "<tr style='background: " . ($deve_ser_filtrada ? '#f8d7da' : '#d4edda') . ";'>";
    echo "<td style='padding: 10px;'><strong>Deve aparecer na página?</strong></td>";
    echo "<td style='padding: 10px;'>" . ($deve_ser_filtrada ? '❌ NÃO' : '✅ SIM') . "</td>";
    echo "<td style='padding: 10px;'>" . ($deve_ser_filtrada ? 'Será filtrada pelo continue' : 'Aparecerá normalmente') . "</td>";
    echo "</tr>";
    
    echo "</table>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>⚠️ ALICE BEATRIZ DA SILVA não foi encontrada no banco de dados</p>";
    echo "</div>";
}

// Simular o processamento da página principal
echo "<h2>🔄 Simulação do Processamento da Página Principal</h2>\n";

// Buscar alguns colaboradores para teste
$colaboradores_query = "
    SELECT cpf, MAX(usuario) as usuario
    FROM edu_relatorio_educacao
    GROUP BY cpf
    ORDER BY MAX(usuario)
    LIMIT 10
";

$stmt_colaboradores = $pdo_edu->prepare($colaboradores_query);
$stmt_colaboradores->execute();
$colaboradores_teste = $stmt_colaboradores->fetchAll();

$colaboradores_processados = 0;
$colaboradores_filtrados = 0;
$colaboradores_exibidos = 0;

echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='padding: 5px;'>CPF</th>";
echo "<th style='padding: 5px;'>Nome</th>";
echo "<th style='padding: 5px;'>Status API</th>";
echo "<th style='padding: 5px;'>Ação</th>";
echo "<th style='padding: 5px;'>Resultado</th>";
echo "</tr>";

foreach ($colaboradores_teste as $colaborador) {
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    
    $usuario_intranet_ativo = $mapa_usuarios_ativos[$cpf_normalizado] ?? null;
    $usuario_intranet_todos = $mapa_usuarios_todos[$cpf_normalizado] ?? null;
    
    $colaboradores_processados++;
    
    // Aplicar a lógica exata da página principal
    if ($usuario_intranet_todos && !$usuario_intranet_ativo) {
        // Usuário existe na Intranet mas está inativo - FILTRAR
        $status_api = 'Inativo';
        $acao = 'continue (FILTRAR)';
        $resultado = '❌ NÃO EXIBE';
        $cor_linha = '#f8d7da';
        $colaboradores_filtrados++;
    } elseif ($usuario_intranet_ativo) {
        // Ativo na Intranet
        $status_api = 'Ativo';
        $acao = 'PROCESSAR';
        $resultado = '✅ EXIBE';
        $cor_linha = '#d4edda';
        $colaboradores_exibidos++;
    } else {
        // Não encontrado na Intranet
        $status_api = 'Não encontrado';
        $acao = 'PROCESSAR (Sem PA)';
        $resultado = '✅ EXIBE';
        $cor_linha = '#fff3cd';
        $colaboradores_exibidos++;
    }
    
    echo "<tr style='background-color: $cor_linha;'>";
    echo "<td style='padding: 5px;'>" . substr($colaborador['cpf'], 0, 3) . "***</td>";
    echo "<td style='padding: 5px;'>" . htmlspecialchars(substr($colaborador['usuario'], 0, 20)) . "</td>";
    echo "<td style='padding: 5px;'>$status_api</td>";
    echo "<td style='padding: 5px;'><strong>$acao</strong></td>";
    echo "<td style='padding: 5px;'><strong>$resultado</strong></td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>📊 Resumo da Simulação:</h3>";
echo "<ul>";
echo "<li><strong>Colaboradores processados:</strong> $colaboradores_processados</li>";
echo "<li><strong>Colaboradores filtrados (inativos):</strong> $colaboradores_filtrados</li>";
echo "<li><strong>Colaboradores que devem aparecer:</strong> $colaboradores_exibidos</li>";
echo "</ul>";
echo "</div>";

// Instruções finais
echo "<h2>🎯 Verificação Final</h2>\n";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>✅ Próximos Passos:</h3>";
echo "<ol>";
echo "<li><strong>Cache limpo:</strong> Todos os caches foram removidos</li>";
echo "<li><strong>Lógica verificada:</strong> Usuários inativos devem ser filtrados</li>";
echo "<li><strong>Teste a página:</strong> <a href='analise_colaboradores.php?aba=colaboradores&nocache=1' style='color: #007bff; font-weight: bold;'>Acessar página principal</a></li>";
echo "<li><strong>Verifique:</strong> ALICE BEATRIZ DA SILVA não deve mais aparecer</li>";
echo "</ol>";
echo "</div>";

if ($alice_data && $mapa_usuarios_todos[str_pad(preg_replace('/[^0-9]/', '', $alice_data['cpf']), 11, '0', STR_PAD_LEFT)] ?? false) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
    echo "<h3>⚠️ Atenção:</h3>";
    echo "<p><strong>ALICE BEATRIZ DA SILVA está INATIVA na Intranet e deve ser FILTRADA.</strong></p>";
    echo "<p>Se ela ainda aparecer na página principal, pode haver um problema de cache do navegador ou sessão PHP.</p>";
    echo "<p><strong>Soluções:</strong></p>";
    echo "<ul>";
    echo "<li>Limpe o cache do navegador (Ctrl+F5)</li>";
    echo "<li>Abra em aba anônima/privada</li>";
    echo "<li>Adicione ?nocache=" . time() . " na URL</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . "</em></p>";
?>
