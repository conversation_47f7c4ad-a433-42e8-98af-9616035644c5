<?php
/**
 * Teste final para garantir que o filtro de usuários inativos está funcionando
 */

require_once 'config/config.php';

echo "<h1>🎯 Teste Final: Filtro de Usuários Inativos</h1>\n";

// 1. Limpar TODOS os caches
echo "<h2>1. 🧹 Limpeza Completa de Cache</h2>\n";

$cache_files_removed = 0;
$cache_dir = EDU_API_CACHE_PATH;

if (is_dir($cache_dir)) {
    $files = glob($cache_dir . '*');
    foreach ($files as $file) {
        if (is_file($file) && unlink($file)) {
            $cache_files_removed++;
        }
    }
}

// Limpar cache de sessão
if (session_status() == PHP_SESSION_ACTIVE) {
    session_destroy();
}

// Limpar cache de opcodes
if (function_exists('opcache_reset')) {
    opcache_reset();
}

echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>✅ Cache limpo:</strong> $cache_files_removed arquivos removidos</p>";
echo "</div>";

// 2. Forçar headers para evitar cache do navegador
header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");

echo "<h2>2. 🎯 Teste da Página Principal</h2>\n";

$timestamp = time();

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>✅ Cache Completamente Limpo!</h3>";
echo "<p><strong>Agora teste a página principal com timestamp único:</strong></p>";
echo "<p>";
echo "<a href='analise_colaboradores.php?aba=colaboradores&nocache=$timestamp&force_refresh=1' ";
echo "style='background: #28a745; color: white; padding: 15px 20px; text-decoration: none; border-radius: 5px; font-size: 16px; font-weight: bold;'>";
echo "🔄 TESTAR PÁGINA PRINCIPAL</a>";
echo "</p>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 O que deve acontecer:</h3>";
echo "<ul>";
echo "<li><strong>✅ Usuários ATIVOS da Intranet:</strong> Devem aparecer normalmente</li>";
echo "<li><strong>✅ Usuários NÃO ENCONTRADOS na Intranet:</strong> Devem aparecer como 'Sem PA Definido'</li>";
echo "<li><strong>❌ Usuários INATIVOS da Intranet:</strong> NÃO devem aparecer (incluindo ALICE BEATRIZ)</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>⚠️ Se ALICE BEATRIZ ainda aparecer:</h3>";
echo "<p><strong>Soluções adicionais:</strong></p>";
echo "<ol>";
echo "<li><strong>Limpe o cache do navegador:</strong> Ctrl+Shift+Del</li>";
echo "<li><strong>Abra em aba anônima/privada</strong></li>";
echo "<li><strong>Adicione parâmetros únicos:</strong> ?nocache=" . time() . "&v=" . rand(1000, 9999) . "</li>";
echo "<li><strong>Verifique se há proxy/CDN</strong> fazendo cache</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🔧 Informações Técnicas:</h3>";
echo "<ul>";
echo "<li><strong>Timestamp único:</strong> $timestamp</li>";
echo "<li><strong>Cache de arquivos:</strong> $cache_files_removed arquivos removidos</li>";
echo "<li><strong>Headers de cache:</strong> Configurados para no-cache</li>";
echo "<li><strong>Sessão PHP:</strong> Destruída</li>";
echo "<li><strong>Cache de opcodes:</strong> " . (function_exists('opcache_reset') ? 'Limpo' : 'N/A') . "</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🧪 Testes Adicionais:</h3>";
echo "<p>";
echo "<a href='teste_simples_filtro.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔍 Teste Isolado da Lógica</a>";
echo "<a href='debug_logica_filtro.php' style='background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🐛 Debug Detalhado</a>";
echo "<a href='teste_colaboradores_sem_pa.php' style='background: #17a2b8; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>📊 Teste Completo</a>";
echo "</p>";
echo "</div>";

echo "<h2>3. 📋 Checklist de Verificação</h2>\n";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>✅ Checklist:</h3>";
echo "<ol>";
echo "<li>☑️ Cache de arquivos limpo</li>";
echo "<li>☑️ Sessão PHP destruída</li>";
echo "<li>☑️ Headers no-cache configurados</li>";
echo "<li>☑️ Timestamp único gerado</li>";
echo "<li>☐ <strong>Teste da página principal</strong> (clique no link acima)</li>";
echo "<li>☐ <strong>Verificar se ALICE BEATRIZ não aparece</strong></li>";
echo "<li>☐ <strong>Verificar se outros usuários aparecem normalmente</strong></li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0;'>";
echo "<h3>🎯 Resultado Esperado:</h3>";
echo "<p><strong>A página deve mostrar:</strong></p>";
echo "<ul>";
echo "<li>✅ Colaboradores ativos da Intranet (com status de bloqueio)</li>";
echo "<li>✅ Colaboradores não encontrados na Intranet (como 'Sem PA Definido')</li>";
echo "<li>❌ NENHUM colaborador inativo da Intranet (incluindo ALICE BEATRIZ)</li>";
echo "</ul>";
echo "</div>";

echo "<p><em>Teste executado em: " . date('d/m/Y H:i:s') . " (Timestamp: $timestamp)</em></p>";

// JavaScript para forçar refresh sem cache
echo "<script>";
echo "function forceRefresh() {";
echo "  window.location.href = 'analise_colaboradores.php?aba=colaboradores&nocache=' + Date.now() + '&force_refresh=1';";
echo "}";
echo "</script>";

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<button onclick='forceRefresh()' style='background: #dc3545; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; font-weight: bold; cursor: pointer;'>";
echo "🚀 FORÇAR REFRESH COMPLETO";
echo "</button>";
echo "</div>";
?>
