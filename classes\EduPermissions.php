<?php
/**
 * Classe para gerenciar permissões específicas do sistema de Educação Corporativa
 * Não interfere com as permissões globais do sistema
 */
class EduPermissions {
    private $pdo;
    private $usuario_id;
    private $nivel_acesso;
    private $is_global_admin;
    
    public function __construct($pdo, $usuario_id) {
        $this->pdo = $pdo;
        $this->usuario_id = $usuario_id;
        $this->loadPermissions();
    }
    
    private function loadPermissions() {
        // Verificar se é administrador global (apenas para referência)
        $stmt = $this->pdo->prepare("
            SELECT nivel_acesso_id
            FROM usuarios
            WHERE id = ? AND ativo = TRUE
        ");
        $stmt->execute([$this->usuario_id]);
        $global_nivel = $stmt->fetchColumn();
        $this->is_global_admin = ($global_nivel == 1); // ID 1 = Administrador global

        // Buscar permissão específica do projeto de educação corporativa
        $stmt = $this->pdo->prepare("
            SELECT nivel_acesso
            FROM edu_permissoes_usuarios
            WHERE usuario_id = ? AND ativo = TRUE
        ");
        $stmt->execute([$this->usuario_id]);
        $this->nivel_acesso = $stmt->fetchColumn();

        // NOVA LÓGICA: Permissões específicas do projeto têm PRIORIDADE TOTAL
        // Se não tem permissão específica no projeto, NEGAR ACESSO
        // Não importa se é admin global - se não está na tabela edu_permissoes_usuarios, não tem acesso
    }
    
    public function isAdmin() {
        // APENAS permissão específica do projeto conta
        return $this->nivel_acesso === 'ADMIN';
    }

    public function isGestor() {
        // Gestor ou Admin do projeto (sem fallback global)
        return $this->nivel_acesso === 'GESTOR' || $this->isAdmin();
    }

    public function isComum() {
        // Apenas usuário comum específico do projeto
        return $this->nivel_acesso === 'COMUM' && !$this->isGestor() && !$this->isAdmin();
    }

    public function hasNoAccess() {
        // Se não tem permissão específica no projeto, não tem acesso
        return ($this->nivel_acesso === null || $this->nivel_acesso === false);
    }

    public function hasAccess() {
        // APENAS permissões específicas do projeto contam
        // Administradores globais SEM permissão específica = SEM ACESSO
        return ($this->nivel_acesso !== null && $this->nivel_acesso !== false);
    }
    
    public function getNivelAcesso() {
        return $this->nivel_acesso;
    }
    
    public function getUsuarioId() {
        return $this->usuario_id;
    }
    
    public function isGlobalAdmin() {
        return $this->is_global_admin;
    }
    
    /**
     * Verificar se usuário pode gerenciar permissões
     * Apenas administradores do projeto podem gerenciar
     */
    public function canManagePermissions() {
        return $this->isAdmin();
    }
    
    /**
     * Verificar se usuário pode gerenciar trilhas
     * Gestores e administradores podem gerenciar
     */
    public function canManageTrilhas() {
        return $this->isGestor() || $this->isAdmin();
    }
    
    /**
     * Verificar se usuário pode acessar relatórios avançados
     * Gestores e administradores podem acessar
     */
    public function canAccessAdvancedReports() {
        return $this->isGestor() || $this->isAdmin();
    }
    
    /**
     * Verificar se usuário pode importar dados
     * Todos os usuários com acesso podem importar
     */
    public function canImportData() {
        return $this->hasAccess();
    }

    /**
     * Verificar se usuário pode gerenciar importações
     * Apenas gestores e administradores podem gerenciar importações
     */
    public function canManageImports() {
        return $this->isGestor() || $this->isAdmin();
    }
    
    /**
     * Verificar se usuário pode visualizar análises
     * Todos os usuários com acesso podem visualizar
     */
    public function canViewAnalysis() {
        return $this->hasAccess();
    }
    
    /**
     * Obter texto descritivo do nível de acesso
     */
    public function getNivelTexto() {
        // Mostrar apenas o nível específico do projeto
        switch ($this->nivel_acesso) {
            case 'ADMIN':
                return 'Administrador';
            case 'GESTOR':
                return 'Gestor';
            case 'COMUM':
                return 'Usuário';
            default:
                // Indicar se é admin global mas sem acesso ao projeto
                if ($this->is_global_admin) {
                    return 'Sem Acesso (Admin Global)';
                }
                return 'Sem Acesso';
        }
    }
    
    /**
     * Verificar se usuário tem acesso ao projeto
     * APENAS permissões específicas do projeto são consideradas
     * Administradores globais SEM permissão específica = ACESSO NEGADO
     */
    public function checkProjectAccess() {
        if (!$this->hasAccess()) {
            header('Location: ../../access_denied_edu.php?projeto=educacao-corporativa');
            exit;
        }
    }
    
    /**
     * Verificar se usuário pode acessar página específica
     */
    public function checkPageAccess($required_level) {
        // Primeiro verificar se tem acesso ao projeto
        $this->checkProjectAccess();
        
        switch ($required_level) {
            case 'admin':
                if (!$this->isAdmin()) {
                    header('Location: ../../access_denied.php?projeto=educacao-corporativa&nivel=admin');
                    exit;
                }
                break;
            case 'gestor':
                if (!$this->isGestor()) {
                    header('Location: ../../access_denied.php?projeto=educacao-corporativa&nivel=gestor');
                    exit;
                }
                break;
            case 'comum':
                // Usuário comum já tem acesso se passou na verificação inicial
                break;
        }
    }
}
?>
