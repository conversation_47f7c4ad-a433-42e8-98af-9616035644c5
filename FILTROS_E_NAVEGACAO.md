# Sistema de Filtros e Navegação

## 🔍 **Funcionalidades de Filtros**

### **Seção de Filtros Completa:**

A página de gerenciamento agora possui uma seção de filtros avançada que permite:

#### **1. Filtro por Trilha**
- Dropdown com todas as trilhas disponíveis
- Opção "Todas as trilhas" para remover filtro
- Filtragem instantânea ao selecionar

#### **2. Filtro por Curso**
- Campo de texto para busca por nome do curso
- Busca em tempo real (keyup)
- Busca parcial (não precisa digitar o nome completo)

#### **3. Filtro por Status do Prazo**
- **"Todos os status"**: Mostra todos os cursos
- **"Com Prazo Personalizado"**: Apenas cursos com prazo personalizado ativo
- **"Prazo Padrão"**: Apenas cursos usando prazo padrão do relatório

#### **4. Ações Rápidas**
- **Expandir**: <PERSON><PERSON> todas as trilhas
- **Retrair**: <PERSON><PERSON> to<PERSON> as trilhas

### **Filtros Ativos**
- Badges visuais mostrando filtros aplicados
- Link "Limpar Filtros" para resetar tudo
- Feedback visual claro sobre o que está sendo filtrado

## 📁 **Funcionalidade de Retrair/Expandir**

### **Controle Individual:**
- **Clique no cabeçalho** da trilha para expandir/retrair
- **Ícone de seta** que rotaciona indicando o estado
- **Texto informativo** "Clique para expandir/retrair"

### **Controle Global:**
- **Botão "Expandir"**: Abre todas as trilhas de uma vez
- **Botão "Retrair"**: Fecha todas as trilhas de uma vez
- Útil para navegação rápida em listas grandes

### **Comportamento Inteligente:**
- Trilhas se expandem automaticamente quando há filtros ativos
- Estado visual claro (seta rotacionada quando retraída)
- Transições suaves com CSS

## 🎨 **Melhorias Visuais**

### **Cabeçalho das Trilhas:**
- **Contador de cursos** por trilha
- **Badge de cursos personalizados** (quantos têm prazo personalizado)
- **Hover effect** no cabeçalho clicável
- **Ícone de colapso** animado

### **Seção de Filtros:**
- **Design moderno** com gradiente sutil
- **Layout responsivo** em 4 colunas
- **Badges coloridos** para filtros ativos
- **Botões de ação** estilizados

### **Feedback Visual:**
- **Mensagem "Nenhum resultado"** quando filtros não retornam dados
- **Contadores dinâmicos** de trilhas e cursos
- **Estados visuais** claros para cada elemento

## 🔧 **Funcionalidades JavaScript**

### **Funções Principais:**

#### **`toggleTrilha(header)`**
- Alterna estado expandido/retraído de uma trilha específica
- Adiciona/remove classe CSS `trilha-collapsed`

#### **`expandAll()` / `collapseAll()`**
- Controle global de todas as trilhas
- Útil para navegação rápida

#### **`applyFilters()`**
- Aplica todos os filtros simultaneamente
- Conta resultados visíveis
- Expande trilhas automaticamente quando há filtros

#### **`updateFilterBadges()`**
- Atualiza badges de filtros ativos
- Mostra/esconde seção de filtros ativos

#### **`clearAllFilters()`**
- Remove todos os filtros
- Restaura visualização completa
- Limpa badges e mensagens

### **Recursos Avançados:**

#### **Filtros por URL**
- Suporte a parâmetros GET na URL
- Permite links diretos com filtros aplicados
- Exemplo: `?trilha=Trilha%20Técnica&status=ativo`

#### **Busca Inteligente**
- Busca case-insensitive
- Busca parcial (substring)
- Filtros combinados (AND logic)

#### **Performance Otimizada**
- Filtros aplicados via CSS (display: none/block)
- Sem recarregamento de página
- Transições suaves

## 📊 **Estatísticas Dinâmicas**

### **Contadores no Cabeçalho:**
- **Total de trilhas** disponíveis
- **Total de cursos** em todas as trilhas
- Atualizados automaticamente

### **Badges por Trilha:**
- **Número de cursos** na trilha
- **Cursos com prazo personalizado** (se houver)
- Cores diferenciadas para fácil identificação

### **Feedback de Resultados:**
- Mensagem quando nenhum resultado é encontrado
- Sugestão para ajustar filtros
- Botão para limpar filtros rapidamente

## 🎯 **Casos de Uso**

### **Gestor procurando curso específico:**
1. Digite nome do curso no filtro
2. Sistema mostra apenas trilhas com esse curso
3. Trilhas se expandem automaticamente

### **Administrador verificando prazos personalizados:**
1. Selecione "Com Prazo Personalizado" no filtro de status
2. Veja apenas cursos configurados
3. Use "Retrair" para visão compacta

### **Navegação rápida em muitas trilhas:**
1. Use "Retrair" para ver apenas cabeçalhos
2. Clique na trilha de interesse
3. Configure prazos conforme necessário

## 🚀 **Benefícios**

### **Para Usuários:**
- ✅ Navegação mais rápida e intuitiva
- ✅ Localização fácil de cursos específicos
- ✅ Visão organizada de grandes volumes de dados
- ✅ Controle total sobre a visualização

### **Para Administradores:**
- ✅ Gestão eficiente de muitas trilhas
- ✅ Identificação rápida de cursos configurados
- ✅ Interface profissional e moderna
- ✅ Produtividade aumentada

### **Para o Sistema:**
- ✅ Performance otimizada (sem recarregamentos)
- ✅ Interface responsiva
- ✅ Código JavaScript organizado
- ✅ Experiência de usuário superior

---

**Sistema de filtros e navegação implementado com sucesso!** 🎉
